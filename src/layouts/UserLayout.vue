<template>
<div class="container">
  <div class="left">
        <div class="title">
          <img src="../assets/newLogin/logo.png" alt="Image"/>
          <span>JUST MAKE</span>
        </div>
  </div>
  <div class="right">
    <lang-switcher class="select-header"/>
    <div class="main">
      <route-view></route-view>
    </div>
    <span class="footer-top">@2024 All Rights Reserved </span>
  </div>
</div>
</template>
<script>
import RouteView from './RouteView'
import LangSwitcher from '@/components/LangSwitcher'
export default {
  name: 'UserLayout',
  components: { RouteView, LangSwitcher},
  data () {
    return {
      local: {
        language: '',
        languageValue: ''
      },
      languages: [
        { key: 'zh_CN', name: '简体中文' },
        { key: 'zh_TW', name: '繁体中文' },
        { key: 'en_US', name: '英语' }
      ],
    }
  },
  mounted () {
  },
  methods: {

  },

}
</script>

<style lang="less" scoped>
.container{
  height: 100%;
  width: 100%;
  background: #E8F9F5;
  display: flex;
  flex: 1;
  justify-content: space-between;
  align-items: center;
  white-space: nowrap; /* 防止换行 */
}
.left,.right {
  height: 100%;
}

 .left{
   flex: 0.5;
   background: url('../assets/newLogin/bgImgLeft.png') no-repeat center center;
   background-size: cover; /* 使背景图像自适应铺满 */
   position: relative; /* 使子元素相对于该元素进行绝对定位 */
}
.title {
  position: absolute;
  top: 3%;
  left: 4%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.title img {
  margin-right: 8px; /* 可选，根据需要调整间距 */
  width: 36px;
  height: 40px;
}
.title span {
  display: inline-block;
  width: 140px;
  height: 33px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 600;
  font-size: 24px;
  color: #000000;
  line-height: 33px;
  text-align: left;
  font-style: normal;
  text-transform: uppercase;
}
.right{
  flex: 0.5;
  background: #FFFFFF;
  /* 你可以根据需要添加其他样式 */
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative; /* 使子元素相对于该元素进行绝对定位 */
}
.select-header{
  position: absolute;
  top: 3%;
  right: 4%;
  width: 106px;
  height: 32px;
  background: #FFFFFF;
  border-radius: 2px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #000000;
  line-height: 20px;
  text-align: center;
  font-style: normal;

}

.main{
  width: 48%; /* 根据需要设置百分比宽度 */
  height: 726px; /* 根据需要设置百分比高度 */
  min-width: 400px;
  min-height: 50%; /* 根据需要设置百分比高度 */
  justify-content: center;
  align-items: center;
  background: #FFFFFF;
}

.footer-top{
  position: absolute;
  top: 95.5%;
  left: 42%;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 14px;
  color: rgba(0,0,0,0.2);
  line-height: 20px;
  text-align: left;
  font-style: normal;
}
/* 高度小于等于800px时应用的样式（适配笔记本） */
@media (max-height: 800px) {
  .main{
    height: 626px; /* 根据需要设置百分比高度 */
    min-width: 350px;
    min-height: 50%; /* 根据需要设置百分比高度 */
  }
  .title {
    top: 1.5%;
  }
  .select-header{
    top: 1.5%;
  }
}
</style>
