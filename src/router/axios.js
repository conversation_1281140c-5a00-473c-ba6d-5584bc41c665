/* eslint-disable handle-callback-err */
/* eslint-disable no-use-before-define */
/* eslint-disable no-undef */
import Vue from 'vue'
import { Loading } from 'element-ui'
import { ACCESS_TOKEN, DEFAULT_TENANT_ID, DEFAULT_LOGIN_OUT_DATE, DEFAULT_LANGUAGE } from '@/store/mutation-types'
import { serialize } from '@/util/util'
import errorCode from './errorCode'
import notification from 'ant-design-vue/es/notification'
import api from '@/api/index'
import qs from 'qs'
import store from '@/store' // progress bar style
import axios from 'axios'
import _ from 'lodash'

axios.defaults.headers.post['Content-Type'] = 'application/json;charset=UTF-8' // 配置请求头
axios.defaults.baseURL = '/gateway' // 配置接口地址
// axios.defaults.baseURL = '' // 配置接口地址
axios.defaults.timeout = 300000
// loading对象
let loading

// 当前正在请求的数量
let needLoadingRequestCount = 0

// 显示loading
function showLoading(target) {
  // 后面这个判断很重要，因为关闭时加了抖动，此时loading对象可能还存在，
  // 但needLoadingRequestCount已经变成0.避免这种情况下会重新创建个loading
  if (needLoadingRequestCount === 0 && !loading) {
    loading = Loading.service({
      lock: true,
      background: 'rgba(255, 255, 255, 0.5)',
      target: target || 'body'
    })
  }
  needLoadingRequestCount++
}

// 隐藏loading
function hideLoading() {
  needLoadingRequestCount--
  needLoadingRequestCount = Math.max(needLoadingRequestCount, 0) // 做个保护
  if (needLoadingRequestCount === 0) {
    // 关闭loading
    toHideLoading()
  }
}

// 防抖：将 300ms 间隔内的关闭 loading 便合并为一次。防止连续请求时， loading闪烁的问题。
var toHideLoading = _.debounce(() => {
  if (loading != null) {
    loading.close()
    loading = null
  }
}, 300)
// 创建 axios 实例

// 返回其他状态吗
axios.defaults.validateStatus = function(status) {
  return status >= 200 && status <= 503 // 默认的
}
// 跨域请求，允许保存cookie
axios.defaults.withCredentials = true
// NProgress Configuration

const logOutN = 0
// HTTPrequest拦截
axios.interceptors.request.use(config => {

  const token = Vue.ls.get(ACCESS_TOKEN)
  config.headers['Accept-Language'] = Vue.ls.get(DEFAULT_LANGUAGE)
  const TENANT_ID = Vue.ls.get(DEFAULT_TENANT_ID) // getStore({ name: 'tenantId' })
  const isToken = (config.headers || {}).isToken === false
  // const token = store.getters.access_token
  if (token && !isToken) {
    config.headers['Authorization'] = 'Bearer ' + token // token
  }
  if (TENANT_ID) {
    config.headers['TENANT_ID'] = TENANT_ID // 租户ID
  }
  // headers中配置serialize为true开启序列化
  if (config.method === 'post' && config.headers.serialize) {
    // config.headers['Content-Type'] = 'application/x-www-form-urlencoded;charset=UTF-8'
    config.data = serialize(config.data)
    delete config.data.serialize
  }

  if (config.method === 'get') {
    config.paramsSerializer = function(params) {
      return qs.stringify(params, { arrayFormat: 'repeat' })
    }
  } else {
    // if (config.headers.showLoading !== false && config.url.indexOf('mes/basicData/qcItm') == -1 && config.url.indexOf('mes/basicData/prd') == -1 && config.url.indexOf('/mes/basicData/mfzc') == -1 &&
    //   config.url.indexOf('/mes/basicData/zc') == -1 && config.url.indexOf('mes-qc-prj/getPrjPrdt') == -1 && config.url.indexOf('mes/mespro/prdZFQty') == -1) {
    //   showLoading(config.headers.loadingTarget)
    // }
  }
  if (config.url.startsWith("/arc/api") || config.url.startsWith("/api/reporting") || config.url.startsWith("/app")) {
    config.baseURL = ''
  }
  return config
}, error => {
  // 判断当前请求是否设置了不显示Loading
  if (config.headers.showLoading !== false) {
    hideLoading()
  }
  notification.error({
    message: '错误信息',
    description: '服务器连接超时'
  })
  return Promise.reject(error)
})

// HTTPresponse拦截
axios.interceptors.response.use(res => {
  // 判断当前请求是否设置了不显示Loading（不显示自然无需隐藏）
  if (res.config.headers.showLoading !== false) {
    hideLoading()
  }

  const status = Number(res.status) || 200
  let message = res.data.msg || errorCode[status] || errorCode['default']

  if (status === 401) {
    store.dispatch('Logout').then(() => {
      setTimeout(() => {
        window.location.reload()
      }, 1500)
    })
    return Promise.reject(message)
  }
  // if (status === 200 && (res.data.code === 0 && (res.data.msg != null && res.data.msg != 'success'))) {
  //   message = res.data.data || message
  //   return Promise.reject(message)
  // }
  if (status === 200 && (res.data.code === 1 || res.data.msg === 'fail')) {
    message = res.data.data || message
    return Promise.reject(message)
  }
  if (status === 503 && res.data.code === 1) {
    message = res.data.data || message
    return Promise.reject('服务器未启动，请稍后再试！')
  }
  if (status === 500 && res.data.code === 1) {
    message = res.data.data || message
    return Promise.reject(message)
  }
  if (status == 479) {
    return Promise.reject(res.data.data)
  }
  if (status !== 200) {
    message = errorCode[status] || res.data.msg || errorCode['default']
    message = message === 'fail' ? res.data.data : message
    if (message != null && message != '' && message != 'undefined' && message != undefined) {
      return Promise.reject(message)
    }
  }


  // if (res.data instanceof Blob) {
  //   const link = document.createElement('a')
  //   link.href = window.URL.createObjectURL(res.data)
  //   const fileName = res.headers["content-disposition"].split('=')[1];
  //   link.download = decodeURI(fileName)
  //   document.body.appendChild(link)
  //   link.click()
  //   window.setTimeout(function () {
  //     window.URL.revokeObjectURL(res.data)
  //     document.body.removeChild(link)
  //   }, 0)
  //   return
  // }


  return res.data
}, error => {
  // 判断当前请求是否设置了不显示Loading（不显示自然无需隐藏）
  if (error.config.headers.showLoading !== false) {
    hideLoading()
  }

  return Promise.reject(new Error(error))
})

export default axios
