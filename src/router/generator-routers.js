// // eslint-disable-next-line
// import * as loginService from '@/api/login'
// // eslint-disable-next-line
// // eslint-disable-next-line
// import { UserLayout, BasicLayout, RouteView, BlankLayout, PageView } from '@/layouts'
// import { bxAnaalyse } from '@/core/icons'

// // 前端路由表
// const constantRouterComponents = {
//   // 基础页面 layout 必须引入
//   BasicLayout: BasicLayout,
//   BlankLayout: BlankLayout,
//   PageView: PageView,
//   '用户管理': () => import(/* webpackChunkName: "error" */ '@/views/sys/user/index'),
//   // eslint-disable-next-line no-dupe-keys
//   // 'PageView': () => import(/* webpackChunkName: "error" */ '@/views/barcode/propertySettings/index'),
//   // 基础页面 layout 必须引入
//   '403': () => import(/* webpackChunkName: "error" */ '@/views/exception/403'),
//   '404': () => import(/* webpackChunkName: "error" */ '@/views/exception/404'),
//   '500': () => import(/* webpackChunkName: "error" */ '@/views/exception/500')
// }

// // 前端未找到页面路由（固定不用改）
// const notFoundRouter = {
//   path: '*',
//   redirect: '/404',
//   hidden: true
// }

// // // 根级菜单
// const rootRouter = {
//   path: '/',
//   name: 'index',
//   component: BasicLayout,
//   meta: { title: 'menu.index' },
//   redirect: '/dashboard/workplace',
//   children: [
//     {
//       path: '/dashboard',
//       name: 'dashboard',
//       redirect: '/dashboard/workplace',
//       component: RouteView,
//       meta: { title: 'menu.dashboard', keepAlive: true, icon: bxAnaalyse },
//       children: [
//         {
//           path: '/dashboard/analysis',
//           name: 'Analysis',
//           component: () => import('@/views/dashboard/Analysis'),
//           meta: { title: 'menu.analysis', keepAlive: false, permission: ['dashboard'] }
//         },
//         {
//           path: '/dashboard/workplace',
//           name: 'Workplace',
//           component: () => import('@/views/dashboard/Workplace'),
//           meta: { title: 'menu.workplace', keepAlive: true, permission: ['dashboard'] }
//         }
//       ]
//     }, {
//       path: '/account',
//       component: RouteView,
//       redirect: '/account/center',
//       name: 'account',
//       meta: { title: 'menu.account', icon: 'user', keepAlive: true, permission: ['user'] },
//       children: [
//         {
//           path: '/account/center',
//           name: 'center',
//           component: () => import('@/views/account/center/Index'),
//           meta: { title: 'menu.center', keepAlive: true, permission: ['user'] }
//         },
//         {
//           path: '/account/settings',
//           name: 'settings',
//           component: () => import('@/views/account/settings/Index'),
//           meta: { title: 'menu.settings', hideHeader: true, permission: ['user'] },
//           redirect: '/account/settings/base',
//           hideChildrenInMenu: true,
//           children: [
//             {
//               path: '/account/settings/base',
//               name: 'BaseSettings',
//               component: () => import('@/views/account/settings/BaseSetting'),
//               meta: { title: '基本设置', permission: ['user'] }
//             },
//             {
//               path: '/account/settings/security',
//               name: 'SecuritySettings',
//               component: () => import('@/views/account/settings/Security'),
//               meta: { title: '安全设置', keepAlive: true, permission: ['user'] }
//             },
//             {
//               path: '/account/settings/custom',
//               name: 'CustomSettings',
//               component: () => import('@/views/account/settings/Custom'),
//               meta: { title: '个性化设置', keepAlive: true, permission: ['user'] }
//             },
//             {
//               path: '/account/settings/binding',
//               name: 'BindingSettings',
//               component: () => import('@/views/account/settings/Binding'),
//               meta: { title: '账户绑定', keepAlive: true, permission: ['user'] }
//             },
//             {
//               path: '/account/settings/notification',
//               name: 'NotificationSettings',
//               component: () => import('@/views/account/settings/Notification'),
//               meta: { title: '新消息通知', keepAlive: true, permission: ['user'] }
//             }
//           ]
//         }
//       ]
//     }
//   ]
// }

// /**
//  * 动态生成菜单
//  * @param token
//  * @returns {Promise<Router>}
//  */
// export const generatorDynamicRouter = token => {
//   return new Promise((resolve, reject) => {
//     loginService
//       .getCurrentUserNav()
//       .then(res => {
//         const { data } = res
//         const menuNav = []
//         // 后端数据, 根级树数组,  根级 PID
//         const routers = generator(data)
//         routers.map(item => {
//           rootRouter.children.push(item)
//         })
//         menuNav.push(rootRouter)
//         menuNav.push(notFoundRouter)

//         resolve(menuNav)
//       })
//       .catch(err => {
//         reject(err)
//       })
//   })
// }

// /**
//  * 格式化树形结构数据 生成 vue-router 层级路由表
//  *
//  * @param routerMap
//  * @param parent
//  * @returns {*}
//  */
// export const generator = (routerMap, parent) => {
//   return routerMap.map(item => {
//     const { show, hideChildren, hiddenHeaderContent, target } = item.meta || {}
//     const currentRouter = {
//       // 如果路由设置了 path，则作为默认 path，否则 路由地址 动态拼接生成如 /dashboard/workplace
//       // path: item.path || `${(parent && parent.path) || ''}/`,
//       path: item.path,

//       // 路由名称，建议唯一
//       name: item.permission || item.name || '',
//       // 该路由对应页面的 组件 :方案1
//       // component: constantRouterComponents[item.component || item.key],
//       // 该路由对应页面的 组件 :方案2 (动态加载)
//       // component: constantRouterComponents[item.component] || (() => import(`@/views${item.path}`)),
//       component: constantRouterComponents[item.name],

//       // meta: 页面标题, 菜单图标, 页面权限(供指令权限用，可去掉)
//       meta: {
//         title: 'menu.' + item.permission,
//         icon: item.icon || undefined,
//         hiddenHeaderContent: hiddenHeaderContent,
//         target: target,
//         permission: [item.permission],
//         keepAlive: Number(item.keepAlive) === 0
//       }
//     }

//     if (currentRouter.path.includes('http') || currentRouter.path.includes('https')) {
//       currentRouter.meta.target = '_blank'
//     }
//     // if (currentRouter.children) {
//     //   currentRouter.component = null
//     // }
//     if (item.children.length > 0) {
//       // 有下级菜单 并没有配置 布局， 默认 PageView
//       currentRouter.component = PageView
//     }
//     // 是否设置了隐藏菜单
//     if (show === false) {
//       currentRouter.hidden = true
//     }
//     // 是否设置了隐藏子菜单
//     if (hideChildren) {
//       currentRouter.hideChildrenInMenu = true
//     }
//     // 为了防止出现后端返回结果不规范，处理有可能出现拼接出两个 反斜杠
//     if (!currentRouter.path.startsWith('http')) {
//       currentRouter.path = currentRouter.path.replace('//', '/')
//     }
//     // 重定向
//     item.redirect && (currentRouter.redirect = item.redirect)
//     // 是否有子菜单，并递归处理
//     if (item.children && item.children.length > 0) {
//       // Recursion
//       currentRouter.children = generator(item.children, currentRouter)
//     }
//     return currentRouter
//   })
// }

// /**
//  * 数组转树形结构
//  * @param list 源数组
//  * @param tree 树
//  * @param parentId 父ID
//  */
// // const listToTree = (list, tree, parentId) => {
// //   list.forEach(item => {
// //     // 判断是否为父级菜单
// //     if (item.parentId === parentId) {
// //       const child = {
// //         ...item,
// //         key: item.key || item.name,
// //         children: []
// //       }
// //       // 迭代 list， 找到当前菜单相符合的所有子菜单
// //       listToTree(list, child.children, item.id)
// //       // 删掉不存在 children 值的属性
// //       if (child.children.length <= 0) {
// //         delete child.children
// //       }
// //       // 加入到树中
// //       tree.push(child)
// //     }
// //   })
// // }
