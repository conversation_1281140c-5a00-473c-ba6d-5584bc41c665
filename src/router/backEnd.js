import router from '@/router/index'
import store from '@/store'
import Vue from 'vue'
import { ACCESS_TOKEN } from '@/store/mutation-types'
import { dynamicRoutes,staticRoutes } from '@/config/router.config'
import { loadOtherRoutes } from '@/utils/FormatOtherRoutes'
import { list as getOtherSysInfo } from '@/api/system/iframe'
import { getMenuByRole } from '@/api/login'
import { BasicLayout } from '@/layouts'


// 加载 views 目录下所有 .vue 文件（递归子目录）
const viewsModules = require.context('../views', true, /\.(vue)$/)

function loadModules(modulesContext) {
  const modules = {}
  modulesContext.keys().forEach(key => {
    // 去掉开头的 '.'
    const name = key.replace(/^\.?/, '')
    modules[name] = modulesContext(key).default || modulesContext(key)
  })
  return modules
}
const dynamicViewsModules = {
  ...loadModules(viewsModules)
}

/**
 * 后端控制路由：入口
 */
export async function initBackEndControlRoutes() {
  // 无 token 停止执行下一步（在外部初始化用户信息）
  if (!Vue.ls.get(ACCESS_TOKEN)) return false;
  // 获取路由菜单数据
  const res = await getBackEndControlRoutes();
  // 无登录权限时，添加判断
  if ((res.data || []).length <= 0) return Promise.resolve(true);
  // 处理路由（component）
  dynamicRoutes[0].children = [...staticRoutes, ...buildRoute(res.data)];
  // 追加第三方软件菜单
  dynamicRoutes[0].children.push(...await setOtherRoutes())
  // 注册路由
  await setAddRoute()
  // 设置路由到 vuex
  store.commit("SET_ROUTERS", dynamicRoutes)
}

function setAddRoute(){
  dynamicRoutes[0].children.forEach(item =>{
    router.addRoute("index",item); // 注册到根路由下
  })
}

/**
 * 后端菜单数据 转 前端路由
 */
function buildRoute(menuData) {
  function deepConvert(list = []) {
    return list
      .filter(item => !!item.path)
      .map(item => {
        const hasChildren = item.children && item.children.length > 0
        const isIframe = item.path?.startsWith('http')
        let component = null

        if (isIframe) {
          component = () => import('@/views/iframe/view.vue');
          item.path = '/iframe/' + window.btoa(item.path);
        }else {
          // 由于嵌套路由使用了绝对路径，导致不继承根路由的布局，所以需在每个一级路由声明布局
          component = hasChildren ? BasicLayout : dynamicImport(dynamicViewsModules, item.path.split('/:')[0])
        }
        if(!component) return null

        const route = {
          path: item.path,
          name: item.permission,
          component,
          meta: {
            title: item.name,
            icon: item.icon,
            isIframe,
            isHide: item.hidden === '1',
            permission: item.permission ? [item.permission] : [],
          }
        }

        if (hasChildren && !isIframe) {
          route.children = deepConvert(item.children)
          // route.redirect = route.children[0]?.path
        }

        return route
      }).filter(Boolean) // 剔除空项
  }

  return deepConvert(menuData)
}


/**
 * 获取第三方软件菜单
 */
async function setOtherRoutes(){
  const route = []
  const otherSys= await getOtherSysInfo()
  const keys = otherSys.data.map(item=> item.code)
  if(keys.length > 0){
    const { success, routes, error } = await loadOtherRoutes(keys)
    if (success) {
      route.push(...routes)
    } else {
      console.error(`第三方软件菜单获取失败:`,error)
    }
  }
  return route
}

/**
 * 请求后端路由菜单接口
 */
function getBackEndControlRoutes() {
  return getMenuByRole();
}

/**
 * 后端路由 component 转换函数
 */
function dynamicImport(dynamicViewsModules, component) {
  const keys = Object.keys(dynamicViewsModules);

  const matchKeys = keys.filter((key) => {
    return key.startsWith(`${component}.vue`);
  });
  if (matchKeys?.length === 1) {
    const matchKey = matchKeys[0];
    return dynamicViewsModules[matchKey];
  }
  if (matchKeys?.length > 1) {
    return false;
  }
  console.warn("无匹配路由：", component)
}

