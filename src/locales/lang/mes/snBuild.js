export const snBuildCH = {
  createSN: '生成序列号',
  removeSN: '删除序列号',
  setting: '设置',
  staDd: '起始时间',
  endDd: '截止时间',
  moNo: '单据代号',
  moDd: '单据日期',
  mrpNo: '产品代号',
  prdNo: '产品代号',
  pgNo: '派工单号',
  qty: '数量',
  poOk: '已生成序列号',
  cid: '项次',
  prdSn: '序列号',
  printId: '打印标志',
  printDd: '打印日期',
  initSn: '初始流水号',
  keepNum: '保留数',
  param: '参数值',
  dispatch: '取值派工单',
  productQty: '生成数量',
  producedQty: '已生成数量',
  name: '名称',
  spc: '货品规格',
  needDd: '需求日期',
  cusNo: '客户代号',
  soNo: '受订单号',
  supPrdNo: '对方货号',
  createBy: '创建者',
  createTime: '创建时间'
}

export const snBuildUS = {
  createSN: 'Generate serial number',
  removeSN: 'Delete serial number',
  setting: 'Setting',
  staDd: 'Start time',
  endDd: 'End time',
  moNo: 'Document code',
  moDd: 'Document date',
  mrpNo: 'Product code',
  prdNo: 'Product code',
  pgNo: 'Dispatch code',
  qty: 'Quantity',
  poOk: 'Generated serial number',
  cid: 'Item number',
  prdSn: 'Serial number',
  printId: 'Print mark',
  printDd: 'Print date',
  initSn: 'Initial serial number',
  keepNum: 'Retain number',
  param: 'Parameter value',
  dispatch: 'Value dispatch order',
  productQty: 'Production quantity',
  producedQty: 'Produced quantity',
  name: 'Name',
  sPC: 'Product Specifications',
  needD: 'Demand Date',
  cusNo: 'Customer Code',
  soNo: 'Order number received',
  supPrdNo: 'Opposite item number',
  createBy: 'Creator',
  createTime: 'Create Time'
}

export const snBuildTW = {
  createSN: '生成序列號',
  removeSN: '刪除序列號',
  setting: '設置',
  staDd: '起始時間',
  endDd: '截止時間',
  moNo: '單據代號',
  moDd: '單據日期',
  mrpNo: '產品代號',
  prdNo: '產品代號',
  pgNo: '派工單號',
  qty: '數量',
  poOk: '已生成序列號',
  cid: '項次',
  prdSn: '序列號',
  printId: '打印標誌',
  printDd: '打印日期',
  initSn: '初始流水號',
  keepNum: '保留數',
  param: '參數值',
  dispatch: '取值派工單',
  name: '名稱',
  spc: '貨品規格',
  needDd: '需求日期',
  cusNo: '客戶代號',
  soNo: '受訂單號',
  supPrdNo: '對方貨號',
  createBy: '創建者',
  createTime: '創建時間',
  productQty: '生成數量',
  producedQty: '已生成數量',
}
