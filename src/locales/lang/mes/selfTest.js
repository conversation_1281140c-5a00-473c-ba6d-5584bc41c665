export const selfTestCH = {
  staDd: '开始日期',
  endDd: '结束日期',
  zjNo: '检验单号',
  zcNo: '工序',
  tzNo: '工序单号',
  moNo: '工单号',
  prdNo: '品号',
  prdName: '品名',
  salNo: '制单人',
  chkKnd: '复检',
  sys: '实验室检验',
  zjDd: '检验日期',
  qty: '检验数量',
  qtyOk: '合格量',
  qtyLost: '不合格量',
  bilNo: '送检单号',
  usr: '制单人',
  rem: '备注',

  qcTypeName: '检验方式',
  cusNo: '客户厂商',
  dep: '部门',
  bilType: '单据类别',
  qcId: '检验结果',
  prdMark: '特征',
  unitName: '单位',
  whName: '仓库',
  batNo: '批号',
  pgNo: '派工单号',
  qtyRtn: '已转合格量',
  lsNo: '不合格评审单',
  boxNo: '周转箱号',
  qcItm: '检验项目',
  qcName: '检验名称',
  qcSpc: '检验标准',
  stdValue: '标准值',
  stdValueId: '逻辑符',
  val1: '偏差值',
  acMin: '允收下限',
  acMax: '允收上限',
  qcType: '检验类型',
  qcTool: '测量工具',
  spcNo: '原因代号',
  itm: '项次',
  qcRec: '检验值',
  chkId: '合格判定',
  spcName: '不合格原因',

}

export const selfTestUS = {
  staDd: 'Start Date',
  endDd: 'End Date',
  zjNo: 'Inspection Order Number',
  zcNo: 'Process',
  tzNo: 'Process Order Number',
  moNo: 'Work Order Number',
  prdNo: 'Product Number',
  prdName: 'Product Name',
  salNo: 'Maker',
  chkKnd: 'Re examination',
  sys: 'Laboratory testing',
  zjDd: 'Inspection date',
  qty: 'Inspection quantity',
  qyOK: 'Qualified Quantity',
  qtyList: 'Unqualified quantity',
  bilNo: 'Inspection Form Number',
  usr: 'Maker',
  rem: 'Remarks',

  qcTypeName: 'Inspection Method',
  cusNo: 'Customer Manufacturer',
  dep: 'Department',
  bilType: 'Document Category',
  qcId: 'Inspection result',
  prdMark: 'Features',
  unitName: 'Unit',
  whName: 'Warehouse',
  batNo: 'Batch number',
  pgNo: 'Dispatch Order Number',
  qtyRTn: 'Transferred qualified quantity',
  lsNo: 'Non conformance Review Form',
  boxNo: 'Turnover box number',
  qcItm: 'Inspection items',
  qcName: 'Inspection Name',
  qcSpc: 'Inspection Standards',
  sTDValue: 'Standard Value',
  sTDValueId: 'Logical Symbol',
  val1: 'Deviation value',
  acMin: 'Acceptable lower limit',
  acMax: 'Acceptable upper limit',
  qcType: 'Inspection Type',
  qcTool: 'Measurement Tool',
  spcNo: 'Reason code',
  itm: 'Item number',
  qcRec: 'Inspection value',
  chkId: 'Qualified judgment',
  spcName: 'Reason for Non conformance',

}

export const selfTestTW = {
  staDd: '開始日期',
  endDd: '結束日期',
  zjNo: '檢驗單號',
  zcNo: '工序',
  tzNo: '工序單號',
  moNo: '工單號',
  prdNo: '品號',
  prdName: '品名',
  salNo: '制單人',
  chkKnd: '複檢：',
  sys: '實驗室檢驗：',
  zjDd: '檢驗日期',
  qty: '檢驗數量',
  qtyOk: '合格量',
  qtyLost: '不合格量：',
  bilNo: '送檢單號',
  usr: '制單人',
  rem: '備註',
  qcTypeName: '檢驗管道',
  cusNo: '客戶廠商',
  dep: '部門',
  bilType: '單據類別',
  qcId: '檢驗結果',
  prdMark: '特徵',
  unitName: '組織',
  whName: '倉庫',
  batNo: '批號',
  pgNo: '派工單號',
  qtyRtn: '已轉合格量',
  lsNo: '不合格評審單',
  boxNo: '周轉箱號',
  qcItm: '檢驗項目',
  qcName: '檢驗名稱',
  qcSpc: '檢驗標準',
  stdValue: '標準值',
  stdValueId: '邏輯符',
  val1: '偏差值',
  acMin: '允收下限',
  acMax: '允收上限',
  qcType: '檢驗類型',
  qcTool: '量測工具',
  spcNo: '原因代號',
  itm: '項次',
  qcRec: '檢驗值',
  chkId: '合格判定',
  spcName: '不合格原因',
}
