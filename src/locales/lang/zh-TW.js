import zhTW from 'ant-design-vue/es/locale-provider/zh_TW'
import zhLocale from 'element-ui/lib/locale/lang/zh-TW'
import tableZHTW from 'vxe-table/lib/locale/lang/zh-TW'
import { indxTW } from './basic/indx'
import { mywhTW } from './basic/mywh'
import { containsetTW } from './basic/containset'
import { prdtTW } from './basic/prdt'
import { salmTW } from './salm/index'
import { ipcbasicTW } from './mes/ipcbasic'
import { zcnoMesTW } from './mes/zcno'
import { personalCenterTW } from './perCenter/personalCenter'
import { dispatchworkerTW } from './mes/dispatchworker'
import { boxOperationTW } from './mes/boxOperation'
import { turnOverBoxTW } from './mes/turnOverBox'
import { deliveryReceiptTW } from './mes/deliveryReceipt'
import { abnormalManageTW } from './mes/abnormalManage'
import { abnormalWorkTW } from './mes/abnormalWork'
import { inspectionFormTW } from './mes/inspectionForm'
import { firstInspectDataTW } from './mes/firstInspectData'
import { selfTestTW } from './mes/selfTest'
import { patrolinSpectionTW } from './mes/patrolinSpection'
import { completInspectTW } from './mes/completInspect'
import { completionTwoTW } from './mes/completionTwo'
import { completeInspecDataTW } from './mes/completeInspecData'
import { entrustWorkerTW } from './mes/entrustWorker'
import { entrustTwoTW } from './mes/entrustTwo'
import { entrustInspecDataTW } from './mes/entrustInspecData'
import { nonConformTW } from './mes/nonConform'
import { qualitySureTW } from './mes/qualitySure'
import { hpgxdjbTW } from './mes/hpgxdjb'
import { inspectitemsTW } from './mes/inspectitems'
import { reasonSetTW } from './mes/reasonSet'
import { toolsettingTW } from './mes/toolsetting'
import { productKeySettingTW } from './mes/productKeySetting'
import { workStepTW } from './mes/workStep'
import { mesSnParamTW } from './mes/mesSnParam'
import { mesSnRuleTW } from './mes/mesSnRule'
import { mesStopSpcTW } from './mes/mesStopSpc'
import { mesMoLineTW } from './mes/mesMoLine'
import { mesMdRefTW } from './mes/mesMdRef'
import { finishedPlanTW } from './mes/finishedPlan'
import { processPlanTW } from './mes/processPlan'
import { categoryTW } from './device/basicEquipInfo/category'
import { equipmentModelTW } from './device/basicEquipInfo/equipmentModel'
import { deviceLocationTW } from './device/basicEquipInfo/deviceLocation'
import { deviceInformTW } from './device/basicEquipInfo/deviceInform'
import { defendSetTW } from './device/basicmaintenance/defendSet'
import { maintenanceItemsTW } from './device/basicmaintenance/maintenanceItems'
import { mainteMethodsTW } from './device/basicmaintenance/mainteMethods'
import { maintenanceAreaTW } from './device/basicmaintenance/maintenanceArea'
import { mainStandardsTW } from './device/basicmaintenance/mainStandards'
import { classSetTW } from './device/basicmaintenance/classSet'
import { defendRepairTW } from './device/basicmaintenance/defendRepair'
import { faultLevelTW } from './device/repairBasicData/faultLevel'
import { urgencyTW } from './device/repairBasicData/urgency'
import { faultCategoryTW } from './device/repairBasicData/faultCategory'
import { mainPlanTW } from './device/maintenancePlan/mainPlan'
import { deviceMaintainTW } from './device/equipmentBy/deviceMaintain'
import { routineCheckTW } from './device/equipmentBy/routineCheck'
import { causemalFunctionTW } from './device/repairBasicData/causemalFunction'
import { sbbyRecordFormTW } from './device/sbbyrecordForm/sbbyRecordForm'
import { socialTW } from './system/social'
import { curTW } from './basic/cur'
import { bankTW } from './basic/bank'
import { baccTW } from './basic/bacc'
import { areaTW } from './basic/area'
import { custTW } from './basic/cust'
import { supTW } from './basic/sup'
import { depTW } from './basic/deproset'
import { zcnoTW, zcnoUS } from './basic/zcno'
import { dataTW } from './sysconfig/dataSource'
import { codeTW } from './sysconfig/code'
import { mailTW } from './sysconfig/mail'
import { appTW } from './sysconfig/app'
import { profigTW } from './sysconfig/profig'
import { tokenTW } from './system/token'
import { propertySettingsTW } from './barcode/propertySettings'
import { managementTW } from './mes/management'
import { equipmentTW } from './mes/equipment'
import { propertySetTW } from './mes/propertySet'
import { separateCodingTW } from './barcode/separateCoding'
import { stationcodeTW } from './mes/stationcode'
import { mfbarrmvruleTW } from './barcode/mfbarrmvrule'
import { prdtBarcodeBoxTW } from './barcode/prdtBarcodeBox'
import { paramterSettingTW } from './barcode/paramterSetting'
import { parametersTW } from './barcode/parameters'
import { list_standardTW } from './barcode/list_standard'
import { heat_treatmentTW } from './barcode/heat_treatment'
import { Furnace_NoTW } from './barcode/Furnace_No'
import { attribute_definitionTW } from './barcode/attribute_definition'
import { txTW } from './sysdaemon/tx'
import { exeTW } from './sysdaemon/exe'
import { traceTW } from './sysdaemon/trace'
import { inventoryTW } from './report/inventory'
import { prdTW } from './report/prd'
import { uninvoicedTW } from './report/uninvoiced'
import { salesTW } from './report/sales'
import { logistTW } from './report/logist'
import { gradeTW } from './report/grade'
import { subTW } from './report/sub'
import { work_detailsTW } from './report/work_details'
import { informationTW } from './report/information'
import { fileTW } from './sysconfig/file'
import { workTW } from './mes/work'
import { qualityTW } from './mes/quality'
import { processTW } from './mes/process'
import { inspecTW } from './mes/inspection'
import { pickedTW } from './mes/picked'
import { entrustmentTW } from './srm/entrustment'
import { prdiversionTW } from './srm/prdiversion'
import { inquiryofferTW } from './srm/inquiryoffer'
import { contractinquiryTW } from './srm/contractinquiry'
// import { stagepayTW } from './srm/stagepay'
import { newpucontractTW } from './srm/newpucontract'
import { inquirycompriTW } from './srm/inquirycompri'
import { submissionTW } from './srm/submission'
import { purchaseTW } from './srm/purchase'
import { deliveryTW } from './srm/delivery'
import { invoiceTW } from './srm/invoice'
import { zlsqTW } from './srm/zlsq'
import { datadownTW } from './srm/datadown'
import { timeszTW } from './srm/timesz'
import { sourceTW } from './develop/source'
import { codeIndexTW } from './develop/codeIndex'
import { baseTW } from './basic/pdmcenter/base'
import { capacityTW } from './basic/pdmcenter/capacity'
import { calendarTW } from './basic/pdmcenter/calendar'
import { workClassTW } from './basic/workClass'
import { creationTW } from './file/creation'
import { versionTW } from './file/version'
import { authorityTW } from './file/authority'
import { identifierTW } from './file/identifier'
import { grantTW } from './file/grant'
import { voidTW } from './file/void'
import { changeTW } from './file/change'
import { launchTW } from './process/launch'
import { completedTW } from './process/completed'
import { dealtTW } from './process/dealt'
import { proverTW } from './process/prover'
import { classificationTW } from './process/classification'
import { examplesTW } from './process/examples'
import { formTypesTW } from './process/formTypes'
import { formEditTW } from './process/formEdit'
import { designTW } from './process/design'
import { priorityTW } from './aps/priority'
import { mergeTW } from './aps/merge'
import { modeSetTW } from './aps/modeSet'
import { schedulingTW } from './aps/scheduling'
import { apsworkcentermeTW } from './aps/apsworkcenterme'
import { messageTW } from './message/message'
import { toolbarTW } from './toolbar/index'
import { permTW } from './sysdaemon/docpermission'
import { registerTW } from './system/register';
import { reFormTW } from './report/forms';
import { barcodeMenuTW } from './barcode/menu';
import { fpTW } from './preventStupidity/index'
import { inspTW } from './barcode/inspectionbar'
import { reWorkTW } from './barcode/szcpgboss'
import { devStaTW } from './mes/devicemanset'
import { faultCauseTW } from './mes/faultcause'
import { devStaManagTW } from './mes/deviceStatusMan'
import { workGroupTW } from './barcode/workGroup'
import { arcCH, arcTW } from './custommana/arc'
import { hpgxdjbUS } from "@/locales/lang/mes/hpgxdjb";
import {dashboardTW} from '@/locales/lang/dashboard'
import { prdStepCH, prdStepTW } from '@/locales/lang/mes/prdStep'
import { ivxTW } from './custommana/ivx'
import { snBuildTW } from '@/locales/lang/mes/snBuild'
import { boxTW } from '@/locales/lang/mes/box'
import { stepStaTW } from '@/locales/lang/mes/mesStepStation'
import { zcStaTW } from '@/locales/lang/mes/mesZcStation'
import {cxztTW} from '@/locales/lang/mes/cxzt'
import { zcStaCH } from '@/locales/lang/mes/mesZcStation'
import { sopTW } from '@/locales/lang/mes/sop'
import { zxTW } from '@/locales/lang/mes/mesZx'
import { boardTW } from '@/locales/lang/mes/board'
import { tempSettingTW } from '@/locales/lang/custommana/templateSettings'
import { boardWorkTW } from '@/locales/lang/mes/boardWork'
import { scanPrintTW } from '@/locales/lang/barcode/scanPrint'
import { firstCheckTW } from '@/locales/lang/mes/mesFirstCheck'
import { firstCheckaaTW } from '@/locales/lang/mes/mesFirstCheckaa'
import { splitboardCH, splitboardTW } from '@/locales/lang/mes/splitboard'
import { splitboxTW } from '@/locales/lang/mes/splitbox'
import { iframeusrTW } from '@/locales/lang/system/iframeusr'
import { iframeTW } from '@/locales/lang/system/iframe'
export default {
  ...zhLocale,
  ...tableZHTW,
  antLocale: zhTW,
  public: {
    spc:'规格',
    copies:'打印份數',
    chkMan: '終審人',
    clsDate: '終審日期',
    synchro: '同步',
    value: '代碼',
    title: '名稱',
    sendMsg: '新消息',
    sendMsgs: '有新消息！',
    filter: '過濾',
    setup: '設定',
    print: '打印',
    export: '匯出',
    import: '匯入',
    localImport: '本地導入',
    cloudImport: '雲端導入',
    addTo: '添加',
    renovate: '重繪',
    waitDown: '正在下載中！',
    downSuccess: '下載成功！',
    login: {
      message: '歡迎',
      description: '歡迎回來',
      remember: '記住我',
      title: '賬號密碼登入',
      forgotPassword: '忘記密碼？ 請點我',
      otherLoginMethods: '其他登入管道',
      ScanCodeLogin: '掃碼登入',
      accountTips: '帳戶代號/分公司代號/公司名稱',
      placeholder: {
        account: '帳號登入',
        accountSet: '賬套選擇',
        passwordS: '密碼',
        tenantId: '請選擇賬套',
        username: '請輸入用戶名',
        password: '密碼',
        logOn: '登入'
      }
    },
    errorMessage: '服務器出現錯誤，請稍後再試',
    message: '信息',
    query: '查詢',
    reset: '重置',
    add: '新增',
    edit: '編輯',
    delete: '刪除',
    snm: '貨品簡稱',
    sure: '確定',
    update: '修改',
    cancel: '取消',
    'to-update': '更新',
    save: '保存',
    return: '返回',
    preview: '預覽',
    power: '選單許可權',
    more: '更多',
    check: '檢視',
    Detailed: '明細',
    action: '操作',
    success: '操作成功',
    successaps: '操作成功,請退出後重新登入生效',
    error: '操作失敗',
    dictionary: '字典項',
    del: {
      title: '提示',
      content: '刪除後不能恢複，是否確認刪除?'
    },
    T: '是',
    F: '否',
    null: '',
    len: '長度超出限製',
    notNull: '不能為空',
    list: '請至少選擇一條數據!',
    one: '請選擇一條數據!',
    numb: '代號',
    name: '名稱',
    placeholder: {
      numb: '請輸入代號',
      name: '請輸入名稱'
    },
    errorInfo: '錯誤信息',
    upLeavel: '上級不能選擇自己',
    notDel: '不能刪除,這條數據',
    others: '其他',
    tip: '提示',
    lose: '數據未保存，是否確認退出？',
    logout: '真的要登出登入嗎 ?',
    set: '請先選配置數據源賬套！',
    dept_tog: '請先同步部門數據',
    zoom: '全屏',
    clear: {
      title: '清除緩存',
      content: '該操作將會清除個性化數據，是否確認?'
    },
    noAccess: '您暫無權限訪問，請與管理員聯繫！',
    SaveOrNot: '數據已被修改，保存嗎？'
  },

  menu: {
    index: '首頁',
    dashboard: '儀錶盤',
    analysis: '分析頁',
    workplace: '協同工作臺',
    monitor: '監控頁（外部）',
    testwork: '測試功能',
    account: '個人頁',
    center: '個人中心',
    logout: '退出登入',
    settings: '個人設定',
    datasource: '數據源管理',
    sysconfig: '係統配置',
    placeholder: {
      search: '菜單搜索'
    },
    'sysapp': 'app管理',
    'sysmodeldatasource': '模塊數據原管理',
    'job-manage': '定時任務',
    'job_log': '定時任務日誌',
    'status_trace_log': '任務軌跡',
    'mqconfig': '即時消息',
    'work': '車間工作',
    'ipcbasic': '工控機基礎資料設置',
    'quality': '首檢',
    'inspection': '巡檢',
    'processInspection': '製程檢',
    'mes_verify': '已檢單據',
    mes: {
      sys: 'MES系統',
      sysSetting: 'MES系統設定',
      propertySetting: 'MES屬性設定',
      basicdata: '基礎數據',
      sn: '序列號管理',
      boxBuild:'箱碼生成作業',
      sop: 'SOP下發',
      stepSta: '工步工位關係表',
      zcSta: '工序工位關係表',
      containerCode: '容器代號設置',
      processParameter: '工序參數設置',
      processUnit: '工序單價設置',
      staffDepartment: '員工部門設置',
      productKeySetting: '產品關鍵部件關係設定',
      workStep: '工步基礎資料',
      snBuild: '工單序列號生成作業',
      reprint:'內碼重打',
      reBoxBuild:'箱碼重打',
      mesMdRef: '月日對照表',
      mesSnParam: '序號規則',
      mesSnRule: '序號編碼規則設定',
      mesStopSpc:'停線原因',
      mesMoLine:'工單上線',
      prdStep: '產品工步表',
      productKeySettingDetail: '產品關鍵部件關係設定詳情',
      stationCodeDetail: '工位代號設定詳情',
      containSetDetail: '容器代號設定詳情',
      PackingListManag: '裝箱作業管理',
      PackingList: '裝箱作業',
      PackingListDetail: '裝箱作業詳情',
      revoleBoxManag: '週轉箱管理',
      revoleBoxOperate: '週轉箱作業',
      revoleBoxOperateDetail: '週轉箱作業詳情',
      revoleBox: '週轉箱',
      revoleBoxDetail: '週轉箱詳情',
      handoverManag: '交接管理',
      handoverList: '交接單',
      exceptionManag: '異常管理',
      exceptionNotice: '異常通知單',
      exceptionNoticeDetail: '異常通知單詳情',
      exceptionHandlingJob: '異常處理作業',
      exceptionHandlingJobDetail: '異常處理作業詳情',
      qualityBasicData: '品質基礎資料',
      inspectionSetting: '檢驗項目設定',
      inspectionSettingDetail: '檢驗項目設定詳情',
      failCause: '不合格原因設定',
      measureToolSetting: '測量工具設定',
      measureToolSettingDetail: '測量工具設定詳情',
      processScheme: '製程檢驗方案',
      processSchemeDetail: '製程檢驗方案詳情',
      finishedProductScheme: '成品檢驗方案',
      finishedProductSchemeDetail: '成品檢驗方案詳情',
      firstInspectionManag: '首檢管理',
      firstInspection: '首檢',
      firstInspectionRecordData: '首檢（錄數據）',
      firstInspectionRecordDataDetail: '首檢（錄數據）詳情',
      firstInspectionDetail: '首檢詳情',
      sentInspection: '首檢送檢',
      sentInspectionDetail: '首檢送檢詳情',
      sentInspectionRecordData: '首檢自檢（錄數據）',
      sentInspectionRecordDataDetail: '首檢自檢（錄數據）詳情',
      itinerantMission: '巡檢',
      itinerantMissionDetail: '巡檢詳情',
      endInspectionManag: '末檢管理',
      endSentInspection: '末檢送檢',
      endSentInspectionDetail: '末檢送檢詳情',
      endInspection: '末檢',
      endInspectionDetail: '末檢詳情',
      endInspectionDetailRecordData: '末檢（錄數據）',
      endInspectionDetailRecordDataDetail: '末檢（錄數據）詳情',
      consignmentInspectionManag: '託工檢管理',
      consignmentInspection: '託工檢',
      consignmentInspectionRecordData: '託工檢（錄數據）',
      consignmentInspectionRecordDataDetail: '託工檢（錄數據）詳情',
      consignmentInspectionDetail: '託工檢詳情',
      consignmentSentInspection: '託工送檢',
      consignmentSentInspectionDetail: '託工送檢詳情',
      unqualifiedManag: '不合格管理',
      unqualifiedReview: '不合格評審單',
      unqualifiedReviewDetail: '不合格評審單詳情',
      unqualifiedReviewPermission: '不合格評審單權限',
      billsManag: '單據管理',
      billsUpdate: '單據更新',
      boardWork: '裝板作業',
      splitboard: '拆板作業',
      firstCheck: '產品首件檢驗作業',
      splitbox: '拆箱作業'
    },
    oms: {
      sys: 'OMS係統',
      sysSetting: 'OMS係統設置',
      propertySettings: 'OMS屬性設置',
    },
    aps: 'APS係統',
    'apset': 'APS設定',
    'priority': '客戶優先級設定',
    'merge': '合並排程設定',
    'modeSet': '共模設定',
    srm: 'SRM係統',
    'entrustment': '採購托工PO',
    'prdiversion': '請購匯總采購分流',
    'inquiryoffer': '詢價報價',
    'contractinquiry': '合同查詢',
    'stagepay': '合同阶段付款',
    'newpucontract': '新建采購合同',
    'inquirycompri': '詢價比價',
    'submission': '報交明細錶',
    'purchas': '採購托工報交',
    'myreport': '我的報交',
    'barcodeprinting': '條碼列印',
    'delivery': '送貨單',
    'deliverynote': '送貨單(採購托工)',
    'invoice': '採購對賬開票',
    'workers': '托工對賬開票',
    'documentIssuance': '圖紙發放',
    'applicationForInformation': '資料申請',
    'datadown': '資料下載',
    'datareview': '資料稽核',
    'downsettings': '下載設置',
    'cleardownset': '清除下載綁定',
    'nomatissed': '未發料明細表',
    'pickinglist': '領料明細表',
    'pickingdifference': '托工領料差异統計表',
    sys: '權限管理',
    'sys_user': '用戶管理',
    'sys_user_detail': '用戶管理詳情',
    'sys_menu': '菜單管理',
    'sys_role': '角色管理',
    'sys_dept': '部門管理',
    'sys_systenant': '租戶管理',
    system: '係統管理',
    'param': '參數管理',
    'sysreports': '報錶管理',
    'system_log': '日誌管理',
    'system_dict': '字典管理',
    'dictSearch': '字典查詢',
    'system_admin_file': '文件列錶',
    'system_syspublicparam': '參數管理',
    'system_client': '終端管理',
    'system_job_sys_job': 'job管理',
    'system_email_verification': '郵箱驗證管理',
    'system_social_details': '秘鑰管理',
    'system_token': '在线用户',
    'system_route': '動態路由',
    sys_daemon: '係統監控',
    'sysdaemon_admin': '服務監控',
    'sysdaemon_redis': '緩存監控',
    'swagger-ui': '接口文檔',
    'doc': '文檔擴展',
    'sysdaemon_tx_index': ' 事務監控',
    'sysdaemon_tx_model': '在線事務',
    'daemon_execution-log': '任務日誌',
    'sys_config': '註冊配置',
    'daemon_status-trace-log': '任務軌跡',
    'daemon_job-log': 'Quartz 日誌',
    workflow: '協同管理',
    'activiti_index': '模型管理',
    'activiti_process': '流程管理',
    'activiti_leave': '請假管理',
    'activiti_task': '任務待辦',
    barcode: '條碼管理',
    'WMS': 'WMS系統',
    'wmszcpgboss': '工序管理',
    'wmszcpg': '工序派工',
    'wmspgwg': '工序二次派工',
    'proostatus': '工序完工狀況',
    'prodispatchcompletion': '工序派工完工統計表',
    'wmsbgboss': '通知單報工管理',
    'wmsbgpg': '分配作業人員',
    'wmsbgwt': '通知單報工',
    'wmsbgpar': '通知單報參數',
    'returnMold': '模具還入',
    'moMoldOut': '製令轉模具領用',
    'tzMoldOut': '通知轉模具領用',
    'mfty': '驗收轉繳庫',
    'tiMm0': '免檢轉繳庫',
    'ordertransferware': '製令轉繳庫',
    'qlM6': '申請轉拖工補料',
    'qlM5': '申請轉拖工退料',
    'qlM4': '申請轉拖工領料',
    'material': '申請轉退料',
    'supplement': '申請轉補料',
    'assignmentMaterials': '托工轉領料',
    'qlMl': '申請轉領料',
    'orderTransfer': '製令轉領料',
    'noticereceipt': '通知單轉領料',
    'mfwrz': '完工品報工',
    'mfmvwork': '製程轉移',
    'notice': '通知單報工',
    'processwork': '製令+製程報工',
    'dailywork': '製成品+製成報工',
    'tiT6': '製成品送檢轉驗收',
    'tiT7': '托工送檢轉驗收',
    'products': '製成品轉送檢',
    'inspectionBar': '採購轉送檢',
    'commissionedworker': '托工送檢',
    'mfps': '特殊檢驗轉進貨',
    'rules': '條碼規則管理',
    'pastecode': '貼碼防呆拆碼規則',
    'purchase': '進貨管理',
    'production': '生産管理',
    'Supporting': '托工管理',
    'stock': '庫存管理',
    'sold': '銷貨管理',
    'separateCoding': '入庫條碼拆碼',
    'propertySettings': '屬性設定',
    'propertySet': '屬性設置',
    'equipment': '設備工位設定',
    'management': '設備管理人員設定',
    'stationcode': '工位代號設定',
    'inspect': '送檢單',
    'inspected': '驗收單',
    'reportWork': '報工管理',
    'pick': '領料單',
    'collected': '繳庫單',
    'supportworker': '托工轉托工繳回',
    'acceptancereturn': '驗收轉托工繳回',
    'mftwz': '完工品轉托工繳回',
    'single': '初盤單',
    'allocation': '調撥單',
    'adjustmentNote': '調增單',
    'dsMc': '調撥申請轉調撥單',
    'Prdt': '庫存查詢',
    'sales': '出庫轉銷貨',
    'salesBatch': '批量出庫銷貨',
    'moldOut': '模具領用管理',
    'MoldIn': '模具歸還管理',
    hotel: '酒店設備',
    fm: '文件管理',
    'creation': '文件類別',
    'version': '文件版本',
    'authority': '文件部門權限',
    'identifier': '文件編製',
    'grant': '文件發放',
    'void': '文件作廢',
    'change': '文件變更',
    'recovery': '檔案回收',
    'browsing': '文件瀏覽',
    'bdmanagement': '在地資料管理',
    'grantreport': '文件發放明細錶',
    'sqmxreport': '文件申請明細錶',
    'sqxzreport': '文件下載明細錶',
    report: '報錶係統',
    'Stock': '庫存查詢',
    'Distributor': '經銷商價格',
    'Sales': '未轉銷貨錶',
    'Subscribed': '收訂未交',
    'Receivable': '應收款賬齡',
    'Uninvoiced': '未開票明細錶',
    'Logistics': '物流查詢',
    'CustGrade': '客戶等級',
    formsbarcode: '報表分析',
    'tablefcim': 'FCIM記錄表',
    'sverbirth': '超產統計',
    'fcimcw': 'FCIM記錄表（場外）',
    'tablezz': '在制',
    'tablezzxq': '在制(詳情)',
    'tablewg': '完工',
    'tablexckz': '現場控制',
    'workReport': '人員報工狀況明細表',
    'salmTime': '人員工時核對錶',
    'learnCurve': '新員工學習曲線錶',
    mp: '微信管理',
    'mp_wxmenu': '菜單管理',
    'mp_wxstatistics': '營運數據',
    'mp_wxaccountfans': '粉絲管理',
    'mp_wxfansmsg': '消息管理',
    'mp_wxaccount': '賬戶管理',
    pay: '支付管理',
    'pay_cd': '收銀臺',
    'pay_paychannel': '通路管理',
    'pay_goods': '商品訂單',
    'pay_orders': '支付訂單',
    'pay_notify': '回調記錄',
    develop: '開發平臺',
    'develop_data': '數據源管理',
    'develop_design': '錶單設計',
    'develop_gen_form': '錶單管理',
    'develop_index': '代碼生成',
    basic: '基礎資料',
    'syssalm': '員工資料',
    'indx': '中類資料',
    'salm': '員工資料',
    'mywh': '倉庫資料',
    'area': '區域資料',
    'bank': '銀行資料',
    'Prdt_t': '貨品資料',
    'cur': '幣別資料',
    'bacc': '賬戶資料',
    'cust': '客戶資料',
    'sup': '供應商資料',
    'depro': '部門群組資料',
    'zcno': '製程資料',
    'syspdmcenterbase': '工作中心設定',
    'syspdmcentercapacity': '工作中心産能設定管理',
    'calendar': '日曆管理',
    'sysmfban': '班別管理',
    register: '系統註冊',
    wms: {
      sys: 'WMS系統',
      sysSetting: 'WMS系統設置',
      propertySetting: 'WMS屬性設置',
      pushMessage: '消息推送',
      baseData: 'WMS基礎資料',
      printWh: '打印出貨倉',
      storageSetting: '儲位存放設定',
      barcodeUpdate: '條碼數據更新',
      barcodeNeaten: '條碼整理',
      inspection: '採購轉送檢',
      equipmentData: '設備基礎資料',
      DeviceSetting: '設備狀態設定',
      faultCauseSetting: '故障原因設定',
      deviceStatusMan: '設備狀態管理',
      workingTeam: '工作組',
      workingTeamDetail: '工作組明細',
      devicemanage: '設備管理',
      dispatchMan: '派工管理',
      dispatchList: '派工單',
      dispatchDetail: '派工單詳情',
      ckManger: '出庫管理',
      boardWork: '（威林）裝板作業',
      boardRePrint:'箱板碼重打',
      scanPrint: '（威林）條碼打印作業'
    },
    device: {
      sys: '設備管理',
      basic: {
        data: '設備基礎資料',
        type: '設備類別',
        model: '設備型號',
        position: '設備位置',
        account: '設備臺帳',
        accountDetail: '設備臺帳詳情'
      },
      inspection: '設備點檢',
      inspectionDaily: '設備日常點檢',
      maintain: {
        maintain: '設備保養',
        data: '保養基礎資料',
        level: '保養等級設定',
        item: '保養項目設定',
        method: '保養方法',
        part: '保養部位',
        standard: '設備保養標準',
        standardDetail: '設備保養標準詳情',
        class: '班別設定',
        repair: '保養維修人員設定',
        plan: '保養計劃',
        planManage: '保養計劃管理',
        manage: {
          manage: '設備保養管理',
          detail: '設備保養詳情',
          record: '設備保養記錄表'
        }
      },
      repair: {
        data: '維修基礎資料',
        level: '故障級別',
        emergency: '緊急程度',
        type: '故障類別',
        cause: '故障原因'
      },
      report:{
        sys: '報表系統',
        design: '報表設計',
        analyse: '分析報表',
        modifyValue: '固定值修改',
      },
    },
    mold:{
      sys: '模具管理系統',
      basic:{
        data: '模具基礎資料',
        type: '模具類別',
        model: '模具型號',
        dataEstablishment: '模具資料建立',
        dataEstablishmentDetail: '模具資料建立詳情',
        dataEstablishmentGroup: '模具組資料建立',
        dataEstablishmentDetailGroup: '模具組資料建立詳情',
        discardReason: '模具報廢原因'
      },
      change:{
        change:'模具變動',
        start: '模具啓用',
        stop: '模具停用',
        verify: '模具驗證',
        query: '模具變動查詢'
      },
      receive:{
        receive: '模具領用',
        record: '模具領用記錄'
      },
      warehouse: '模具倉庫'
    },
    //自定义管理
    custommana:{
      title: '自定義管理',
      printCustommana:'套版管理',
      printTemplateDesigner:'套版設計',
      printTemplateDetails:'套版設計詳情',
      printTemplateInterfaceDetails: '套版接口詳情',
      printTemplateInterface:'套版接口列表',
      ivxSetting:'ivx設定',
      ivxAddressSetting:'地址設定',
      interfaceSetting:'接口設定',
      userSetting:'用戶設定',
      ivxManagement:'ivx管理',
    },
  },
  role: {
    roleName: '角色名稱',
    roleCode: '角色標識',
    roleDesc: '角色描述',
    dsType: '數據權限',
    weight: '權重',
    createTime: '創建時間',
    updateTime: '更新時間',
    dsScopeM: '組織',
    refresh: '修改成功,正在刷新權限',
    interfaceFieldPermission: '介面欄位許可權',
    satus: {
      '0': '全部',
      '1': '自定義',
      '2': '本級及子級',
      '3': '本級',
      '4': '本人',
      'T': '是',
      'F': '否'
    },
    placeholder: {
      name: '請輸入角色名稱',
      roleCode: '請輸入角色標識',
      roleDesc: '請輸入角色描述',
      dsType: '請輸選擇數據權限',
      weight: '請輸入權重(值越小權重越大)',
      createTime: '創建時間',
      dsScopeM: '請輸選擇權限'
    }
  },
  // 用戶管理
  user: {
    mac: 'mac 地址',
    userId: '序號',
    username: '用戶名',
    name: '姓名',
    phone: '手機號',
    phone1: '手機號1',
    email: '郵箱',
    roleName: '角色',
    statu: '狀態',
    lockFlag: '鎖定',
    sup: '供應商',
    status: {
      0: '有效',
      1: '鎖定'
    },
    deptName: '用戶部門',
    password: '密碼',
    oldpassword: '原密碼',
    password2: '確認密碼',
    message: '信息管理',
    pass: '密碼管理',
    tip: '用戶名已經存在',
    placeholder: {
      oldpassword: '原密碼不能為空且不少於6位',
      newpassword1: '新密碼不少於6位',
      newpassword2: '兩次輸入密碼不一緻!',
      mac: '請輸入mac地址',
      roleName: '請選擇角色',
      username: '請輸入用戶名',
      name: '請輸入姓名',
      deptName: '請選擇所屬部門',
      id: '請輸入用戶id',
      password: '請輸入密碼',
      phone: '請輸入手機號'
    }
  },
  // 菜單管理
  carte: {
    parentId: '父級節點',
    name: '標題',
    type: '類型',
    menuId: '節點ID',
    permission: '權限標識',
    path: '地址',
    icon: '圖示',
    sort: '排序',
    keepAlive: '路由緩沖',
    module: '是否模塊',
    menu: '菜單',
    button: '按鈕',
    hidden: '隱藏',
    placeholder: {
      parentId: '請輸入父級節點',
      name: '請輸入標題',
      menuId: '請輸入節點ID',
      permission: '請輸入標識',
      path: '請輸入地址',
      icon: '請選擇圖示'
    }
  },
  // 部門管理
  dept: {
    parentId: '上級編號',
    parentName: '上級部門',
    deptId: '部門編號',
    deptCode: '部門代號',
    name: '部門名稱',
    sort: '排序',
    stopDd: '失效時間',
    placeholder: {
      parentId: '請輸入父級節點',
      deptId: '請輸入節點編號',
      deptCode: '請輸入部門代號',
      name: '請輸入部門名稱',
      sort: '請輸入排序'
    }
  },
  // 租戶管理
  tenant: {
    name: '租戶名稱',
    statu: '狀態',
    customer: '註冊號',
    itSaGroup: '是否集團',
    status: '狀態',
    status0: '正常',
    status9: '凍結',
    id: '序號',
    code: '租戶編號',
    codeName: '當前數據源',
    startTime: '開始時間',
    endTime: '結束時間',
    placeholder: {
      name: '請輸入租戶名',
      customer: '請輸入註冊號',
      code: '請輸入租戶編號',
      compon: '請輸入賬套',
      rangepicker: '請選擇時間'
    },
    compon: '賬套',
    time: '有效時間'
  },
  // 日誌管理
  log: {
    type: '類型',
    typed: {
      0: '正常',
      9: '異常'
    },
    id: '序號',
    title: '標題',
    remoteAddr: 'IP位址',
    method: '請求方式',
    serviceId: '客戶端',
    time: '請求時間',
    createTime: '創建時間'

  },

  // 字典管理
  dict: {
    type: '類型',
    dictype: '字典類型',
    dictyped: {
      1: '係統類',
      0: '業務類'
    },
    id: '序號',
    description: '描述',
    remarks: '備註信息',
    createTime: '創建時間',
    placeholder: {
      type: '請輸入類型',
      dictype: '字典類型',
      value: '請輸入數據值',
      label: '請輸入標簽名',
      description: '請描述',
      sort: '請輸入排序',
      remarks: '請備註信息'
    },
    value: '數據值',
    label: '標簽名',
    sort: '排序'
  },
  // 參數管理
  param: {
    publicName: '名稱',
    system: '類型',
    publicKey: '鍵',
    publicValue: '值',
    validateCode: '編碼',
    publicType: '類型',
    status: '狀態',
    placeholder: {
      publicName: '請輸入名稱',
      system: '請選擇類型',
      publicKey: '請輸入鍵',
      publicValue: '請輸入值',
      validateCode: '請輸入編碼',
      status: '請選擇狀態'
    }
  },
  // job管理
  job: {
    a: '收起',
    b: '展開',
    state: {
      '1': '未PO',
      '2': '運行中',
      '3': '暫停'
    },
    jobExecuteStatus_1: {
      '0': '正常',
      '1': '異常'
    },
    jobType_1: {
      '1': 'java類',
      '2': 'spring bean',
      '3': 'Rest 調用',
      '4': 'jar',
      '9': '其他'
    },
    misfirePolicy_1: {
      '1': '錯失周期立即執行',
      '2': '錯失周期執行一次',
      '3': '下周期執行'
    },
    content: {
      stop: '即將暫停全部運行中定時任務, 是否繼續?',
      startJobs: '即將啓動全部暫定中定時任務, 是否繼續?',
      refresh: '即將刷新全部定時任務, 是否繼續?',
      start: ' 即將PO或啓動該任務, 是否繼續?',
      suspend: ' 即將暫停該任務, 是否繼續?',
      implement: '立刻執行一次任務, 是否繼續?',
      edit: '運行中定時任務不可修改，請先暫停後操作'
    },
    error: {
      del: '運行中任務不可刪除！',
      start: '定時任務已運行',
      suspend: '已暫停，不要重複操作'
    },
    jobId: '序號',
    startTime: '首次執行時間',
    previousTime: '上次執行時間',
    nextTime: '下次執行時間',
    jobName: '任務名稱',
    className: '執行文件',
    jobGroup: '任務組名',
    jobStatus: '任務狀態',
    jobExecuteStatus: '執行狀態',
    jobType: '類型',
    executePath: '執行路徑',
    methodName: '執行方法',
    methodParamsValue: '執行參數值',
    cronExpression: 'cron錶達式',
    misfirePolicy: '錯誤執行策略',
    remark: '備註信息',
    log: '日誌',
    start: '啓動',
    suspend: '暫停',
    implement: '執行',
    stop: '暫停全部任務',
    startJobs: '啓動全部任務',
    refresh: '重置所有任務',
    jobMessage: '狀態描述',
    executeTime: '執行時間(ms)',
    exceptionInfo: '異常信息',
    createTime: '開始時間',
    placeholder: {
      executePath: '請輸入執行路徑',
      className: '請輸入執行文件',
      jobType: '請輸選擇類型',
      jobName: '請輸入任務名稱',
      jobGroup: '請輸入任務組稱',
      jobStatus: '請選擇任務狀態',
      jobExecuteStatus: '請輸入執行狀態',
      methodName: '請輸入執行方法',
      methodParamsValue: '請輸入執行參數值',
      cronExpression: '請輸入cron錶達式',
      misfirePolicy: '請輸入錯誤執行策略',
      remark: '備註信息'
    }
  },

  // 終端管理
  client: {
    index: '序號',
    clientId: '編號',
    clientSecret: '密鑰',
    scope: '域',
    autoapprove: '自動放行',
    auto: {
      a: '是',
      b: '否'
    },
    authorizedGrantTypes: '授權模式',
    accessTokenValidity: '令牌時效',
    refreshTokenValidity: '刷新時效',
    webServerRedirectUri: '回調地址',
    authorities: '權限',
    additionalInformation: '擴展信息',
    placeholder: {
      clientId: '請輸入編號',
      clientSecret: '請輸入密鑰',
      scope: '請輸入域',
      accessTokenValidity: '令牌時效',
      refreshTokenValidity: '刷新時效',
      webServerRedirectUri: '請輸入回調地址',
      authorities: '請輸入權限',
      additionalInformation: 'JSON格式數據'
    }
  },
  indx: indxTW,
  salm: salmTW,
  ipcbasic: ipcbasicTW,
  hpgxdjb: hpgxdjbTW,
  inspectitems: inspectitemsTW,
  reasonSet: reasonSetTW,
  toolsetting: toolsettingTW,
  productKeySetting:productKeySettingTW,
  workStep:workStepTW,
  mesSnParam:mesSnParamTW,
  mesSnRule:mesSnRuleTW,
  mesStopSpc:mesStopSpcTW,
  mesMoLine:mesMoLineTW,
  mesMdRef:mesMdRefTW,
  finishedPlan: finishedPlanTW,
  processPlan: processPlanTW,
  category: categoryTW,
  equipmentModel: equipmentModelTW,
  deviceLocation: deviceLocationTW,
  deviceInform: deviceInformTW,
  defendSet: defendSetTW,
  maintenanceItems: maintenanceItemsTW,
  mainteMethods: mainteMethodsTW,
  maintenanceArea: maintenanceAreaTW,
  mainStandards: mainStandardsTW,
  classSet: classSetTW,
  defendRepair: defendRepairTW,
  faultLevel: faultLevelTW,
  urgency: urgencyTW,
  faultCategory: faultCategoryTW,
  mainPlan: mainPlanTW,
  deviceMaintain: deviceMaintainTW,
  routineCheck: routineCheckTW,
  causemalFunction: causemalFunctionTW,
  sbbyRecordForm: sbbyRecordFormTW,
  zcnoMes: zcnoMesTW,
  personalCenter:personalCenterTW,
  dispatchworker: dispatchworkerTW,
  boxOperation: boxOperationTW,
  turnOverBox: turnOverBoxTW,
  deliveryReceipt: deliveryReceiptTW,
  abnormalManage: abnormalManageTW,
  abnormalWork: abnormalWorkTW,
  inspectionForm: inspectionFormTW,
  firstInspectData: firstInspectDataTW,
  selfTest: selfTestTW,
  patrolinSpection: patrolinSpectionTW,
  completInspect: completInspectTW,
  completionTwo: completionTwoTW,
  completeInspecData: completeInspecDataTW,
  entrustWorker: entrustWorkerTW,
  entrustTwo: entrustTwoTW,
  entrustInspecData: entrustInspecDataTW,
  nonConform: nonConformTW,
  qualitySure: qualitySureTW,
  mywh: mywhTW,
  containset: containsetTW,
  prdt: prdtTW,
  cur: curTW,
  bank: bankTW,
  bacc: baccTW,
  area: areaTW,
  cust: custTW,
  sup: supTW,
  dep: depTW,
  zcno: zcnoTW,
  social: socialTW,
  dataSource: dataTW,
  code: codeTW,
  mail:mailTW,
  app: appTW,
  token: tokenTW,
  propertySettings: propertySettingsTW,
  propertySet: propertySetTW,
  equipment: equipmentTW,
  management: managementTW,
  stationcode: stationcodeTW,
  separateCoding: separateCodingTW,
  mfbarrmvrule: mfbarrmvruleTW,
  prdtBarcodeBox: prdtBarcodeBoxTW,
  tx: txTW,
  exe: exeTW,
  trace: traceTW,
  inventory: inventoryTW,
  prd: prdTW,
  uninvoiced: uninvoicedTW,
  sales: salesTW,
  logist: logistTW,
  grade: gradeTW,
  sub: subTW,
  file: fileTW,
  work: workTW,
  quality: qualityTW,
  process: processTW,
  inspec: inspecTW,
  picked: pickedTW,
  entrustment: entrustmentTW,
  prdiversion: prdiversionTW,
  inquiryoffer: inquiryofferTW,
  contractinquiry: contractinquiryTW,
  // stagepay:stagepayTW,
  newpucontract: newpucontractTW,
  inquirycompri: inquirycompriTW,
  submission: submissionTW,
  purchase: purchaseTW,
  source: sourceTW,
  codeIndex: codeIndexTW,
  base: baseTW,
  capacity: capacityTW,
  calendar: calendarTW,
  workClass: workClassTW,
  creation: creationTW,
  version: versionTW,
  authority: authorityTW,
  identifier: identifierTW,
  delivery: deliveryTW,
  grant: grantTW,
  launch: launchTW,
  completed: completedTW,
  dealt: dealtTW,
  invoice: invoiceTW,
  prover: proverTW,
  classification: classificationTW,
  examples: examplesTW,
  formTypes: formTypesTW,
  formEdit: formEditTW,
  profig: profigTW,
  design: designTW,
  void: voidTW,
  change: changeTW,
  priority: priorityTW,
  merge: mergeTW,
  modeSet: modeSetTW,
  scheduling: schedulingTW,
  paramterSetting: paramterSettingTW,
  parameters: parametersTW,
  list_standard: list_standardTW,
  heat_treatment: heat_treatmentTW,
  Furnace_No: Furnace_NoTW,
  attribute_definition: attribute_definitionTW,
  work_details: work_detailsTW,
  zlsq: zlsqTW,
  datadown: datadownTW,
  timesz: timeszTW,
  information: informationTW,
  apsworkcenterme: apsworkcentermeTW,
  message: messageTW,
  toolbar: toolbarTW,
  perm: permTW,
  register: registerTW,
  reForm: reFormTW,
  barcodeMenu: barcodeMenuTW,
  fp: fpTW,
  insp: inspTW,
  reWork: reWorkTW,
  devSta: devStaTW,
  faultCause: faultCauseTW,
  devStaManag: devStaManagTW,
  workGroup: workGroupTW,
  arc: arcTW,
  dashboard: dashboardTW,
  prdStep: prdStepTW,
  ivx: ivxTW,
  snBuild: snBuildTW,
  box: boxTW,
  board: boardTW,
  boardWork: boardWorkTW,
  stepSta: stepStaTW,
  zcSta: zcStaTW,
  cxzt:cxztTW,
  sop: sopTW,
  mesZx: zxTW,
  tempSetting:tempSettingTW,
  scanPrint: scanPrintTW,
  firstCheck: firstCheckTW,
  firstCheckaa: firstCheckaaTW,
  splitboard: splitboardTW,
  splitbox: splitboxTW,
  iframe: iframeTW,
  iframeusr: iframeusrTW
}