/**
 * Vue i18n loader
 * created by @musnow
 * https://github.com/musnow
 */
import Vue from 'vue'
import VueI18n from 'vue-i18n'
// default language
import enUS from './lang/en-US'
import zhCN from './lang/zh-CN'
import zhTW from './lang/zh-TW'
// change default accept-language
import { DEFAULT_LANGUAGE } from '@/store/mutation-types'
import ElementLocale from 'element-ui/lib/locale'

Vue.use(VueI18n)

export const defaultLang = 'zh-CN'

const messages = {
  'en-US': {
    ...enUS
  },
  'zh-CN': {
    ...zhCN
  },
  'zh-TW': {
    ...zhTW
  }
}

const i18n = new VueI18n({
  locale: defaultLang,
  fallbackLocale: defaultLang,
  silentTranslationWarn: true,
  messages
})

ElementLocale.i18n((key, value) => i18n.t(key, value))

export default i18n

const loadedLanguages = [defaultLang]

// 从缓存設置中加载当前语言
// if (Vue.ls.get('lang') !== null && defaultLang !== Vue.ls.get('lang')) {
//   loadLanguageAsync(localStorage.lang)
// }

function setI18nLanguage (lang) {
  i18n.locale = lang

  document.querySelector('html').setAttribute('lang', lang)
  return lang
}

/**
 * i18n Render
 * @param key
 * @returns rendered string
 */
export function i18nRender (key) {
  return i18n.t(key)
}

export function loadLanguageAsync (lang = defaultLang) {
  return new Promise(resolve => {
    // 缓存语言设置
    Vue.ls.set(DEFAULT_LANGUAGE, lang)
    if (i18n.locale !== lang) {
      if (!loadedLanguages.includes(lang)) {
        return import(/* webpackChunkName: "lang-[request]" */ `./lang/${lang}`).then(msg => {
          i18n.setLocaleMessage(lang, msg.default)
          loadedLanguages.push(lang)
          return setI18nLanguage(lang)
        })
      }
      return resolve(setI18nLanguage(lang))
    }
    return resolve(lang)
  })
}
