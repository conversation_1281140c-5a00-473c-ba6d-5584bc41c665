<template>
  <div class="layout">
    <vTable ref="vTable" v-bind="vTableProps" @toolbarClick="handleToolbarClick" @formItemIconClick="handleIconClick"
            @formDataChange="handleFormDataChange" @cellDblclick="handleDblclick" :printType="printType">
    </vTable>
    <file-Drawer
      ref="Drawer"
      @refresh="handleSubmit"
    />
  </div>
</template>

<script>
import { fetchList } from '@/api/system/file'

import vTable from '@/components/amtxts/vTable/vGrid.vue'
import FileDrawer from './fileDrawer'

export default {
  name:'TenantList',
  components: {
    vTable,FileDrawer
  },
  data() {
    return {
      printType: 0,
      vTableProps: {
        api_find: fetchList,
        toolbarItems: [
          { label: '文件列表', value: 'title' },
          { label: '重置', value: 'reset' },
          { label: '预览', value: 'preview' },
        ],
        formDataRaw: [
          { field: 'file.original', type: 'input', suffixIcon: 'el-icon-search'},
        ],
        tableColumn: [
          { type:'radio'},
          { field: "bucketName", title: "file.room", },
          {
            field: 'fileName', title: 'file.oldname',
            slots: {
              default: ({ row }) => {
                return this.$createElement('a', {
                  domProps: {
                    innerHTML: row.original,
                  },
                });
              },
            },
          },
          { field: 'type', title: 'file.type', },
          { field: 'fileSize', title: 'file.size', },
          { field: 'createBy', title: 'file.uploads', },
          { field: 'createTime', title: 'file.createTime', },
        ],

      }
    }
  },
  methods: {
    handleToolbarClick(params) {
      switch (params.code) {
        case 'create':
          this.$refs.Drawer.create({ title: this.$t('public.add') })
          // this.$refs.createRef.handleVisible();
          break;
        case 'reset':
          this.handleSubmit();
          break;
        case 'preview':
          this.preview();
          break
        default:
      }
    },
    handleIconClick(params) {
    },
    handleFormDataChange(params) {
    },
    handleChoose(params) {
    },
    handleSelectListEvent(params) {
    },
    handleDblclick(param) {
      if (['jpg', 'jpeg', 'png', 'gif','pdf'].includes(param.type)) {
        this.$router.push({
          name: 'viewPDF',
          params: {filePath:'/gateway' + param.url},
        })
      }else {
        this.$message.error('选择的文件类型不支持预览')
        return
      }
    },
    handleSubmit(){
      this.$refs.vTable.handleGet();
    },
    preview () {
      const records = this.$refs.vTable.$refs.gridRef.getRadioRecord()
      if (records == null){
        this.$message.error('请选择一条记录')
        return
      }
      if (['jpg', 'jpeg', 'png', 'gif','pdf'].includes(records.type)) {
        this.$router.push({
          name: 'viewPDF',
          params: {filePath:'/gateway' + records.url},
        })
      }else {
        this.$message.error('选择的文件类型不支持预览')
        return
      }
    },
  },
}
</script>
<style lang="less" scoped>
</style>
