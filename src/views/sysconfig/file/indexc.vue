
<template>
  <a-card :bordered="false">
    <a-row :gutter="8">
      <a-col :span="24">
        <a-row>
          <div class="table-page-search-wrapper">
            <a-form layout="inline">
              <a-row :gutter="48">
                <a-col
                  :md="8"
                  :sm="24"
                >
                  <a-form-item :label="$t('file.name')">
                    <a-input
                      v-model="queryParam.fileName"
                      :placeholder="$t('file.placeholder.name')"
                    />
                  </a-form-item>
                </a-col>
                <a-col
                  :md="8"
                  :sm="24"
                >
                  <span class="table-page-search-submitButtons">
                    <a-button
                      type="primary"
                      @click="getList"
                    >{{ $t('public.query') }}</a-button>
                    <a-button
                      style="margin-left: 8px"
                      @click="reset"
                    >{{ $t('public.reset') }}</a-button>
                  </span>
                </a-col>
              </a-row>
            </a-form>
          </div>
        </a-row>
        <!-- <a-button type="primary" style="margin-bottom:10px" @click="add" v-if="permissions.sys_systenant_add"> {{ $t('public.add') }}</a-button> -->
        <vxe-toolbar custom>
          <template v-slot:buttons>
            <a-button
              style="margin-left:10px"
              type="primary"
              icon="plus"
              @click="add()"
            >{{ $t('public.add') }}</a-button>
          </template>
        </vxe-toolbar>
        <vxe-table
          border
          resizable
          stripe
          highlight-current-row
          show-overflow
          highlight-hover-row
          export-config
          ref="xTable"
          :loading="loading"
          :data="tableData"
          :keyboard-config="{ isArrow: true }"
          @cell-dblclick="cellDBLClickEvent"
          :edit-config="{ trigger: 'click', mode: 'row' }"
        >
          <!-- <vxe-table-column type="checkbox" fixed="left" align="center" :width="50"></vxe-table-column> -->
          <vxe-table-column
            field="bucketName"
            title="file.room"
            align="center"
          ></vxe-table-column>
          <!--  <vxe-table-column field="fileName" title="file.name" align="center"></vxe-table-column>-->
          <vxe-table-column
            title="file.oldname"
            align="center"
          >
            <template slot-scope="scope">
              <a @click="downLoad(scope.row)">
                {{ scope.row.original }}
              </a>
            </template>
          </vxe-table-column>
          <vxe-table-column
            field="type"
            title="file.type"
            align="center"
          ></vxe-table-column>
          <vxe-table-column
            field="fileSize"
            title="file.size"
            align="center"
          ></vxe-table-column>
          <vxe-table-column
            field="createBy"
            title="file.uploads"
            align="center"
          ></vxe-table-column>
          <vxe-table-column
            field="createTime"
            title="file.createTime"
            align="center"
          ></vxe-table-column>

        </vxe-table>
        <vxe-pager
          :loading="loading"
          :current-page="tablePage.currentPage"
          :page-size="tablePage.pageSize"
          :total="tablePage.total"
          :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
          @page-change="handlePageChange"
        >
        </vxe-pager>
        <!-- 弹出框 -->
        <file-Drawer
          ref="Drawer"
          @getList="getList"
        />
      </a-col>
    </a-row>
  </a-card>
</template>
<script>
import { mapGetters } from 'vuex'
import { fetchList } from '@/api/system/file'
import FileDrawer from './fileDrawer'
import { handleDown } from '@/utils/util'

export default {
  name: 'TenantList',
  components: {
    FileDrawer
  },
  data () {
    return {
      queryParam: {},
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      loading: false,
      tableData: []
    }
  },
  computed: {
    ...mapGetters(['permissions'])
  },
  filters: {
  },
  created () {
    this.getList()
  },
  methods: {
    getList () {
      this.loading = true
      fetchList(
        Object.assign(
          {
            'current': this.tablePage.currentPage,
            'size': this.tablePage.pageSize,
            'descs': 'create_time'
          }, this.queryParam
        )
      )
        .then(res => {
          this.tableData = res.data.records
          this.tablePage.total = res.data.total
          this.tablePage.currentPage = res.data.current
          this.loading = false
        })
        .catch(e => {
          this.loading = false
        })
    },
    // 分页
    handlePageChange ({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getList()
    },
    // 重置搜索内容
    reset () {
      this.queryParam = {}
    },
    // 双击弹出编辑框
    cellDBLClickEvent ({ row }) {
      this.$refs.Drawer.edit({ title: this.$t('public.Detailed') }, row)
    },
    // 点击添加按钮
    add () {
      this.$refs.Drawer.create({ title: this.$t('public.add') })
    },

    // 点击原文件名字
    downLoad (row) {
      const fileName = row.fileName
      const bucketName = row.bucketName
      const original = row.original
      handleDown(fileName, bucketName, original)
    }
  }
}
</script>
