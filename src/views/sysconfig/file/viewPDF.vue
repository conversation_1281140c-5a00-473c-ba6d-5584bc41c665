<template>
  <div>
    <!-- 显示 PDF -->
    <div v-if="showPDF" class="pdf-container">
      <div class="block fixed-pagination">
        <el-pagination
          background
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-size="1"
          layout="total, prev, pager, next"
          :total="numPages">
        </el-pagination>
        <el-button size="mini" @click="scaleD" style="display: inline">放大</el-button>
        <el-button size="mini" @click="scaleX" style="display: inline">缩小</el-button>
      </div>
      <!-- 加载进度条 -->
      <el-progress
        :percentage="loadingProgress"
        v-if="isLoading"
        style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 300px; z-index: 101;"
      ></el-progress>
      <pdf ref="pdf" :src="url" :page="currentPage" @page-loaded="pageLoaded"  @loaded="onPdfLoaded" @progress="loadedRatio = $event"></pdf>
    </div>
    <!-- 显示图片 -->
    <div v-if="showImage">
      <img :src="url" alt="Preview" style="max-width: 100%; max-height: 80vh;" />
    </div>
  </div>
</template>

<script>
import pdf from 'vue-pdf';

export default {
  name: "vinit",
  components: {
    pdf
  },
  data() {
    return {
      showPDF: false,
      showImage: false,
      url: '',
      numPages: undefined,
      currentPage: 1, // 当前显示的页码
      scale: 100, //放大系数
      onPdfLoadedWidth:"",
      onPdfLoadedHeigh:"",
      loadedRatio: 0,
      isLoading: false, // 是否正在加载
      loadingProgress: 0 // 加载进度百分比
    };
  },
  computed: {
    // 当前页面链接 http://***********:9000/viewPDF?filePath=测试.pdf
    // return http://***********:9000
    trimmedUrl() {
      // 完整的URL
      const fullUrl = window.location.href;
      // 使用URL对象来解析URL
      const urlObject = new URL(fullUrl);
      // 获取截取后的域名和端口号部分
      const trimmedUrl = `${urlObject.protocol}//${urlObject.host}`;
      return trimmedUrl;
    }
  },
  watch: {},
  filters: {},
  activated() {
    this.checkFileType();
  },
  mounted() {
    this.checkFileType();
  },
  methods: {
    // 检查文件类型，决定显示 PDF 还是图片
    checkFileType() {
      const filePath = this.$route.params.filePath;
      if (filePath) {
        const fileExtension = filePath.split('.').pop().toLowerCase();
        if (fileExtension === 'pdf') {
          this.showImage = false;
          this.showPDF = true;
          this.getTotal();
        } else if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExtension)) {
          this.showPDF = false;
          this.showImage = true;
          this.url = this.trimmedUrl + filePath;
        }
      }
    },
    // 获取pdf总页数
    getTotal() {
      this.pdfPreviwe = this.$route.params.pdfPreviwe;
      // 多页pdf的src中不能直接使用后端获取的pdf地址 否则会按页数请求多次数据
      let loadingTask = this.trimmedUrl + this.$route.params.filePath;
      // 需要使用下述方法的返回值作为url
      this.url = pdf.createLoadingTask(loadingTask);
      this.isLoading = true;
      this.loadingProgress = 0;
      this.url.onProgress = (progressData) => {
        this.loadingProgress = Math.round(progressData.loaded / progressData.total * 100);
      };
      // 获取页码
      this.url.promise.then(pdf => {
        this.numPages = pdf.numPages;
        this.isLoading = false;
      }).catch(error => {
        console.error('获取 PDF 页数时出错:', error);
      });
    },
    handleCurrentChange(val) {
      this.currentPage = val
      console.log(`当前页: ${val}`);
    },
    //放大
    scaleD() {
      this.scale += 5;
      this.$refs.pdf.$el.style.width = parseInt(this.scale) + "%";
      this.$refs.pdf.$el.style.height = parseInt(this.scale) + "%";
    },

    //缩小
    scaleX() {
      this.scale += -5;
      this.$refs.pdf.$el.style.width = parseInt(this.scale) + "%";
      this.$refs.pdf.$el.style.height = parseInt(this.scale) + "%";
    },
    pageLoaded() {
      const pdfContainer = document.querySelector('.pdf-container');
      if (pdfContainer) {
        const containerWidth = pdfContainer.offsetWidth;
        const containerHeight = pdfContainer.offsetHeight;
        console.log('容器宽度:', containerWidth);
        console.log('容器高度:', containerHeight);
      }
      console.log('页面加载完成');
    },
    calculatePdfScale() {
      const pdfContainer = document.querySelector('.pdf-container');
      if (pdfContainer) {
        this.onPdfLoadedWidth = pdfContainer.offsetWidth;
        this.onPdfLoadedHeigh = pdfContainer.offsetHeight;
      }
    },
    onPdfLoaded() {
      this.$nextTick(() => {
        this.calculatePdfScale();
      });
    }
  }
};
</script>

<style scoped>
.fixed-pagination {
  position: fixed;
  top: 3px;
  left: 50%;
  transform: translateX(-50%);
  background-color: white;
  z-index: 100;
  width: fit-content;
  padding: 5px;
  box-sizing: border-box;
  display: grid; /* 使用 Grid 布局 */
  grid-auto-flow: column; /* 元素按列方向排列 */
  align-items: center; /* 垂直居中对齐 */
  gap: 10px; /* 设置元素之间的间距 */
}

</style>