<template>
  <div>
    <!-- 新增弹窗 -->
    <el-drawer :title="$t(title)" :visible.sync="drawer" size="40%" ref="drawerRef" class="JustMake-drawer">
      <div class="drawer-content">
        <el-upload
          ref="upload"
          class="upload-demo"
          action="/admin/sys-file/upload"
          drag
          :file-list="fileList"
          :on-success="onSuccess()"
          :auto-upload=true
          multiple>
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip" slot="tip">{{ $t('file.tip') }}</div>
        </el-upload>
        <div class="eFooter">
          <el-button @click="handleCancel">{{ $t('public.cancel') }}</el-button>
          <el-button type="primary" @click="handleCreate" :loading="drawerLoading">{{ drawerLoading ? 'Loading ...' :
            $t('public.sure')
            }}</el-button>
        </div>

      </div>
    </el-drawer>
  </div>
</template>

<script>
import Forms from '@/components/Forms/index.vue'
import {
  add,
  edit,
} from '@/api/fm/creation'
import {addObjs} from "@/api/system/file";
import moment from "moment";
export default {
  components: {
    Forms,
  },
  data() {
    return {
      api_save: add,
      drawer: false,
      drawerLoading: false,
      title: 'public.add',
      status: '0', // 0 新增 1 编辑
      fileList:[],
      roleTypeList: [],
      formData: {},
      currentRow: {},
      formDataRaw: [
        {
          field: 'creation.idxNo', type: 'input',
          rules: [{ required: true,message: this.$t('creation.placeholder.idxNo'), }]
        },
        {
          field: 'creation.name', type: 'input',
          rules: [{ required: true,message: this.$t('creation.placeholder.name'), }]
        },
        {
          field: 'creation.stopId', type: 'radio',
          values: [
            { label: this.$t('public.T'), value: 'T' },
            { label: this.$t('public.F'), value: 'F' },
          ],
        },
      ],
    }
  },
  created() {

  },
  watch: {
    status: function (newVal) {
      if (newVal === '0') {
        this.title = 'public.add'
        this.api_save = add
      } else {
        this.title = 'public.edit'
        this.api_save = edit
      }
    }
  },
  methods: {
    onSuccess(response, file, fileList) {
      console.log(response)
      console.log(file)
      console.log(fileList)
    },
    beforeUpload(file) {
      this.fileList = [...this.fileList, file]
      return false

    },
    handleVisible() {
      this.status = '0'
      this.formData = {}
      this.drawer = !this.drawer
    },
    getPropName(field) {
      return field.includes('.') ? field.split('.').pop() : field;
    },
    handleFormsChange(params) {
      this.$emit('formDataChange', params)
    },
    handleSelectListEvent(param) {
      this.$emit('selectListEvent', param)
      if (param.code === 'choose' && param.field === 'user.username') {
        this.formData.datasalNo = param.obj.data.salNo
        this.formData.name = param.obj.data.name //姓名
        this.formData.username = param.obj.data.salNo //用户名
        if (param.obj.data.sysDeptVo) {
          this.formData.deptName = param.obj.data.sysDeptVo.name  //部门名称
          this.formData.deptCode = param.obj.data.sysDeptVo.deptCode  //部门名称
          this.formData.deptId = param.obj.data.sysDeptVo.id  //部门名称
        }
      }
    },
    handelCompEvent(params) {
      this.$emit('formItemIconClick', params)
    },
    handleCancel() {
      this.formData = {}
      this.$nextTick(() => {
        this.$refs.drawerFormRef.clearValidate()
      })
      this.drawer = false
    },
    async handleCreate() {
      // 获取数据
      // this.
      // 匹配数据
      // 添加数据
      if (this.fileList.length > 0) {
        const formData = new FormData()
        this.fileList.forEach(file => {
          formData.append('files', file)
        })

        formData.append('filelog', true)
        formData.append('bucketName', 'file') // minio 文件存储桶名称
        console.log(formData)
        this.uploading = true
        addObjs(formData)
          .then((res) => {
            if (res.code === 0) {
              this.$emit('refresh')
              this.$message.success(this.$t('public.success'))
              this.loading = false
            }
          })
          .catch(err => this.requestFailed(err))
          .finally(() => {
            this.loading = false
          })
      } else {
        this.loading = false
        this.$message.error(this.$t('app.err'))
      }
    },
    // async handleCreate() {
    //   if (this.drawerLoading) {
    //     return;
    //   }
    //   if (!this.api_save || typeof this.api_save !== 'function') {
    //     return this.$message.error('请配置 api_save 参数');
    //   }
    //   // 表单校验
    //   try {
    //     await this.$refs.drawerFormRef.validate();
    //   } catch (error) {
    //     return;
    //   }
    //   let result = null;
    //   try {
    //     this.drawerLoading = true;
    //     result = await this.api_save(this.formData)
    //     if (result.code == 0) {
    //       this.$message.success(this.$t('public.success'))
    //       this.$emit('refresh')
    //       this.drawer = false;
    //     }
    //   } catch (err) {
    //     console.error(err)
    //     this.$message.error(err || this.$t('public.error'));
    //   } finally {
    //     this.drawerLoading = false;
    //     // this.$emit('toolbarClick', { code: 'create', result });
    //   }
    // },

    handleEdit(row) {
      this.currentRow = row
      this.status = '1'
      this.drawer = true
      this.$nextTick(() => {
        this.formData = Object.assign({}, row) // 拷贝数据
      })
    },
  },
}
</script>

<style scoped>
.upload-demo{
  width: 100%; /* 自定义宽度 */
  padding: 10px; /* 可选：自定义内边距 */
  text-align: center; /* 可选：文本居中 */
}
.eFooter{
  display: flex;
  justify-content: center;
  margin-top: 40px;
}
/deep/ .el-upload{
   width: 100%;
 }
/deep/ .el-upload-dragger{
  width: 100%;
}
</style>