<template>
<div>
  <a-drawer placement="right" :closable="false" @close="onClose" :visible="visible" :destroyOnClose="true" width="50%">
    <template slot="title">
      <span class="title-name">{{ title }}</span>
      <span v-if="modeType!=='0'" class="title-age">
        <a-dropdown>
          <a-button class="ant-dropdown-link">
            {{ $t('public.action') }}
            <a-icon type="down" />
          </a-button>
          <a-menu slot="overlay">
            <a-menu-item>
              <a @click="del()">{{ $t('public.delete') }}</a>
            </a-menu-item>
            <a-menu-item>
              <a @click="downLoad()">{{ $t('file.downLoad') }}</a>
            </a-menu-item>
          </a-menu>
        </a-dropdown>
      </span>
    </template>
    <a-form :form="form" ref="form">
      <a-row :gutter="16">
        <a-col v-if="modeType==='0'">
          <a-form-item :label="$t('file.enclosure')" v-bind="formItemLayout" :labelCol="{span:2 }" :wrapperCol="{span: 20}" v-if="modeType==='0'">
            <a-upload-dragger name="file" multiple :fileList="fileList" :remove="handleRemove" :beforeUpload="beforeUpload">
              <p class="ant-upload-drag-icon">
                <a-icon type="inbox" />
              </p>
              <p class="ant-upload-hint">
                {{ $t('file.tip') }}
              </p>
            </a-upload-dragger>
            <!-- <a-upload
                name="file"
                :multiple="false"
                action="/admin/sys-file/upload"
                :headers="headers"
                @change="handleChange"
              >
                <a-button type="primary"> <a-icon type="upload" /> {{ $t('file.upload') }} </a-button>
              </a-upload>
              <p >{{ $t('file.tip') }}</p> -->
          </a-form-item>
        </a-col>
        <div v-if="modeType==='1'">
          <a-col>
            <a-form-item :label="$t('file.room')" v-bind="formItemLayout">
              <a-input :disabled="formStatus" v-decorator="['bucketName', { rules: [{ required: true, message:$t('file.placeholder.bucketName') }
                  ] }]" :placeholder="$t('file.placeholder.bucketName')">

              </a-input>
            </a-form-item>
          </a-col>
          <a-col>
            <a-form-item :label="$t('file.name')" v-bind="formItemLayout">
              <a-input :disabled="formStatus" v-decorator="['fileName', { rules: [{ required: true, message:$t('file.placeholder.name') }
                  ] }]" :placeholder="$t('file.placeholder.name')">

              </a-input>
            </a-form-item>
          </a-col>
          <a-col>
            <a-form-item :label="$t('file.oldname')" v-bind="formItemLayout">
              <a-input :disabled="formStatus" v-decorator="['original', { rules: [{ required: true, message:$t('file.placeholder.original') }
                  ] }]" :placeholder="$t('file.placeholder.original')">

              </a-input>
            </a-form-item>
          </a-col>
          <a-col>
            <a-form-item :label="$t('file.type')" v-bind="formItemLayout">
              <a-input :disabled="formStatus" v-decorator="['type', { rules: [{ required: true, message:$t('file.placeholder.type') }
                  ] }]" :placeholder="$t('file.placeholder.type')">

              </a-input>
            </a-form-item>
          </a-col>
          <a-col>
            <a-form-item :label="$t('file.size')" v-bind="formItemLayout">
              <a-input :disabled="formStatus" v-decorator="['fileSize', { rules: [{ required: true, message:$t('file.placeholder.size') }
                  ] }]" :placeholder="$t('file.placeholder.size')">

              </a-input>
            </a-form-item>
          </a-col>
          <a-col>
            <a-form-item :label="$t('file.uploads')" v-bind="formItemLayout">
              <a-input :disabled="formStatus" v-decorator="['createBy', { rules: [{ required: true, message:$t('file.placeholder.uploads') }
                  ] }]" :placeholder="$t('file.placeholder.uploads')">

              </a-input>
            </a-form-item>
          </a-col>
          <a-col>
            <a-form-item :label="$t('file.createTime')" v-bind="formItemLayout">
              <a-input :disabled="formStatus" v-decorator="['createTime', { rules: [{ required: true, message:$t('file.placeholder.createTime') }
                  ] }]" :placeholder="$t('file.placeholder.createTime')">

              </a-input>
            </a-form-item>
          </a-col>
        </div>
      </a-row>

    </a-form>
    <a-row :gutter="16">
      <a-col class="gutter-row" :span="12" style="text-align:right">
        <a-button type="primary" v-if="modeType==='0'" :loading="loading" @click="handleOK()">{{ $t('public.save') }}</a-button>
      </a-col>
      <a-col class="gutter-row" :span="12" style="text-align:left">
        <a-button @click="onClose">{{ $t('public.cancel') }}</a-button>
      </a-col>
    </a-row>
  </a-drawer>
</div>
</template>

<script>
import Vue from 'vue'
import {
  DEFAULT_TENANT_ID
} from '@/store/mutation-types'
import {
  delObj,
  addObjs
} from '@/api/system/file'
import store from '@/store'
import {
  handleDown
} from '@/utils/util'
export default {
  name: 'FileModel',
  data() {
    return {
      title: '',
      visible: false,
      modeType: '',
      loading: false,
      formStatus: true,
      confirmLoading: false,
      fileList: [],
      form: this.$form.createForm(this),
      headers: {},
      row: {},
      formItemLayout: {
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 8
          },
          md: {
            span: 8
          },
          lg: {
            span: 4
          }
        },
        wrapperCol: {
          xs: {
            span: 5
          },
          sm: {
            span: 18
          },
          md: {
            span: 14
          },
          lg: {
            span: 18
          }
        }
      }
    }
  },
  created() {
    const TENANT_ID = Vue.ls.get(DEFAULT_TENANT_ID)
    const token = store.getters.access_token
    this.headers = {
      Authorization: 'Bearer ' + token,
      TENANT_ID: TENANT_ID
    }
  },
  methods: {
    handleRemove(file) {
      const index = this.fileList.indexOf(file)
      const newFileList = this.fileList.slice()
      newFileList.splice(index, 1)
      this.fileList = newFileList
    },
    beforeUpload(file) {
      // if (this.fileList.length === 1) {
      //   this.$message.error(this.$t('app.one'))
      //   return false
      // }
      this.fileList = [...this.fileList, file]
      return false
    },

    // 删除
    del() {
      const that = this
      this.$confirm({
        title: this.$t('public.del.title'),
        content: this.$t('public.del.content'),
        okText: this.$t('public.sure'),
        okType: 'danger',
        cancelText: this.$t('public.cancel'),
        onOk() {
          that.loading = true
          const id = that.row.id
          delObj(id)
            .then(() => {
              that.$emit('getList')
              that.onClose()
              that.loading = false
              that.$message.success(that.$t('public.success'))
            })
            .catch(() => {
              that.loading = false
            })
        },
        onCancel() {}
      })
    },
    // 文件下载
    downLoad() {
      const fileName = this.row.fileName
      const bucketName = this.row.bucketName
      const original = this.row.original
      handleDown(fileName, bucketName, original)
    },
    onClose() {
      this.loading = false
      this.visible = false
      this.modeType = ''
    },
    // 添加弹框
    create(model) {
      this.modeType = '0'
      this.title = model.title
      this.visible = true
      this.fileList = []
    },
    // 点击编辑按钮
    handleMenuClick() {
      this.modeType = '2'
      this.title = this.$t('public.edit')
      this.formStatus = true
    },
    edit(model, row) {
      this.title = model.title
      this.modeType = '1'
      this.row = row
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue({
          type: row.type,
          bucketName: row.bucketName,
          fileName: row.fileName,
          original: row.original,
          fileSize: row.fileSize,
          createBy: row.createBy,
          createTime: row.createTime
        })
      })
    },
    // 确认添加
    handleOK() {
      this.form.validateFields((err, values) => {
        if (!err) {
          this.loading = true
          if (this.fileList.length > 0) {
            const formData = new FormData()
            this.fileList.forEach(file => {
              formData.append('files', file)
            })
            formData.append('filelog', true)
            formData.append('bucketName', 'file') // minio 文件存储桶名称
            this.uploading = true
            addObjs(formData)
              .then((res) => {
                if (res.code === 0) {
                  this.loading = false
                  this.$emit('refresh')
                  this.visible = false
                  this.$message.success(this.$t('public.success'))
                }
              })
              .catch(err => this.requestFailed(err))
              .finally(() => {
                this.loading = false
              })
          } else {
            this.loading = false
            this.$message.error(this.$t('app.err'))
          }
        }
      })
    }

  }
}
</script>
