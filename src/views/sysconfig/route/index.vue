<template>
  <div>
    <p>
      <a-alert
        message="路由配置是非常专业的事情，不建议非工程师操作"
        banner
        closable
      /></p>
    <!--在模板中使用vue-json-editor-->
    <vue-json-editor v-model="json"></vue-json-editor>
    <!-- <div><button>更新</button></div> -->
    <a-row :gutter="16" style="text-align:center;margin-top:12px">
      <span >  <a-button type="primary" @click="update" :loading="loading" >{{ $t('public.to-update') }}</a-button></span>
    </a-row>
  </div>
</template>

<script>
// import { fetchList, putObj, refreshObj } from '@/api/system/route'
import { fetchList, putObj, refreshObj } from '@/api/system/route'

import vueJsonEditor from 'vue-json-editor'
export default {
  data () {
    return {
      json: null,
      loading: false
    }
  },

  // 注入vueJsonEditor组件
  components: {
    vueJsonEditor
  },
  created () {
    this.getList()
  },
  methods: {
    getList () {
      fetchList().then((res) => {
        const re = res.data
        re.forEach(i => {
          if (i.predicates) {
            i.predicates = JSON.parse(i.predicates)
          }
          if (i.filters) {
            i.filters = JSON.parse(i.filters)
          }
        })
        this.json = re
      }).catch((err) => {
            this.requestFailed(err)
          })
    },
    update () {
      this.loading = true
      putObj(this.json).then(() => {
        this.loading = false
        refreshObj().then(() => {
          this.loading = false
          this.$message.success(this.$t('public.success'))
        }).catch((err) => {
            this.requestFailed(err)
          })
      }).catch((err) => {
            this.requestFailed(err)
          })
    }
  }
}
</script>
