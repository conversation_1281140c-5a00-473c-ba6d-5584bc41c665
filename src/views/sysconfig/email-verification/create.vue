<template>
  <div>
    <!-- 新增弹窗 -->
    <el-drawer :title="$t(title)" :visible.sync="drawer" size="40%" ref="drawerRef" class="JustMake-drawer">
      <div class="drawer-content">
        <el-form ref="drawerFormRef" :model="formData" label-position="right" label-width="100px" class="drawer-form">
          <el-row>
            <el-col v-for="(item, index) in formDataRaw" :key="index" :span="item.span || 12">
              <el-form-item :label="$t(item.field)" :title="$t(item.field)" :prop="getPropName(item.field)"
                :rules="item.rules">
                <Forms :data="item" v-model="formData[getPropName(item.field)]" @iconClick="handelCompEvent"
                  @change="handleFormsChange" @selectListEvent="handleSelectListEvent"></Forms>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="drawer-footer">
        <el-button @click="handleCancel">{{ $t('public.cancel') }}</el-button>
        <el-button type="primary" @click="handleCreate" :loading="drawerLoading">{{ drawerLoading ? 'Loading ...' :
          $t('public.sure')
          }}</el-button>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import Forms from '@/components/Forms/index.vue'
import { list } from '@/api/admin/datasource'
import { updateOrSaveEmail } from '@/api/admin/mail'
import { mapState } from 'vuex'
export default {
  components: {
    Forms,
  },
  data() {
    return {
      api_save: updateOrSaveEmail,
      drawer: false,
      drawerLoading: false,
      title: 'public.add',
      status: '0', // 0 新增 1 编辑
      formData: {},
      currentData:{},
      currentRow: {},
      dataBaseNames: [],
      dsScopeCode: null,
      formDataRaw: [
        // {
        //   field: 'mail.tenantId', type: 'input',
        //   rules: [{ required: true, message: this.$t('tenant.placeholder.tenantId') }],
        // },
        {
          field: 'mail.smtpserv', type: 'input',
          rules: [{ required: true, message: this.$t('mail.placeholder.smtpserv') }],
        },
        {field: 'mail.sendmail', type: 'input',
          rules: [{ required: true, message: this.$t('mail.placeholder.sendmail') }],
        },
        {field: 'mail.pwd', type: 'input',
          rules: [{ required: true, message: this.$t('mail.placeholder.pwd') }],
        },
      ]
    }
  },

  computed: {
    ...mapState({
      userInfo: state => state.user.userInfo
    })
  },
  watch: {
    status: function (newVal) {
      if (newVal === '0') {
        this.title = 'public.add'
        this.api_save = updateOrSaveEmail
      } else {
        this.title = 'public.edit'
        this.api_save = updateOrSaveEmail
      }
    }
  },
  methods: {
    handleVisible() {
      this.status = '0'
      this.formData = {}
      this.currentData.tenantId=this.userInfo.tenantId
      this.currentData.createBy=this.userInfo.name
      this.currentData.state="0"
      this.$nextTick(() => {
        this.$refs.drawerFormRef.clearValidate()
      })
      this.drawer = !this.drawer
    },
    getPropName(field) {
      return field.includes('.') ? field.split('.').pop() : field;
    },
    handleFormsChange(params) {
      this.$emit('formDataChange', params)
    },
    handleSelectListEvent(param) {
      this.$emit('selectListEvent', param)
    },
    handelCompEvent(params) {
      this.$emit('formItemIconClick', params)
    },
    handleCancel() {
      this.formData = {}
      this.$nextTick(() => {
        this.$refs.drawerFormRef.clearValidate()
      })
      this.drawer = false
    },
    async handleCreate() {
      if (this.drawerLoading) {
        return;
      }
      if (!this.api_save || typeof this.api_save !== 'function') {
        return this.$message.error('请配置 api_save 参数');
      }
      // 表单校验
      try {
        await this.$refs.drawerFormRef.validate();
      } catch (error) {
        return;
      }
      let result = null;
      try {
        this.drawerLoading = true;
        if (this.currentRow.pwd!==this.formData.pwd){
          this.currentData.state="1"
        }
        this.currentData={
          ...this.currentData,
          ...this.formData
        }
        this.currentData.userId=this.userInfo.userId
        result = await this.api_save(this.currentData)
        if (result.code == 0) {
          this.$message.success(this.$t('public.success'))
          this.$emit('refresh')
        }
      } catch (err) {
        this.$message.error(err || this.$t('public.error'));
      } finally {
        this.drawer = false;
        this.currentData={}
        this.currentRow={}
        this.drawerLoading = false;
      }
    },
    handleEdit(row) {
      this.currentRow = row
      this.status = '1'
      this.drawer = true
      this.currentData.tenantId=this.userInfo.tenantId
      this.formData.updateBy=this.userInfo.name
      this.currentData.state="0"
      this.$nextTick(() => {
        this.formData = Object.assign({}, row) // 拷贝数据

      })
    },

  },

}
</script>
