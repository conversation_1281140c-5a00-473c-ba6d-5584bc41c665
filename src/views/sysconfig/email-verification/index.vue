<template>
  <div class="layout">
    <vTable ref="vTable" v-bind="vTableProps" @toolbarClick="handleToolbarClick" @formItemIconClick="handleIconClick"
            @formDataChange="handleFormDataChange" @cellDblclick="handleDblclick">
    </vTable>
    <CreateDialog ref="createRef" @refresh="handleSubmit"></CreateDialog>
  </div>
</template>
<script>
import vTable from '@/components/amtxts/vTable/vGrid.vue'
import CreateDialog from './create.vue'
import { delEmail, getEmailPage } from '@/api/admin/mail'
export default {
  name: 'EmailVerification',
  components: {
    vTable, CreateDialog
  },
  data() {
    return {
      vTableProps: {
        api_find: getEmailPage,
        api_delete: delEmail,
        toolbarItems: [
        ],
        formDataRaw: [
          {
            field: 'mail.smtpserv', type: 'input', placeholder: this.$t('mail.placeholder.smtpserv')
          },
          {
            field: 'mail.sendmail', type: 'input', placeholder: this.$t('mail.placeholder.sendmail')
          },
        ],
        tableColumn: [
          // { field: "id", title: "id", },
          // { field: "tenantId", title: "mail.tenantId", },
          { field: "smtpserv", title: "mail.smtpserv", },
          { field: 'user', title: 'mail.user', },
          { field: 'sendmail', title: 'mail.sendmail', },
          { field: 'createBy', title: 'mail.createBy', },
          { field: 'createTime', title: 'mail.createTime', },
          { field: 'updateTime', title: 'mail.updateTime', },
          { field: 'updateBy', title: 'mail.updateBy', },
        ],
      }
    }
  },
  methods: {
    handleToolbarClick(params) {
      switch (params.code) {
        case 'create':
          this.$refs.createRef.handleVisible();
          break;
        default:
      }
    },
    handleIconClick(params) {
    },
    handleFormDataChange(params) {
    },
    handleChoose(params) {
    },
    handleSelectListEvent(params) {
    },
    handleDblclick(param) {
      this.$refs.createRef.handleEdit(param);
    },
    handleSubmit() {
      this.$refs.vTable.handleGet();
    }
  },
}
</script>
