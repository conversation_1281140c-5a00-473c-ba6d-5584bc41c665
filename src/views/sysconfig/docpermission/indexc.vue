<template>
<div class="consrm">
  <div  style="text-align:right;margin-right:16px;position: absolute;top: 102px;right:0px;z-index:999">
    <a-button
                size='small'
                type="primary"
                style="margin-left:10px"
                
                @click="add()"
              >{{ $t('public.add') }}</a-button>
              <!-- v-if="permissions['authory_add']" -->
                  <a-button
                      size='small'
                      type="primary"
                      style="margin-left: 8px"
                      @click="reset"
                    >{{ $t('public.reset') }}</a-button>
  </div>
  <el-tabs type="border-card">
      <el-tab-pane label="单据权限">
  <a-card :bordered="false">
    <a-spin :spinning="spinning">
      <a-row :gutter="8">
        <a-col :span="24">
          <div class="table-page-search-wrapper">
            <a-form layout="inline">
              <a-row :gutter="48">
                <a-col
                  :md="8"
                  :sm="24"
                >
                  <a-form-item label="单据">
                     <el-select
                        v-model="queryParam.bilType"
                        placeholder="请选择单据"
                        size="mini"
                        @change="biltychange"
                        clearable
                        style="max-width:200px; width:100%;"
                      >
                        <el-option
                          v-for="item in biltypedatatwo"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        ></el-option>
                      </el-select>
                  </a-form-item>
                </a-col>
                <a-col
                  :md="8"
                  :sm="24"
                >
                     <a-form-item
            label="权限人"
            >
              <my-selectList
                url="/admin/user/page"
                :tableColumn="$Column.apsusertwo"
                :form="$Form.apsuser"
                :read-only="true"
                :data="data"
                name="salNo"
                @choose="choose($event)"
                placeholder="权限人"
              ></my-selectList>
           </a-form-item>
                  </a-col>
                <a-col
                  :md="8"
                  :sm="24"
                  style="margin-top:5px;"
                >
                  <span class="table-page-search-submitButtons">
                    <a-button
                      size='small'
                      type="primary"
                      @click="search"
                    >{{ $t('public.query') }}</a-button>
                   
                  </span>
                </a-col>
              </a-row>
            </a-form>
          </div>
          <vxe-toolbar custom>
            <template v-slot:buttons>
              <!-- <a-dropdown :trigger="['click']">
                <a-button size='small'>
                  {{ $t('public.action') }}
           
                </a-button>
                <a-menu slot="overlay">
                  <a-menu-item key="0">
                    <a @click="dropdownMenuEvent('remove')">{{ $t('public.delete') }}</a>
                  </a-menu-item>
                </a-menu>
              </a-dropdown> -->
              
            </template>
          </vxe-toolbar>
          <vxe-table
            size='small'
            border
            resizable
            stripe
            highlight-current-row
            show-overflow
            highlight-hover-row
            export-config
            ref="xTable"
            :loading="loading"
            :data="tableData"
            :keyboard-config="{ isArrow: true }"
            @cell-dblclick="cellDBLClickEvent"
            :edit-config="{ trigger: 'click', mode: 'row' }"
          >
            <!-- <vxe-table-column
              type="checkbox"
              fixed="left"
              align="center"
              :width="50"
            ></vxe-table-column> -->
            <vxe-table-column
              field="bilName"
              fixed="left"
              title="单据"
              align="center"
            ></vxe-table-column>
            <vxe-table-column
              field="bilType"
              fixed="left"
              title="单据类型"
              align="center"
            ></vxe-table-column>
            <vxe-table-column
              field="username"
              fixed="left"
              title="权限人"
              align="center"
            ></vxe-table-column>
            <vxe-table-column
              field="save"
              fixed="left"
              title="修改"
              align="center"
            >
            <template slot-scope="scope">
              <div v-if="scope.row.save == true">是</div>
              <div v-else>否</div>
            </template>
            </vxe-table-column>
            <vxe-table-column
              field="del"
              fixed="left"
              title="删除"
              align="center"
            >
             <template slot-scope="scope">
              <div v-if="scope.row.del == true">是</div>
              <div v-else>否</div>
            </template>
            </vxe-table-column>
            <vxe-table-column title="public.action" align="center" fixed="right">
            <template slot-scope="scope">
              <a-button  style="background-color: #f56c6c;border-color: #f56c6c;color:#fff;" size="small"   @click="dropdownMenuEvent(scope.row)">
                  删除
                </a-button>
                <!-- v-if="permissions['authory_del']" -->
            </template>
              <!-- <a-popconfirm title="确定删除该数据？" ok-text="确定" @confirm="deleEvent(scope.row)" cancel-text="取消">
                <a-button size="small">
                  删除
                </a-button>
              </a-popconfirm> -->
          </vxe-table-column>
          </vxe-table>
          <vxe-pager
            :loading="loading"
            :current-page="tablePage.currentPage"
            :page-size="tablePage.pageSize"
            :total="tablePage.total"
            :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
            @page-change="handlePageChange"
          ></vxe-pager>
        </a-col>
        <!-- 添加弹出框 -->
        <salm-drawer
          ref="modal"
          @onOk="onOk"
        />
      </a-row>
    </a-spin>
  </a-card>
      </el-tab-pane>
  </el-tabs>
</div>
</template>

<script>
import Vue from 'vue'
import { authoryquery,authorytype,authorydel } from '@/api/system/file'
import { tenantList } from '@/api/login'
import { DEFAULT_TENANT_ID } from '@/store/mutation-types'
import { getTog, getTogto } from '@/api/salm'
import { deptTree } from '@/api/admin/dept'
import salmDrawer from './salmDrawer'
import { mapGetters } from 'vuex'
import MySelectList from '@/components/MySelectList'
export default {
  name: 'SalmList',
  components: {
    salmDrawer,
    MySelectList
  },
  data () {
    return {
      node: {},
      spinning: false,
      props: {
        // url: '/admin/dept/lazyTree',
         url: '/mes/qcItm/allQcitm',
        params: 'qcItm' // 索引
        // deptId
      },
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      loading: false,
      loading1: false,
      // 查询参数
      queryParam: {},
      tableData: [],
      biltypedata:[],
      biltypedatatwo:[],
      data:'',
    }
  },

  created () {
    this.authtype()
    this.getList()
  },
  computed: {
    ...mapGetters(['permissions'])
  },
  methods: {
    biltychange(val){
   
         this.queryParam.bilName = this.biltypedatatwo.filter(i => i.value === val)[0].label
    },
    authtype(){
      authorytype(
      )
        .then(res => {
          this.biltypedata = res.data 
          let newArr = new Array
          for (var key in this.biltypedata) {
          
            var temp = {};
            if (this.biltypedata[key] != "") {
              temp.value = key;
              temp.label = this.biltypedata[key];
          
              newArr.push(temp);
            }
          }
          this.biltypedatatwo = newArr
          // this.tablePage.total = res.data.total
          // this.tablePage.currentPage = res.data.current
          // this.loading = false
        })
        .catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    // 渲染组件
    choose (obj) {
      if (obj.obj.name === 'salNo') {
        this.queryParam.username = obj.obj.data.username
      }
    },
    search () {
      this.tablePage.currentPage = 1
      this.getList()
    },
    getList () {
      // fetchList
      this.loading = true
      
      authoryquery(
        Object.assign(
          {
            // qcItm: this.queryParam.qcItm,
            // dep: this.queryParam.dep,
            // name: this.queryParam.name,
            bilName:this.queryParam.bilName,
            bilType:this.queryParam.bilType,
            username:this.queryParam.username,
            current: this.tablePage.currentPage,
            size: this.tablePage.pageSize
          }
        )
      )
        .then(res => {
          this.tableData = res.data.records
          this.tablePage.total = res.data.total
          this.tablePage.currentPage = res.data.current
          this.loading = false
        })
        .catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    // 分页
    handlePageChange ({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getList()
    },
    // 重置搜索内容
    reset () {
      this.queryParam = {}
      this.data = ''
    },
    // 新增
    add () {
      this.$refs.modal.create({ title: this.$t('public.add') })
    },
    // 双击弹出编辑框
    cellDBLClickEvent ({ row }) {
     
      this.$refs.modal.edit({ title: this.$t('public.Detailed') }, row)
    },
    onOk () {
      this.reset()
      this.getList()
      // this.$refs.tree.onLoadData(this.node)
    },
    // 删除按钮
    dropdownMenuEvent (row) {
      // switch (name) {
      //   case 'remove': {
          // const selectRecords = this.$refs.xTable.getCheckboxRecords()
          // if (selectRecords.length) {
          //   const arr = []
          //   selectRecords.forEach(i => {
          //     return arr.push(i.keyId)
          //   })
            const that = this
            this.$confirm({
              title: this.$t('public.del.title'),
              content: this.$t('public.del.content'),
              okText: this.$t('public.sure'),
              okType: 'danger',
              cancelText: this.$t('public.cancel'),
              onOk () {
                // delAll
                that.loading = true
                authorydel({keyId:row.keyId ,version:row.version})
                  .then(() => {
                    that.getList()
                    that.loading = false
                    // that.$refs.tree.getTree()
                    that.$message.success(that.$t('public.success'))
                  })
                  .catch(err => that.requestFailed(err))
                  .finally(() => {
                    that.loading = false
                  })
              },
              onCancel () {
                that.loading = false
              }
            })
          // } else {
          //   this.$message.warning(this.$t('public.list'))
          // }
          // break
      //   }
      // }
    }

  }
}
</script>
<style lang="less">
</style>
