<template>
  <div>
    <a-drawer
      :title="title"
      placement="right"
      :closable="false"
      @close="onClose"
      :visible="visible"
      :destroyOnClose="true"
      width="60%"
    >
    
      <a-form
        :form="form"
        ref="form"
      >
      
        <a-row>
          <a-form-item
          label="单据类型"
          v-bind="formItemLayout"
        >
      <a-select
       :disabled="formStatus"
       @change="biltychange"
            allowClear
            v-decorator="['bilType', { rules: [{ required: true, message: '单据类型' }] }]"
            placeholder="单据类型"
            style="width: 100%"
            :getPopupContainer="triggerNode => {return triggerNode.parentNode || document.body;}"
          >
            <a-select-option
              v-for="item in biltypedatatwo"
              :key="item.value"
              :value="item.value"
              :label="item.label"
            >
              {{item.label}}
            </a-select-option>
          </a-select>
      </a-form-item>
         
           <a-form-item
              v-bind="formItemLayout"
            label="权限人"
            >
             <!-- :disabled="formStatus" -->
            <!-- multiple -->
              <my-selectList
              :disabled="formStatus"
                url="/admin/user/page"
                :tableColumn="$Column.apsusertwo"
                :form="$Form.apsuser"
                :read-only="true"
                :data="data"
                name="salNo"
                @choose="choose($event)"
                v-decorator="['username', { rules: [{ required: true, message:'请选择权限人' } ] }]"
                placeholder="权限人"
              ></my-selectList>
           </a-form-item>
          <!-- <a-col
            :xs="24"
            :sm="24"
            :md="12"
            :lg="8"
            :xl="8"
          >
            <a-form-item
              label="权限人"
              v-bind="formItemLayout"
            >
              <my-selectListwo
                        url="mes/basicData/salData"
                        :read-only="true"
                        :tableColumn="$Column.salm"
                        :form="$Form.salm"
                        :data="data"
                        name="salNo"
                        @choose="choose($event)"
                        ref="selectList"
                        v-decorator="['salNo', { rules: [{ required: true, message:'请选择权限人' } ] }]"
                        placeholder="请选择权限人"
                      ></my-selectListwo>
            </a-form-item>
          </a-col> -->
        
          <a-form-item
            label="是否修改"
            v-bind="formItemLayout"
            :disabled="formStatus"
          >
            <a-switch
           
               v-model="save"
            />
          </a-form-item>
    
       
          <a-form-item
            label="是否删除"
            v-bind="formItemLayout"
            :disabled="formStatus"
          >
            <a-switch
           v-model="del"
               @change="onChange"
                placeholder="是否删除"
            />
          </a-form-item>
    
          <!-- <a-col
            :xs="24"
            :sm="24"
            :md="12"
            :lg="8"
            :xl="8"
          >
            <a-form-item
              label="检验名称"
              v-bind="formItemLayout"
            >
              <a-input
                :disabled="formStatus"
                v-decorator="['name', { rules: [{ required: true, message:'检验名称' }
                                                ,{ max:50,message:$t('public.len')}
                ] }]"
                placeholder="请输入检验名称"
              />
            </a-form-item>
          </a-col> -->
          <!-- <a-col
            :xs="24"
            :sm="24"
            :md="12"
            :lg="8"
            :xl="8"
          >
            <a-form-item
              label="备注"
              v-bind="formItemLayout"
            >
              <a-input
                :disabled="formStatus"
                v-decorator="['rem', { rules: [{ required: false, message:'备注' }
                                                   ,{ max:40,message:$t('public.len')}
                ] }]"
                placeholder="请输入备注"
              />
            </a-form-item>
          </a-col> -->
          <!-- <a-col
            :xs="24"
            :sm="24"
            :md="12"
            :lg="8"
            :xl="8"
          >
            <a-form-item
              label="上级检验项目"
              v-bind="formItemLayout"
            >
              <a-input
                :disabled="formStatus"
                v-decorator="['qcItmUp', { rules: [{ required: false, message:'上级检验项目' }
                                                ,{ max:20,message:$t('public.len')}
                ] }]"
                placeholder="请输入上级检验项目"
              />
            </a-form-item>
          </a-col> -->
          
          <!-- <a-col
            :xs="24"
            :sm="24"
            :md="12"
            :lg="8"
            :xl="8"
          >
            <a-form-item
              label="检验方法描述"
              v-bind="formItemLayout"
            >
              <a-input
                :disabled="formStatus"
                v-decorator="['qcMth', { rules: [{ required: false, message:'检验方法描述' }
                                               ,{ max:20,message:$t('public.len')}
                ] }]"
                placeholder="请输入检验方法描述"
              />
            </a-form-item>
          </a-col> -->
          <!-- <a-col
            :xs="24"
            :sm="24"
            :md="12"
            :lg="8"
            :xl="8"
          >
            <a-form-item
              :label="$t('salm.bth')"
              v-bind="formItemLayout"
            >
              <a-date-picker
                :disabled="formStatus"
                style="width: 100%"
                v-decorator="['bth', { rules: [{ required: false, message:$t('salm.placeholder.bth') }] }]"
                format="YYYY-MM-DD"
              />
            </a-form-item>
          </a-col>
          <a-col
            :xs="24"
            :sm="24"
            :md="12"
            :lg="8"
            :xl="8"
          >
            <a-form-item
              :label="$t('salm.dutInD')"
              v-bind="formItemLayout"
            >
              <a-date-picker
                :disabled="formStatus"
                style="width: 100%"
                v-decorator="['dutInD', { rules: [{ required: false, message:$t('salm.placeholder.dutInD') }] }]"
                format="YYYY-MM-DD"
              />
            </a-form-item>
          </a-col>
          <a-col
            :xs="24"
            :sm="24"
            :md="12"
            :lg="8"
            :xl="8"
          >
            <a-form-item
              :label="$t('salm.dutOtD')"
              v-bind="formItemLayout"
            >
              <a-date-picker
                :disabled="formStatus"
                style="width: 100%"
                v-decorator="['dutOtD', { rules: [{ required: false, message:$t('salm.placeholder.dutOtD') }] }]"
                format="YYYY-MM-DD"
              />
            </a-form-item>
          </a-col>
          <a-col
            :xs="24"
            :sm="24"
            :md="12"
            :lg="8"
            :xl="8"
          >
            <a-form-item
              :label="$t('salm.upSalNo')"
              v-bind="formItemLayout"
            >
              <my-selectList
                url="/basic/salm/page"
                :tableColumn="$Column.salm"
                :form="$Form.salm"
                :read-only="true"
                :data="Updata"
                name="upSalNo"
                :disabled="UpStatus"
                @choose="choose($event)"
                ref="selectList"
                v-decorator="['upSalNo', { rules: [{ required: false, message:$t('salm.placeholder.upSalName') }] }]"
                :placeholder="$t('salm.placeholder.upSalName')"
              ></my-selectList>
            </a-form-item>
          </a-col>
          <a-col
            :xs="24"
            :sm="24"
            :md="12"
            :lg="8"
            :xl="8"
          >
            <a-form-item
              :label="$t('salm.eMail')"
              v-bind="formItemLayout"
            >
              <a-input
                :disabled="formStatus"
                v-decorator="['email', { rules: [{ required: false, message:$t('salm.placeholder.eMail') }
                                                 ,{ max:100,message:$t('public.len')}
                ] }]"
                :placeholder="$t('salm.placeholder.eMail')"
              />
            </a-form-item>
          </a-col>
          <a-col
            :xs="24"
            :sm="24"
            :md="12"
            :lg="8"
            :xl="8"
          >
            <a-form-item
              :label="$t('salm.logon')"
              v-bind="formItemLayout"
            >
              <a-select
                :disabled="formStatus"
                :getPopupContainer=" triggerNode => {
                  return triggerNode.parentNode || document.body;
                }"
                style="width: 100%"
                v-decorator="['logon', { rules: [{ required: true, message:$t('salm.placeholder.logon') }] }]"
                :placeholder="$t('salm.placeholder.logon')"
              >
                <a-select-option value="T">{{ $t('public.T') }}</a-select-option>
                <a-select-option value="F">{{ $t('public.F') }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col
            :xs="24"
            :sm="24"
            :md="12"
            :lg="8"
            :xl="8"
          >
            <a-form-item
              :label="$t('salm.deproNo')"
              v-bind="formItemLayout"
            >
              <a-input
                :disabled="formStatus"
                v-decorator="['deproNo', { rules: [{ required: false, message:$t('salm.placeholder.deproName') }] }]"
                :placeholder="$t('salm.placeholder.deproName')"
              />
            </a-form-item>
          </a-col>
          <a-col
            :xs="24"
            :sm="24"
            :md="12"
            :lg="8"
            :xl="8"
          >
            <a-form-item
              :label="$t('salm.rem')"
              v-bind="formItemLayout"
            >
              <a-input
                :disabled="formStatus"
                type="textarea"
                v-decorator="['rem', { rules: [{ required: false, message:$t('salm.placeholder.rem') }] }]"
                :placeholder="$t('salm.placeholder.rem')"
              />
            </a-form-item>
          </a-col> -->
        </a-row>
      </a-form>
      <a-row :gutter="16">
        <a-col
          class="gutter-row"
          :span="12"
          style="text-align:right"
        >
          <a-button
            type="primary"
            v-if="modeType==='0'"
            :loading="loading"
            @click="handleOK()"
          >{{ $t('public.save') }}</a-button>
          <!-- &&this.permissions.authory_update -->
          <!-- &&this.permissions.authory_update -->
          <a-button
            type="primary"
            v-if="modeType==='1'"
            @click="handleMenuClick()"
          >{{ $t('public.edit') }}</a-button>
          <a-button
            type="primary"
            v-if="modeType==='2'"
            :loading="loading"
            @click="handleEdit()"
          >{{ $t('public.save') }}</a-button>
        </a-col>
        <a-col
          class="gutter-row"
          :span="12"
          style="text-align:left"
        >
          <a-button @click="onClose">{{ $t('public.cancel') }}</a-button>
        </a-col>
      </a-row>
    </a-drawer>
  </div>
</template>

<script>
import { list } from '@/api/admin/datasource'
import MySelectListwo from '@/components/MySelectListwo'
import { add,qcItmadd, edit, getTogto } from '@/api/salm'
import {authorysave,authoryupdate,authorytype } from '@/api/system/file'
import MySelectList from '@/components/MySelectList'
import moment from 'moment'
import { mapGetters } from 'vuex'
export default {
  name: 'SalmModal',
  components: {
    MySelectList,
    MySelectListwo
  },
  data () {
    return {
      bilType:'',
      title: '',
      loading: false,
      visible: false,
      formStatus: false,
      formIndex: false,
      UpStatus: false,
      data: '',
      Updata: '',
      row: {},
      save:false,
      del:false,
      modeType: '',
      form: this.$form.createForm(this),
      formItemLayout: {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 7 },
          md: { span: 8 },
          lg: { span: 7 }
        },
        wrapperCol: {
          xs: { span: 5 },
          sm: { span: 16 },
          md: { span: 17 },
          lg: { span: 16 }
        }
      },
      biltypedata:[],
      biltypedatatwo:[],
       selectedItems: [],
       bilName:'',
    }
  },
  computed: {
    ...mapGetters(['permissions'])
  },
  created () {
    // this.authtype()

  },
  methods: {
    biltychange(val){
      
         this.bilName = this.biltypedatatwo.filter(i => i.value === val)[0].label

    },
     onChange (checked) {
 
      // if (!checked) {
      //   this.del = 1
      // } else if (checked) {
      //   this.del = 0
      // }
    },
    authtype(){
      authorytype()
        .then(res => {
          this.biltypedata = res.data 
          let newArr = new Array
          for (var key in this.biltypedata) {
         
            var temp = {};
            if (this.biltypedata[key] != "") {
              temp.value = key;
              temp.label = this.biltypedata[key];
          
              newArr.push(temp);
            }
          }
          this.biltypedatatwo = newArr
          this.$forceUpdate()
        })
        .catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    disabledDate (current) {
      return current && current < moment().endOf('day')
    },
    choose (obj) {
       var map = {}
      if (obj.obj.name === 'salNo') {
        map[username] = obj.obj.data.username
      }
      this.form.setFieldsValue(map)
setTimeout(() => {
       this.form.setFieldsValue({
          'username': obj.obj.data.username,
        })
      }, 1)
    },
    
    // 取消关闭
    onClose () {
      this.form.resetFields()
      this.loading = false
      this.visible = false
      this.keyStatus = ''
      this.data = ''
      this.Updata = ''
      this.modeType = '0'
      this.row = {}
    },

      getDataSource () {
      list()
        .then(res => {
          this.selectedItems = res.data
           this.$forceUpdate()
        })
        .catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    create (model, obj) {
      this.authtype()
      this.formIndex = false
      this.title = model.title
      this.modeType = '0'
      this.visible = true
      this.formStatus = false
      
      setTimeout(() => {
       this.form.setFieldsValue({
          'bilType': '',
          'del': false,
          'save': false,
          'username': '',
        })
      }, 1)
    },
    // 点击编辑按钮
    handleMenuClick () {
      this.modeType = '2'
      this.title = this.$t('public.edit')
      this.formIndex = true
      this.UpStatus = false
      this.formStatus = false
    },
    // 双击弹出框
    edit (model, row) {
        this.authtype()
      this.title = model.title
      this.modeType = '1'
      this.row = row
    this.del=  row.del 
      this.save= row.save
      this.data= row.username
      this.formStatus = true
      this.UpStatus = true
      this.formIndex = true
      this.visible = true
      setTimeout(() => {
        this.form.setFieldsValue({
          'bilType': row.bilType,
          'username' : row.username,
        })
      }, 1)
    },

    // 添加确认
    handleOK () {
      
      this.form.validateFields((err, values) => {
    
      
        const Params = {
          ...values,
          version:'',
          save:this.save,
          del:this.del,
          bilName:this.bilName
          
        }
        // if (Params.salNo === Params.upSalNo) {
        //   this.$notification.error({
        //     message: this.$t('public.errorInfo'),
        //     description: this.$t('public.upLeavel')
        //   })
        //   err = true
        // }
        if (!err) {
          this.loading = true
           authorysave(Params)
            .then((res) => {
              if (res !== null) {
                getTogto().then(() => { })
                this.$emit('onOk')
                this.loading = false
                this.visible = false
                this.$message.success(this.$t('public.success'))
              }
            })
            .catch(err => this.requestFailed(err))
            .finally(() => {
              this.loading = false
            })
        }
      })
    },
    // 确认编辑
    handleEdit () {
      
      this.form.validateFields((err, values) => {
        this.bilName = this.biltypedatatwo.filter(i => i.value === values.bilType)[0].label
        const Params = {
          ...values,
          keyId: this.row.keyId,
          version: this.row.version,
           save:this.save,
          del:this.del,
           bilName:this.bilName
        }
        // if (Params.salNo === Params.upSalNo) {
        //   this.$notification.error({
        //     message: this.$t('public.errorInfo'),
        //     description: this.$t('public.upLeavel')
        //   })
        //   err = true
        // }
        if (!err) {
          this.loading = true
           authoryupdate(Params)
            .then((res) => {
              if (res !== null) {
                if(res.msg == 'fail'){
                   this.$message.warning(res.data)
                   return
                }else{
                   getTogto().then(() => { })
                    this.loading = false
                    this.$emit('onOk')
                    this.visible = false
                    this.$message.success(this.$t('public.success'))
                    this.data = ''
                }
              }
            })
            .catch(err => this.requestFailed(err))
            .finally(() => {
              this.loading = false
            })
        }
      })
    }

  }
}
</script>
