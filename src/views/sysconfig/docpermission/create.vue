<template>
	<div>
		<!-- 新增弹窗 -->
		<el-drawer :title="$t(title)" :visible.sync="drawer" size="40%" ref="drawerRef" class="JustMake-drawer">
			<div class="drawer-content">
				<el-form ref="drawerFormRef" :model="formData" label-position="right" label-width="100px" class="drawer-form">
					<el-row>
						<el-col v-for="(item, index) in formDataRaw" :key="index" :span="item.span || 12">
							<el-form-item :label="$t(item.field)" :title="$t(item.field)" :prop="getPropName(item.field)"
								:rules="item.rules">
								<Forms :data="item" v-model="formData[getPropName(item.field)]" @iconClick="handelCompEvent"
									@change="handleFormsChange" @selectListEvent="handleSelectListEvent"></Forms>
							</el-form-item>
						</el-col>
					</el-row>
				</el-form>
			</div>
			<div class="drawer-footer">
				<el-button @click="handleCancel">{{ $t('public.cancel') }}</el-button>
				<el-button type="primary" @click="handleCreate" :loading="drawerLoading">{{ drawerLoading ? 'Loading ...' :
					$t('public.sure')
				}}</el-button>
			</div>
		</el-drawer>
	</div>
</template>

<script>
import Forms from '@/components/Forms/index.vue'
import { authorysave, authoryupdate, authorytype } from '@/api/system/file'
export default {
	components: {
		Forms,
	},
	data() {
		return {
			selectedItems: null,
			api_save: authorysave,
			drawer: false,
			drawerLoading: false,
			title: 'public.add',
			status: '0', // 0 新增 1 编辑
			formData: {
				bilName: '',
			},
			currentRow: {},
			formDataRaw: [
				{
					field: 'perm.bilType', type: 'select',
					rules: [{ required: true, message: this.$t('perm.placeholder.bilType') }],
				},
				{
					field: 'perm.username', type: 'selectList',
					props: {
						url: "/admin/user/page",
						tableColumn: this.$Column.apsusertwo,
						form: this.$Form.apsuser
					},
					rules: [{ required: true, message: this.$t('perm.placeholder.username') }],
				},
				{ field: 'perm.save', type: 'switch',},
				{ field: 'perm.del', type: 'switch',},
			]
		}
	},
	mounted() {
		this.setValues()
	},
	watch: {
		status: function (newVal) {
			if (newVal === '0') {
				this.title = 'public.add'
				this.api_save = authorysave
			} else {
				this.title = 'public.edit'
				this.api_save = authoryupdate
			}
		}
	},
	methods: {
		handleVisible() {
			this.status = '0'
			this.formData = {}
			this.$nextTick(() => {
				this.$refs.drawerFormRef.clearValidate()
			})
			this.drawer = !this.drawer
		},
		getPropName(field) {
			return field.includes('.') ? field.split('.').pop() : field;
		},
		handleFormsChange(params) {
			if (params.code == 'perm.bilType') {
				this.formData['bilName'] = this.selectedItems.find(item => item.value == params.result).label
			}
		},
		handleSelectListEvent(param) {
			if (param.field === 'perm.username') {
				this.formData['username'] = param.obj.data.username
			}
		},
		handelCompEvent(params) {
			this.$emit('formItemIconClick', params)
		},
		handleCancel() {
			this.formData = {}
			this.$nextTick(() => {
				this.$refs.drawerFormRef.clearValidate()
			})
			this.drawer = false
		},
		async handleCreate() {
			if (this.drawerLoading) {
				return;
			}
			if (!this.api_save || typeof this.api_save !== 'function') {
				return this.$message.error('请配置 api_save 参数');
			}
			// 表单校验
			try {
				await this.$refs.drawerFormRef.validate();
			} catch (error) {
				return;
			}
			let result = null;
			try {
				this.drawerLoading = true;
				result = await this.api_save(this.formData)
				if (result.code == 0) {
					this.$message.success(this.$t('public.success'))
					this.$emit('refresh')
					this.drawer = false;
				}
			} catch (err) {
				console.error(err)
				this.$message.error(err || this.$t('public.error'));
			} finally {
				this.drawerLoading = false;
			}
		},
		handleEdit(row) {
			this.currentRow = row
			this.status = '1'
			this.drawer = true
			this.$nextTick(() => {
				this.formData = Object.assign({}, row) // 拷贝数据

			})
		},
		async getDataSource() {
			return await authorytype()
				.then(res => {
					this.selectedItems = Object.entries(res.data).map(([key, value]) => ({
						value: key,
						label: value
					}));
				})
				.catch(err => this.requestFailed(err))
				.finally(() => {
					this.loading = false
				})
		},
		async setValues() {
			const item = this.formDataRaw.find(item => item.field === 'perm.bilType')
			if (item) {
				await this.getDataSource()
				item.values = this.selectedItems
			}
		},

	},

}
</script>
