<template>
  <div>
    <a-drawer
      placement="right"
      :closable="false"
      @close="onClose"
      :visible="visible"
      :destroyOnClose="true"
      width="70%"
    >
      <template slot="title" >
        <span class="title-name">{{ title }}</span>
        <!-- <span v-if="modeType!=='0'" class="title-age">
        </span > -->
      </template>
      <a-form :form="form" ref="form">
        <a-row :gutter="16">
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <a-form-item :label="$t('job.jobName')" v-bind="formItemLayout">
              <a-input
                :disabled="formIndex"
                v-decorator="['jobName', { rules: [{ required: true, message:$t('job.placeholder.jobName') }] }]"
                :placeholder="$t('job.placeholder.jobName')"
              />
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <a-form-item :label="$t('job.jobGroup')" v-bind="formItemLayout">
              <a-input
                :disabled="formIndex"
                v-decorator="['jobGroup', { rules: [{ required: true, message:$t('job.placeholder.jobGroup') }] }]"
                :placeholder="$t('job.placeholder.jobGroup')"
              />
            </a-form-item>
          </a-col>

          <a-col
            :xs="24"
            :sm="24"
            :md="12"
            :lg="12"
            :xl="12"
          >
            <a-form-item :label="$t('job.jobExecuteStatus')" v-bind="formItemLayout">
              <a-select
                :disabled="formStatus"
                style="width: 100%"
                v-model="formEdit.jobLogStatus"
                :placeholder="$t('job.placeholder.jobExecuteStatus')"
              >
                <a-select-option
                  v-for="item in executeList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <a-form-item :label="$t('job.jobType')" v-bind="formItemLayout">
              <a-select
                :disabled="formStatus"
                style="width: 100%"
                v-model="formEdit.jobType"
                :placeholder="$t('job.placeholder.jobType')"
              >
                <a-select-option
                  v-for="item in typeList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <a-form-item :label="$t('job.className')" v-bind="formItemLayout">
              <a-input
                :disabled="formStatus"
                v-decorator="['className', { rules: [{ required: true, message:$t('job.placeholder.className') }] }]"
                :placeholder="$t('job.placeholder.className')"
              />
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <a-form-item :label="$t('job.methodName')" v-bind="formItemLayout">
              <a-input
                :disabled="formStatus"
                v-decorator="['methodName', { rules: [{ required: true, message:$t('job.placeholder.methodName') }] }]"
                :placeholder="$t('job.placeholder.methodName')"
              />
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <a-form-item :label="$t('job.methodParamsValue')" v-bind="formItemLayout">
              <a-input
                :disabled="formStatus"
                v-decorator="['methodParamsValue', { rules: [{ required: true, message:$t('job.placeholder.methodParamsValue') }] }]"
                :placeholder="$t('job.placeholder.methodParamsValue')"
              />
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <a-form-item :label="$t('job.cronExpression')" v-bind="formItemLayout">
              <j-cron :disabled="formStatus" ref="innerVueCron" v-decorator="['cronExpression', {'initialValue':'',rules: [{ required: true, message: $t('job.placeholder.cronExpression') }]}]" @change="setCorn"></j-cron>
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <a-form-item :label="$t('job.jobMessage')" v-bind="formItemLayout">
              <a-input
                :disabled="formStatus"
                v-decorator="['jobMessage', { rules: [{ required: true, message:$t('job.jobMessage') }] }]"
                :placeholder="$t('job.jobMessage')"
              />
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <a-form-item :label="$t('job.executeTime')" v-bind="formItemLayout">
              <a-input
                :disabled="formStatus"
                type="number"
                v-decorator="['executeTime', { rules: [{ required: true, message:$t('job.remark') }] }]"
                :placeholder="$t('job.executeTime')"
              />
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <a-form-item :label="$t('job.exceptionInfo')" v-bind="formItemLayout">
              <a-input
                :disabled="formStatus"
                type="exceptionInfo"
                v-decorator="['exceptionInfo', { rules: [{ required: true, message:$t('job.placeholder.remark') }] }]"
                :placeholder="$t('job.placeholder.remark')"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <a-row :gutter="16" >
        <a-col class="gutter-row" :span="12" style="text-align:right">
        </a-col>
        <a-col class="gutter-row" :span="12" style="text-align:left">
          <a-button @click="onClose" >{{ $t('public.cancel') }}</a-button>
        </a-col>
      </a-row>
    </a-drawer>
  </div>
</template>
<script>
import JCron from '@/components/jeecg/JCron.vue'
export default {
  name: 'JobModel',
  components: {
    JCron
  },
  data () {
    return {
      formStatus: false,
      formIndex: false,
      title: '',
      visible: false,
      modeType: '', // 添加为0，编辑为1
      formEdit: {
        jobType: '', // 类型
        misfirePolicy: '',
        jobLogStatus: '',
        jobExecuteStatus: ''
      },
      row: {},
      openId: '',
      confirmLoading: false,
      form: this.$form.createForm(this),
      formItemLayout: {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 7 },
          md: { span: 8 },
          lg: { span: 7 }
        },
        wrapperCol: {
          xs: { span: 5 },
          sm: { span: 16 },
          md: { span: 17 },
          lg: { span: 16 }
        } }
    }
  },
  props: {
    // eslint-disable-next-line vue/require-default-prop
    typeList: Array,
    // eslint-disable-next-line vue/require-default-prop
    policyList: Array,
    // eslint-disable-next-line vue/require-default-prop
    statusList: Array,
    // eslint-disable-next-line vue/require-default-prop
    executeList: Array
  },
  methods: {
    setCorn (data) {
      this.$nextTick(() => {
        this.form.setFieldsValue({
          'cronExpression': data
        })
      })
    },

    onClose () {
      this.visible = false
      this.formEdit = {}
      this.form.resetFields()
    },
    // 点击编辑按钮弹出框
    edit (model, record) {
      this.formStatus = true
      this.title = model.title
      this.openId = record.jobId
      this.modeType = '1'
      this.row = record
      this.formStatus = true
      this.formIndex = true
      this.visible = true

      this.formEdit = {
        jobType: record.jobType, // 类型
        misfirePolicy: record.misfirePolicy,
        jobExecuteStatus: record.jobExecuteStatus,
        jobLogStatus: record.jobLogStatus
      }
    
      this.$nextTick(() => {
        this.form.setFieldsValue({
          'jobName': record.jobName,
          'jobGroup': record.jobGroup,
          'executePath': record.executePath,
          'className': record.className,
          'methodName': record.methodName,
          'methodParamsValue': record.methodParamsValue,
          'cronExpression': record.cronExpression,
          'jobMessage': record.jobMessage,
          'exceptionInfo': record.exceptionInfo,
          'executeTime': record.executeTime,
          'remark': record.remark
        })
      })
    }
  }
}
</script>
