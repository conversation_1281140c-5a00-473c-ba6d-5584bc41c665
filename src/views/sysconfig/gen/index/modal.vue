<template lang="">
  <div>
    <a-modal
      :title="title"
      :visible="visible"
      width="50%"
      @cancel="handleCancel"
      :footer="null"
    >
      <a-form-model
        layout="horizontal"
        ref="ruleForm"
        :rules="rules"
        :model="form"
      >
        <a-row>
          <a-col :span="12">
            <a-form-model-item v-bind="formItemLayout" :label="$t('codeIndex.tableName')" prop="tableName">
              <a-input v-model="form.tableName" :disabled="formStatus" :placeholder="$t('codeIndex.placeholder.tableName')" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item v-bind="formItemLayout" :label="$t('codeIndex.packageName')" prop="packageName">
              <a-input v-model="form.packageName" :placeholder="$t('codeIndex.placeholder.packageName')" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item v-bind="formItemLayout" :label="$t('codeIndex.author')" prop="author">
              <a-input :disabled="formStatus" v-model="form.author" :placeholder="$t('codeIndex.placeholder.author')" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item v-bind="formItemLayout" :label="$t('codeIndex.moduleName')" prop="moduleName">
              <a-input :disabled="formStatus" v-model="form.moduleName" :placeholder="$t('codeIndex.placeholder.moduleName')" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item v-bind="formItemLayout" :label="$t('codeIndex.tablePrefix')" prop="tablePrefix">
              <a-input :disabled="formStatus" v-model="form.tablePrefix" :placeholder="$t('codeIndex.placeholder.tablePrefix')" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item v-bind="formItemLayout" :label="$t('codeIndex.comments')" prop="comments">
              <a-input :disabled="formStatus" v-model="form.comments" :placeholder="$t('codeIndex.placeholder.comments')" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item v-bind="formItemLayout" :label="$t('codeIndex.sync')" prop="sync">
              <a-select style="width: 100%" v-model="form.sync" :placeholder="$t('codeIndex.placeholder.sync')">
                <a-select-option value="true">{{ $t('public.T') }}</a-select-option>
                <a-select-option value="false">{{ $t('public.F') }}</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12" v-if="status==='0'" >
            <a-form-model-item v-bind="formItemLayout" :label="$t('codeIndex.style')" prop="style">
              <a-radio-group v-model="form.style" @change="onChange" >
                <a-radio v-for="(i,index) in styleList" :key="index" :value="i.value" :label="i.label">
                  {{ i.label }}
                </a-radio>
              </a-radio-group>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="16" >
          <a-col class="gutter-row" :span="12" style="text-align:right">
            <a-button type="primary" :loading="loading" @click="save()">{{ $t('codeIndex.generate') }}</a-button>
          </a-col>
          <a-col class="gutter-row" :span="12" style="text-align:left">
            <a-button type="primary" @click="reset()">{{ $t('public.reset') }}</a-button>
          </a-col>
        </a-row>
      </a-form-model>
    </a-modal>
  </div>
</template>
<script>
import { getStyle, handleDown } from '@/api/develop/codeIndex'
export default {
  data () {
    return {
      title: '',
      visible: false,
      formStatus: false,
      status: '',
      loading: false,
      formItemLayout: {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        }
      },
      styleList: [],
      form: {},
      name: '',
      rules: {
        style: [
          { required: true,
            message: this.$t('codeIndex.rules'),
            trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    // 选择风格
    onChange (e) {
      this.form.style = e.target.value
    },
    // 单个生成
    show (title, row, name) {
      this.name = name
      this.row = row
      this.title = title.title
      this.visible = true
      this.getStyle()
      this.form = {
        tableName: row.tableName
      }
    },
    // 批量生成
    all (row) {
      this.status = '0'
      this.visible = true
      this.getStyle()
    },
    async getStyle () {
      const res = await getStyle()
      this.styleList = res.data
    },
    save () {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.loading = true
          this.form.dsName = this.name
          handleDown(this.form)
            .then(() => {
              this.$emit('getList')
              this.loading = false
              this.visible = false
              this.$message.success(this.$t('public.success'))
            })
            .catch(() => {
              this.loading = false
              this.$message.error(this.$t('public.error'))
            })
        } else {
          this.loading = false
          this.$message.error(this.$t('public.error'))
        }
      })
    },
    reset () {
      this.form = {}
    },
    handleCancel () {
      this.status = ''
      this.visible = false
      this.form = {}
    }
  }

}
</script>
