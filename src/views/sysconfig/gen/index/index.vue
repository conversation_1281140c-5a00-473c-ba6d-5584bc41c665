<template>
  <a-card :bordered="false">
    <a-row :gutter="8">
      <a-col :span="24">
        <div class="table-page-search-wrapper">
          <a-form layout="inline">
            <a-row :gutter="48">
              <a-col :md="8" :sm="24">
                <a-form-item >
                  <a-select style="width: 100%" v-model="queryParam.dsName" :placeholder="$t('codeIndex.placeholder.source')" @change="getList">
                    <a-select-option
                      v-for="item in dataSourceList"
                      :key="item.id"
                      :label="item.name"
                      :value="item.name">
                      {{ item.name }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item >
                  <a-input v-model="queryParam.tableName" allow-clear :placeholder="$t('codeIndex.placeholder.tableName')" />
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <span class="table-page-search-submitButtons" >
                  <a-button type="primary" @click="getList" >{{ $t('public.query') }}</a-button>
                  <a-button style="margin-left: 8px" @click="reset">{{ $t('public.reset') }}</a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
        <vxe-toolbar custom>
          <template v-slot:buttons>
            <a-dropdown :trigger="['click']">
              <a-button>{{ $t('public.action') }}<a-icon type="down"/></a-button>
              <a-menu slot="overlay">
                <a-menu-item key="0">
                  <a @click="batch">{{ $t('codeIndex.batch') }}</a>
                </a-menu-item>
              </a-menu>
            </a-dropdown>
          </template>
        </vxe-toolbar>
        <vxe-table
          border
          resizable
          stripe
          highlight-current-row
          show-overflow
          highlight-hover-row
          export-config
          ref="xTable"
          :loading="loading"
          :data="tableData"
          :keyboard-config="{ isArrow: true }"
          :edit-config="{ trigger: 'click', mode: 'row' }"
        >
          <vxe-table-column type="checkbox" fixed="left" align="center" :width="50"></vxe-table-column>
          <vxe-table-column field="tableName" title="codeIndex.tableName" align="center"></vxe-table-column>
          <vxe-table-column field="tableComment" title="codeIndex.tableComment" align="center"></vxe-table-column>
          <vxe-table-column field="tableCollation" title="codeIndex.tableCollation" align="center"></vxe-table-column>
          <vxe-table-column field="engine" title="codeIndex.engine" align="center"></vxe-table-column>
          <vxe-table-column field="createTime" title="codeIndex.createTime" align="center"></vxe-table-column>
          <vxe-table-column title="public.action" align="center">
            <template v-slot="scope">
              <el-button size="mini" type="primary" @click="generate(scope.row)">{{ $t('codeIndex.generate') }}</el-button>
            </template>
          </vxe-table-column>
        </vxe-table>
        <vxe-pager
          :loading="loading"
          :current-page="tablePage.currentPage"
          :page-size="tablePage.pageSize"
          :total="tablePage.total"
          :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
          @page-change="handlePageChange"
        >
        </vxe-pager>
        <!-- 生成 -->
        <modal ref="modal" @getList="getList" />
      </a-col>
    </a-row>
  </a-card>
</template>
<script>
import { mapGetters } from 'vuex'
import { fetchList, fetchSelectDsList } from '@/api/develop/codeIndex'
import modal from './modal'
export default {
  name: 'TenantList',
  components: {
    modal
  },
  data () {
    return {
      queryParam: {},
      dataSourceList: [],
      tableData: [],
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      }
    }
  },
  computed: {
    ...mapGetters(['permissions'])
  },
  created () {
    this.getList()
    this.getdataSourceList()
  },
  methods: {
    // 分页
    handlePageChange ({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getList()
    },
    // 获取数据源
    async getdataSourceList () {
      const res = await fetchSelectDsList()
      this.dataSourceList = res.data
    },
    // 查询列表
    async getList () {
      this.loading = true
      try {
        const res = await fetchList(Object.assign(
          {
            current: this.tablePage.currentPage,
            size: this.tablePage.pageSize
          }, this.queryParam
        ))
        this.loading = false
        this.tableData = res.data.records
        this.tablePage.total = res.data.total
        this.tablePage.currentPage = res.data.current
      } catch (err) {
        this.loading = false
      }
    },
    // 重置搜索内容
    reset () {
      this.queryParam = {}
    },
    // 生成
    generate (row) {
      const name = this.queryParam.dsName
      this.$refs.modal.show({ title: this.$t('codeIndex.build') }, row, name)
    },
    // 批量生成
    batch (record) {
      const selectRecords = this.$refs.xTable.getCheckboxRecords()
      if (selectRecords.length > 1 || selectRecords.length < 11) {
        this.$refs.modal.all(record)
      } else {
        this.$message.warning(this.$t('codeIndex.list'))
      }
    }

  }
}
</script>
