<template>
  <div>
    <a-drawer
      placement="right"
      :closable="false"
      @close="onClose"
      :visible="visible"
      :destroyOnClose="true"
      width="70%"
    >
      <template slot="title" >
        <span class="title-name">{{ title }}</span>
      </template>
      <a-form-model
        layout="horizontal"
        ref="ruleForm"
        :rules="rules"
        :model="form"
      >
        <a-row>
          <a-col>
            <a-form-model-item v-bind="formItemLayout" :label="$t('source.name')" prop="name">
              <a-input v-model="form.name" :disabled="formStatus" :placeholder="$t('source.placeholder.name')" />
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-model-item v-bind="formItemLayout" :label="$t('source.url')" prop="url">
              <a-input :rows="4" type="textarea" :disabled="formStatus" v-model="form.url" :placeholder="$t('source.placeholder.url')" />
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-model-item v-bind="formItemLayout" :label="$t('source.username')" prop="username">
              <a-input :disabled="formStatus" v-model="form.username" :placeholder="$t('source.placeholder.username')" />
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-model-item v-bind="formItemLayout" :label="$t('source.password')" prop="password">
              <a-input :disabled="formStatus" v-model="form.password" :placeholder="$t('source.placeholder.password')" />
            </a-form-model-item>
          </a-col>

        </a-row>
      </a-form-model>
      <a-row :gutter="16" >
        <a-col class="gutter-row" :span="12" style="text-align:right">
          <a-button type="primary" v-if="status==='0'" @click="handleOK()" :loading="loading">{{ $t('public.save') }}</a-button>
          <a-button type="primary" v-if="status==='1'" @click="handleMenuClick()">{{ $t('public.edit') }}</a-button>
          <a-button type="primary" v-if="status==='2'" :loading="loading" @click="handleEdit()">{{ $t('public.save') }}</a-button>
        </a-col>
        <a-col class="gutter-row" :span="12" style="text-align:left">
          <a-button @click="onClose" >{{ $t('public.cancel') }}</a-button>
        </a-col>
      </a-row>
    </a-drawer>
  </div>
</template>
<script>
import { addObj, putObj } from '@/api/develop/datasource'
import { mapGetters } from 'vuex'

export default {
  name: 'SourceModel',
  data () {
    return {
      title: '',
      visible: false,
      loading: false,
      status: '', // 添加,修改状态0增加，1修改
      formStatus: false,
      row: {},
      form: {},
      formItemLayout: {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        }
      },
      rules: {
        username: [
          { required: true, message: this.$t('source.placeholder.username'), trigger: 'blur' }
        ],
        password: [
          { required: true, message: this.$t('source.placeholder.password'), trigger: 'blur' }
        ],
        url: [
          { required: true, message: this.$t('source.placeholder.url'), trigger: 'blur' }
        ],
        name: [
          { required: true, message: this.$t('source.placeholder.name'), trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    ...mapGetters(['permissions'])
  },

  created () {
  },
  methods: {
    onClose () {
      this.visible = false
      this.form = {}
    },

    create (model) {
      this.title = model.title
      this.status = '0'
      this.visible = true
      this.formStatus = false
    },
    // 点击编辑按钮
    handleMenuClick () {
      this.status = '2'
      this.title = this.$t('public.edit')
      this.formStatus = false
    },
    edit (model, row) {
      this.title = model.title
      this.status = '1'
      this.visible = true
      this.formStatus = true
      this.row = row
      this.form = {
        username: row.username,
        name: row.name,
        url: row.url,
        password: row.password
      }
    },
    // 确定
    handleOK (e) {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          this.loading = true
          addObj(this.form)
            .then(() => {
              this.loading = false
              this.$emit('getList')
              this.visible = false
              this.$message.success(this.$t('public.success'))
            })
            .catch(() => {
              this.loading = false
              this.$message.error(this.$t('public.error'))
            })
        } else {
          this.loading = false
          this.$message.error(this.$t('public.error'))
        }
      })
    },
    // 修改
    handleEdit () {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.loading = true
          const Params = {
            username: this.form.username,
            name: this.form.name,
            url: this.form.url,
            password: this.form.password,
            id: this.row.id
          }
          putObj(Params)
            .then(() => {
              this.$emit('getList')
              this.loading = false
              this.visible = false
              this.$message.success(this.$t('public.success'))
            })
            .catch(() => {
              this.loading = false
              this.$message.error(this.$t('public.error'))
            })
        } else {
          this.loading = false
          this.$message.error(this.$t('public.error'))
        }
      })
    }
  }
}
</script>
<style lang="less">
.title-age{
  float: right;
}
</style>
