<template>
  <div>
    <a-modal
      :title="$t('job.log')"
      :visible="visible"
      :confirmLoading="confirmLoading"
      @cancel="handleCancel"
      width="80%"
      :footer="null"
    >
      <vxe-table
        ref="xTable"
        :loading="loading"
        :data="tableData"
      >
        <!-- <vxe-table-column type="checkbox" fixed="left" align="center" :width="50"></vxe-table-column> -->
        <vxe-table-column width="100" field="jobName" fixed="left" title="job.jobName" align="center"></vxe-table-column>
        <vxe-table-column width="100" field="jobGroup" title="job.jobGroup" align="center"></vxe-table-column>
        <!-- <vxe-table-column width="100" field="jobStatus" title="job.jobStatus" align="center">
          <template slot-scope="scope">
            <a-tag color="blue" v-if="scope.row.jobStatus!=null" type="primary">{{ scope.row.jobStatus | state }}</a-tag>
          </template>
        </vxe-table-column> -->
        <vxe-table-column width="100" field="jobType" title="job.jobType" align="center">
          <template slot-scope="scope">
            <a-tag color="blue" type="primary">{{ scope.row.jobType | jobType }}</a-tag>
          </template>
        </vxe-table-column>
        <vxe-table-column width="100" field="executePath" title="job.executePath" align="center"></vxe-table-column>
        <vxe-table-column  width="100" field="className" title="job.className" align="center"></vxe-table-column>
        <vxe-table-column  width="100" field="methodName" title="job.methodName" align="center"></vxe-table-column>
        <vxe-table-column width="100" field="methodParamsValue" title="job.methodParamsValue" align="center"></vxe-table-column>
        <vxe-table-column width="100" field="cronExpression" title="job.cronExpression" align="center"></vxe-table-column>
        <vxe-table-column width="100" field="jobMessage" title="job.jobMessage" align="center"></vxe-table-column>
        <vxe-table-column width="100" field="executeTime" title="job.executeTime" align="center"></vxe-table-column>
        <vxe-table-column field="exceptionInfo" title="job.exceptionInfo" align="center"></vxe-table-column>
         <vxe-table-column width="180" field="createTime" title="记录时间" align="center"></vxe-table-column>
      </vxe-table>
      <vxe-pager
        :loading="loading"
        :current-page="tablePage.currentPage"
        :page-size="tablePage.pageSize"
        :total="tablePage.total"
        :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
        @page-change="handlePageChange"
      >
      </vxe-pager>
    </a-modal>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import { fetchList } from '@/api/daemon/job-manage-log'
import { STable } from '@/components'
export default {
  name: 'DictItem',
  components: {
    STable
  },
  data () {
    return {
      formEdit: '',
      visible: false,
      titled: '',
      confirmLoading: false,
      visibled: false,
      id: '',
      tableData: [],
      loading: false,
      modeType: '', // 添加为0，编辑为1
      form: this.$form.createForm(this),
      // eslint-disable-next-line vue/no-dupe-keys
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      }
    }
  },
  computed: {
    ...mapGetters(['permissions'])
  },
  created () {
  },
  filters: {
    state: function (val) {
      const systemType = {
        '1': '未发布',
        '2': '运行中',
        '3': '暂停'
      }
      return systemType[val]
    },
    jobExecuteStatus: function (val) {
      const systemType = {
        '0': '正常',
        '1': '异常'
      }
      return systemType[val]
    },
    jobType: function (val) {
      const systemType = {
        '1': 'java类',
        '2': 'spring bean',
        '3': 'Rest 调用',
        '4': 'jar',
        '9': '其他'
      }
      return systemType[val]
    },
    misfirePolicy: function (val) {
      const systemType = {
        '1': '错失周期立即执行',
        '2': '错失周期执行一次',
        '3': '下周期执行'
      }
      return systemType[val]
    }
  },
  methods: {
    // 获取刷新搜索列表
    getList () {
      this.loading = true
      fetchList(
        Object.assign(
          {
            jobId: this.openId,
            descs: 'create_time',
            current: this.tablePage.currentPage,
            size: this.tablePage.pageSize
          }
        )
      )
        .then(res => {
          this.tableData = res.data.records
          this.tablePage.total = res.data.total
          this.tablePage.currentPage = res.data.current
          this.loading = false
        })
        .catch(e => {
          this.loading = false
        })
    },
    // 分页
    handlePageChange ({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getList()
    },
    handleCancel () {
      // this.getList()
      this.visible = false
      this.form.resetFields()
      this.startTime = null
      this.endTime = null
      this.status = '0'
    },
    // 字典项弹出
    find (record) {
      this.openId = record.jobId
      this.visible = true
      this.getList()
    },
    // // 添加弹框取消
    handleCanceld () {
      this.visibled = false
    },
    // 编辑按钮
    updated (record) {
      this.titled = this.$t('public.edit')
      this.modeType = '1'
      this.visibled = true
      this.formEdit = true
      this.id = record.id
      this.$nextTick(() => {
        this.form.setFieldsValue({
          'type': record.type,
          'description': record.description,
          'remarks': record.remarks,
          'value': record.value,
          'label': record.label,
          'sort': record.sort
        })
      })
    }

  }
}
</script>
