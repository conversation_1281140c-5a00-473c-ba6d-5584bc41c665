<template>
  <a-card :bordered="false">
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="48">
          <a-col
            :md="8"
            :sm="24"
          >
            <a-form-item :label="$t('job.jobName')">
              <a-input
                v-model="queryParam.jobName"
                :placeholder="$t('job.placeholder.jobName')"
              />
            </a-form-item>
          </a-col>
          <a-col
            :md="8"
            :sm="24"
          >
            <a-form-item :label="$t('job.jobGroup')">
              <a-input
                v-model="queryParam.jobGroup"
                :placeholder="$t('job.placeholder.jobGroup')"
              />
            </a-form-item>
          </a-col>

          <template v-if="advanced">
            <a-col
              :md="8"
              :sm="24"
            >
              <a-form-item :label="$t('job.jobStatus')">
                <a-select
                  style="width: 100%"
                  v-model="queryParam.jobStatus"
                  :placeholder="$t('job.placeholder.jobStatus')"
                >
                  <a-select-option
                    v-for="item in statusList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >{{ item.label }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col
              :md="8"
              :sm="24"
            >
              <a-form-item :label="$t('job.jobExecuteStatus')">
                <a-select
                  style="width: 100%"
                  v-model="queryParam.jobExecuteStatus"
                  :placeholder="$t('job.placeholder.jobExecuteStatus')"
                >
                  <a-select-option
                    v-for="item in executeList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >{{ item.label }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </template>
          <a-col
            :md="!advanced && 8 || 24"
            :sm="24"
          >
            <span
              class="table-page-search-submitButtons"
              :style="advanced && { float: 'right', overflow: 'hidden' } || {} "
            >
              <a-button
                type="primary"
                @click="getList"
              >{{ $t('public.query') }}</a-button>
              <a-button
                style="margin-left: 8px"
                @click="reset"
              >{{ $t('public.reset') }}</a-button>
              <a
                @click="toggleAdvanced"
                style="margin-left: 8px"
              >
                {{ advanced ? $t('job.a') : $t('job.b') }}
                <a-icon :type="advanced ? 'up' : 'down'" />
              </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <vxe-toolbar custom>
      <template v-slot:buttons>
        <!-- <a-dropdown :trigger="['click']">
          <a-button>{{ $t('public.action') }}
            <a-icon type="down" />
          </a-button>
          <a-menu slot="overlay">
            <a-menu-item key="0">
              <a @click="dropdownMenuEvent('remove')">{{ $t('public.delete') }}</a>
            </a-menu-item>

          </a-menu>
        </a-dropdown> -->
        <a-button
          type="primary"
          style="margin-left:10px"
          @click="add"
          v-if="permissions.system_job_sys_job_add"
        >{{ $t('public.add') }}</a-button>
        <a-button
          v-if="permissions.system_job_sys_job_shutdown_job"
          type="primary"
          :loading="loading"
          style="background-color:#E6A23C;border-color:#E6A23C;margin-left:10px"
          @click="stop"
        >{{ $t('job.stop') }}</a-button>
        <a-button
          :loading="loading"
          v-if="permissions.system_job_sys_job_start_job"
          type="primary"
          style="background-color:#67C23A;border-color:#67C23A;margin-left:10px"
          @click="startJobs"
        >{{ $t('job.startJobs') }}</a-button>
        <a-button
          :loading="loading"
          v-if="permissions.system_job_sys_job_refresh_job"
          type="primary"
          style="background-color:#F56C6C;border-color:#F56C6C;margin-left:10px"
          @click="refresh"
        >{{ $t('job.refresh') }}</a-button>

      </template>
    </vxe-toolbar>
    <vxe-table
      border
      resizable
      stripe
      highlight-current-row
      show-overflow
      highlight-hover-row
      export-config
      ref="xTable"
      :loading="loading"
      :data="tableData"
      :keyboard-config="{ isArrow: true }"
       :scroll-y="{enabled: false}"
      @cell-dblclick="cellDBLClickEvent"
      :edit-config="{ trigger: 'click', mode: 'row' }"
    >
      <!-- <vxe-table-column
        type="checkbox"
        fixed="left"
        align="center"
        :width="50"
      ></vxe-table-column> -->
      <vxe-table-column
        field="jobName"
        fixed="left"
        title="job.jobName"
        align="center"
        :width="150"
      ></vxe-table-column>
      <vxe-table-column
        field="jobGroup"
        title="job.jobGroup"
        align="center"
        :width="150"
      ></vxe-table-column>
      <vxe-table-column
        field="jobStatus"
        title="job.jobStatus"
        align="center"
        :width="150"
      >
        <template slot-scope="scope">
          <a-tag
            v-if="scope.row.jobStatus!=null"
            color="blue"
            type="primary"
          >
            {{ $t('job.state.'+scope.row.jobStatus) }}
          </a-tag>
        </template>
      </vxe-table-column>
      <vxe-table-column
        field="jobExecuteStatus"
        title="job.jobExecuteStatus"
        align="center"
        :width="150"
      >
        <template slot-scope="scope">
          <a-tag
            v-if="scope.row.jobExecuteStatus!=null"
            color="blue"
            type="primary"
            :width="150"
          >{{ $t('job.jobExecuteStatus_1.'+scope.row.jobExecuteStatus) }}</a-tag>
        </template>
      </vxe-table-column>
      <vxe-table-column
        field="createTime"
        title="job.createTime"
        align="center"
        :width="150"
      ></vxe-table-column>
      <vxe-table-column
        field="startTime"
        title="job.startTime"
        align="center"
        :width="150"
      ></vxe-table-column>
      <vxe-table-column
        field="previousTime"
        title="job.previousTime"
        align="center"
        :width="150"
      ></vxe-table-column>
      <vxe-table-column
        field="nextTime"
        title="job.nextTime"
        align="center"
        :width="150"
      ></vxe-table-column>
      <vxe-table-column
        field="jobType"
        title="job.jobType"
        align="center"
        :width="150"
      >
        <template slot-scope="scope">
          <a-tag
            v-if="scope.row.jobType!=null"
            color="blue"
            type="primary"
          >{{ $t('job.jobType_1.'+scope.row.jobType) }}</a-tag>
        </template>
      </vxe-table-column>
      <vxe-table-column
        field="executePath"
        title="job.executePath"
        align="center"
        :width="150"
      ></vxe-table-column>
      <vxe-table-column
        field="className"
        title="job.className"
        align="center"
        :width="150"
      ></vxe-table-column>
      <vxe-table-column
        field="methodName"
        title="job.methodName"
        align="center"
        :width="150"
      ></vxe-table-column>
      <vxe-table-column
        field="methodParamsValue"
        title="job.methodParamsValue"
        align="center"
        :width="150"
      ></vxe-table-column>
      <vxe-table-column
        field="cronExpression"
        title="job.cronExpression"
        align="center"
        :width="150"
      ></vxe-table-column>
      <vxe-table-column
        field="misfirePolicy"
        title="job.misfirePolicy"
        align="center"
        :width="150"
      >
        <template slot-scope="scope">
          <a-tag
            v-if="scope.row.misfirePolicy!=null"
            color="blue"
            type="primary"
          >{{ $t('job.misfirePolicy_1.'+scope.row.misfirePolicy) }}</a-tag>
        </template>
      </vxe-table-column>
      <vxe-table-column
        field="remark"
        title="job.remark"
        align="center"
        :width="150"
      ></vxe-table-column>

    </vxe-table>
    <vxe-pager
      :loading="loading"
      :current-page="tablePage.currentPage"
      :page-size="tablePage.pageSize"
      :total="tablePage.total"
      :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
      @page-change="handlePageChange"
    >
    </vxe-pager>
    <!-- 弹出框 -->
    <job-Drawer
      ref="Drawer"
      :typeList="typeList"
      :policyList="policyList"
      @getList="getList"
      :statusList="statusList"
      :executeList="executeList"
    />
  </a-card>
</template>

<script>
import {
  fetchList,
  shutdownJobsRa,
  startJobsRa,
  refreshJobsRa
} from '@/api/daemon/job-manage'
import { getJobStatus, getJobExecute, getJobType, getPolicy } from '@/api/system/dict'
import { mapGetters } from 'vuex'
import jobDrawer from './jobDrawer'

export default {
  name: 'JobList',
  components: {
    jobDrawer
  },
  data () {
    return {
      // 高级搜索 展开/关闭
      advanced: false,
      selectedRowKeys: [],
      tableData: [],
      loading: false,
      // 查询参数
      queryParam: {},
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      statusList: [],
      executeList: [],
      typeList: [],
      policyList: [],
      openId: '',
      form: this.$form.createForm(this)
    }
  },

  created () {
    this.getStatusList()
    this.getExecuteList()
    this.getType()
    this.getPolicyList()
    this.getList()
  },
  computed: {
    ...mapGetters(['permissions'])
  },
  filters: {
    // state: function (val) {
    //   const systemType = {
    //     '1': '未发布',
    //     '2': '运行中',
    //     '3': '暂停'
    //   }
    //   return systemType[val]
    // },
  },
  methods: {
    // 获取状态列表
    getStatusList () {
      getJobStatus().then(res => {
        this.statusList = res.data
      })
    },
    // 获取执行状态
    getExecuteList () {
      getJobExecute().then(res => {
        this.executeList = res.data
      })
    },
    // 获取类型
    getType () {
      getJobType().then(res => {
        this.typeList = res.data
      })
    },
    getPolicyList () {
      getPolicy().then(res => {
        this.policyList = res.data
      })
    },
    // 是否展开查询条件
    toggleAdvanced () {
      this.advanced = !this.advanced
    },
    // 查询列表
    getList () {
      this.loading = true
      fetchList(
        Object.assign(
          {
            descs: 'create_time',
            jobName: this.queryParam.jobName,
            jobGroup: this.queryParam.jobGroup,
            jobStatus: this.queryParam.jobStatus,
            jobExecuteStatus: this.queryParam.jobExecuteStatus,
            current: this.tablePage.currentPage,
            size: this.tablePage.pageSize
          }
        )
      )
        .then(res => {
          this.tableData = res.data.records
          this.tablePage.total = res.data.total
          this.tablePage.currentPage = res.data.current
          this.loading = false
        })
        .catch(e => {
          this.loading = false
        })
    },
    // 分页
    handlePageChange ({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getList()
    },
    // 重置搜索内容
    reset () {
      this.queryParam = {}
    },
    // 点击添加按钮
    add () {
      this.$refs.Drawer.create({ title: this.$t('public.add') })
    },
    // 双击弹出编辑框
    cellDBLClickEvent ({ row }) {
      this.$refs.Drawer.edit({ title: this.$t('public.Detailed') }, row)
    },

    // 暂停全部
    stop () {
      const that = this
      this.$confirm({
        title: this.$t('public.del.title'),
        content: this.$t('job.content.stop'),
        okText: this.$t('public.sure'),
        okType: 'danger',
        cancelText: this.$t('public.cancel'),
        onOk () {
          shutdownJobsRa()
            .then(response => {
              const code = response.code
              if (code === 0) {
                that.getList()
                that.$message.success(that.$t('public.success'))
              } else {
                that.$message.error(that.$t('public.error'))
              }
            })
            .catch(() => {
              that.$message.error(that.$t('public.error'))
            })
        },
        onCancel () { }
      })
    },
    // 启动全部暂停定时任务
    startJobs () {
      const that = this
      this.$confirm({
        title: this.$t('public.del.title'),
        content: this.$t('job.content.startJobs'),
        okText: this.$t('public.sure'),
        okType: 'danger',
        cancelText: this.$t('public.cancel'),
        onOk () {
          startJobsRa()
            .then(response => {
              var code = response.code
              if (code === 0) {
                that.getList()
                that.$message.success(that.$t('public.success'))
              } else {
                that.$message.error(that.$t('public.error'))
              }
            })
            .catch(() => {
              that.$message.error(that.$t('public.error'))
            })
        },
        onCancel () { }
      })
    },
    // 启动全部暂停定时任务
    refresh () {
      const that = this
      this.$confirm({
        title: this.$t('public.del.title'),
        content: this.$t('job.content.refresh'),
        okText: this.$t('public.sure'),
        okType: 'danger',
        cancelText: this.$t('public.cancel'),
        onOk () {
          refreshJobsRa()
            .then(response => {
              var code = response.code
              if (code === 0) {
                that.getList()
                that.$message.success(that.$t('public.success'))
              } else {
                that.$message.error(that.$t('public.error'))
              }
            })
            .catch(() => {
              that.$message.error(that.$t('public.error'))
            })
        },
        onCancel () { }
      })
    }

  }
}
</script>
<style lang="less">
.ant-Drawer-confirm-body-wrapper {
  padding-top: 10px !important;
}
</style>
<style lang="less">
.title-age {
  float: right;
}
</style>
