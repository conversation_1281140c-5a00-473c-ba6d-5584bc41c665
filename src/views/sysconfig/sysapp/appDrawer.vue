<template>
  <div>
    <a-drawer placement="right" :closable="false" @close="onClose" :visible="visible" :destroyOnClose="true"
      width="70%">
      <template slot="title">
        <span class="title-name">{{ title }}</span>
      </template>
      <a-form-model ref="ruleForm" :model="form" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-form-model-item prop="version" ref="version" :label="$t('app.version')">
          <el-semver-input :disabled="formStatus" :prefix="false" v-model="form.version"
            :placeholder="$t('app.placeholder.version')" />
        </a-form-model-item>

        <a-form-model-item prop="type" ref="type" :label="$t('app.type')">
          <a-select :disabled="formStatus" allowClear :placeholder="$t('app.placeholder.type')" style="width: 100%"
            @change="change" v-model="form.type">
            <a-select-option v-for="item in list" :key="item.id" :label="item.label" :value="item.value">{{ item.label
              }}</a-select-option>
          </a-select>
        </a-form-model-item>

        <a-form-model-item ref="rem" :label="$t('app.rem')">
          <a-input :disabled="formStatus" v-model="form.rem" :placeholder="$t('app.placeholder.rem')" />
        </a-form-model-item>
        <a-form-model-item prop="file" ref="file" :label="$t('app.name')">
          <a-upload-dragger name="form.file" v-model="form.file" accept=".apk" :fileList="fileList"
            :remove="handleRemove" :beforeUpload="beforeUpload">
            <p class="ant-upload-drag-icon">
              <a-icon type="inbox" />
            </p>
            <p class="ant-upload-hint">
              {{ $t('app.placeholder.name') }}
            </p>
          </a-upload-dragger>
        </a-form-model-item>
      </a-form-model>
      <a-row :gutter="16">
        <a-col class="gutter-row" :span="12" style="text-align:right">
          <a-button type="primary" v-if="modeType==='0'" :loading="loading" @click="handleOK()">{{ $t('public.save')
            }}</a-button>
        </a-col>
        <a-col class="gutter-row" :span="12" style="text-align:left">
          <a-button @click="onClose">{{ $t('public.cancel') }}</a-button>
        </a-col>
      </a-row>
    </a-drawer>
  </div>
</template>

<script>
  import Vue from 'vue'
  import { addObj, getApptype } from '@/api/sysapp/sysapp'
  import { DEFAULT_TENANT_ID } from '@/store/mutation-types'
  import store from '@/store'
  import ElSemverInput from '@femessage/el-semver-input'
  import { mapGetters } from 'vuex'
  export default {
    name: 'APPModal',
    components: {
      ElSemverInput
    },
    data() {
      return {
        title: '',
        labelCol: { span: 5 },
        wrapperCol: { span: 17 },
        visible: false,
        loading: false,
        formStatus: false,
        formIndex: false,
        modeType: '',
        data: '',
        type: '',
        list: [],
        fileList: [],
        row: {},
        form: {},
        rules: {
          version: [
            { required: true, message: this.$t('app.placeholder.version'), trigger: 'blur' }
          ],
          type: [
            { required: true, message: this.$t('app.placeholder.type'), trigger: 'blur' }
          ],
          file: [
            { required: true, message: this.$t('app.placeholder.name'), trigger: 'blur' }
          ]
        }
      }
    },
    created() {
      const TENANT_ID = Vue.ls.get(DEFAULT_TENANT_ID)
      const token = store.getters.access_token
      this.headers = {
        Authorization: 'Bearer ' + token,
        TENANT_ID: TENANT_ID
      }
    },
    computed: {
      ...mapGetters(['permissions'])
    },
    methods: {

      handleRemove(file) {
        const index = this.fileList.indexOf(file)
        const newFileList = this.fileList.slice()
        newFileList.splice(index, 1)
        this.fileList = newFileList
      },
      beforeUpload(file) {
        if (file.type !== 'application/vnd.android.package-archive') {
          this.$message.error(this.$t('app.apk'))
          return false
        }
        if (this.fileList.length === 1) {
          this.$message.error(this.$t('app.one'))
          return false
        }
        this.fileList = [...this.fileList, file]
        this.form.file = file
        return false
      },

      change(e) {
        let type = ''
        this.list.forEach(i => {
          if (i.value === e) {
            type = i
          }
        })
        this.type = type
      },
      // // 取消
      onClose() {
        this.fileList = []
        this.fileIds = ''
        this.fileNames = ''
        this.fileURL = ''
        this.visible = false
        this.version = ''
        this.form = {}
        this.$refs.ruleForm.resetFields()
      },
      create(model, row) {
        getApptype().then((res) => {
          this.list = res.data
        })
        this.title = model.title
        this.modeType = '0'
        this.visible = true
        this.formIndex = false
        this.formStatus = false
        this.form = {}
        this.fileList = []
      },
      // 点击编辑按钮
      handleMenuClick() {
        this.modeType = '2'
        this.title = this.$t('public.edit')
        this.formIndex = true
        this.UpStatus = false
        this.formStatus = false
      },

      // 添加确认
      handleOK() {
        this.$refs.ruleForm.validate(valid => {
          if (valid) {
            const form = new FormData()
            form.append('file', this.form.file)
            form.append('version', this.form.version)
            form.append('rem', this.form.rem)
            form.append('type', this.form.type)
            form.append('label', this.type.label)
            this.uploading = true
            this.loading = true
            addObj(form)
              .then(() => {
                this.$emit('onOk')
                this.loading = false
                this.visible = false
                this.$message.success(this.$t('public.success'))
                this.$refs.ruleForm.resetFields()
                this.form = {}
                this.fileList = []
              })
              .catch((err) => {
                this.loading = false
                this.requestFailed(err)
              })
          } else {

            this.loading = false
            return false
          }
        })
      }

    }
  }
</script>
<style lang="less">
  .title-age {
    float: right;
  }
</style>