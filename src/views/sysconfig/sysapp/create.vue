<template>
  <div>
    <!-- 新增弹窗 -->
    <el-drawer :title="$t(title)" :visible.sync="drawer" size="40%" ref="drawerRef" class="JustMake-drawer">
      <div class="drawer-content">
        <el-form ref="drawerFormRef" :model="formData" label-position="right" label-width="100px" class="drawer-form">
          <el-row>
            <el-col v-for="(item, index) in formDataRaw" :key="index" :span="item.span || 12">
              <el-form-item :label="$t(item.field)" :title="$t(item.field)" :prop="getPropName(item.field)"
                :rules="item.rules">
                <Forms :data="item" v-model="formData[getPropName(item.field)]" @iconClick="handelCompEvent"
                  @change="handleFormsChange" @selectListEvent="handleSelectListEvent"></Forms>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item :label="$t('app.name')">
                <a-upload-dragger name="form.file" accept=".apk" :fileList="fileList" :remove="handleRemove"
                :beforeUpload="beforeUpload">
                <p class="ant-upload-drag-icon">
                  <a-icon type="inbox" />
                </p>
                <p class="ant-upload-hint">
                  {{ $t('app.placeholder.name') }}
                </p>
              </a-upload-dragger>
                </el-form-item>
             
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="drawer-footer">
        <el-button @click="handleCancel">{{ $t('public.cancel') }}</el-button>
        <el-button type="primary" @click="handleCreate" :loading="drawerLoading">{{ drawerLoading ? 'Loading ...' :
          $t('public.sure')
          }}</el-button>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import Forms from '@/components/Forms/index.vue'
import { addObj, getApptype, putObj, sysappversionupd } from '@/api/sysapp/sysapp'
export default {
  components: {
    Forms,
  },
  data() {
    return {
      api_save: addObj,
      drawer: false,
      drawerLoading: false,
      title: 'public.add',
      status: '0', // 0 新增 1 编辑
      formData: {},
      currentRow: {},
      fileList: [],
      formDataRaw: [
        {
          field: 'app.version', type: 'input',
          rules: [{ required: true, message: this.$t('app.placeholder.version') }],
        },
        {
          field: 'app.type', type: 'select', values: [],
          rules: [{ required: true, message: this.$t('app.placeholder.type') }],
        },
        {
          field: 'app.rem', type: 'input',
          rules: [{ required: true, message: this.$t('app.placeholder.rem') }],
        }, 

      ]
    }
  },
  mounted() {
    this.setValues()
  },
  watch: {
    status: function (newVal) {
      if (newVal === '0') {
        this.title = 'public.add'
        this.api_save = addObj
      } else {
        this.title = 'public.edit'
        this.api_save = sysappversionupd
      }
    }
  },
  methods: {
    handleVisible() {
      this.status = '0'
      this.formData = {}
      this.drawer = !this.drawer
    },
    getPropName(field) {
      return field.includes('.') ? field.split('.').pop() : field;
    },
    handleFormsChange(params) {
      this.$emit('formDataChange', params)
    },
    handleSelectListEvent(param) {
      this.$emit('selectListEvent', param)
    },
    handelCompEvent(params) {
      this.$emit('formItemIconClick', params)
    },
    handleCancel() {
      this.formData = {}
      this.$nextTick(() => {
        this.$refs.drawerFormRef.clearValidate()
      })
      this.drawer = false
    },
    async handleCreate() {
      if (this.drawerLoading) {
        return;
      }
      if (!this.api_save || typeof this.api_save !== 'function') {
        return this.$message.error('请配置 api_save 参数');
      }
      // 表单校验
      try {
        await this.$refs.drawerFormRef.validate();
      } catch (error) {
        return;
      }
      let result = null;
      try {
        this.drawerLoading = true;
        result = await this.api_save(this.formData)
        if (result.code == 0) {
          this.$message.success(this.$t('public.success'))
          this.$emit('refresh')
          this.drawer = false;
        }
      } catch (err) {
        console.error(err)
        this.$message.error(err || this.$t('public.error'));
      } finally {
        this.drawerLoading = false;
      }
    },
    handleEdit(row) {
      this.currentRow = row
      this.status = '1'
      this.drawer = true
      this.$nextTick(() => {
        this.formData = Object.assign({}, row) // 拷贝数据
      })
    },
    async setValues() {
      await getApptype().then((res) => {
        const item = this.formDataRaw.find(item => item.field === 'app.type')
        if (item) {
          item.values.push(...res.data)
        }
      })
    },
    handleRemove(file) {
      const index = this.fileList.indexOf(file)
      const newFileList = this.fileList.slice()
      newFileList.splice(index, 1)
      this.fileList = newFileList
    },
    beforeUpload(file) {
      if (file.type !== 'application/vnd.android.package-archive') {
        this.$message.error(this.$t('app.apk'))
        return false
      }
      if (this.fileList.length === 1) {
        this.$message.error(this.$t('app.one'))
        return false
      }
      this.fileList = [...this.fileList, file]
      this.formData.file = file
      return false
    },
  },

}
</script>
