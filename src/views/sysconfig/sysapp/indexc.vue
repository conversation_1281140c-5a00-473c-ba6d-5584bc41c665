<template>
  <a-card :bordered="false">
    <a-row :gutter="8">
      <a-col>
        <div class="table-page-search-wrapper">
          <a-form layout="inline">
            <a-row :gutter="48">
              <a-col :md="8" :sm="24">
                <a-form-item :label="$t('app.type')">
                  <a-select allowClear style="width: 100%" v-model="queryParam.type"
                    :placeholder="$t('app.placeholder.type')">
                    <a-select-option v-for="item in list" :key="item.id" :label="item.label" :value="item.value">{{
                      item.label }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item :label="$t('app.delFlag')">
                  <a-select allowClear v-model="queryParam.delFlag" :placeholder="$t('app.placeholder.delFlag')">
                    <a-select-option :value="null">全部</a-select-option>
                    <a-select-option :value="1">{{ $t('app.Flag1') }}</a-select-option>
                    <a-select-option :value="0">{{ $t('app.Flag2') }}</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <span class="table-page-search-submitButtons">
                  <a-button type="primary" @click="getList">{{ $t('public.query') }}</a-button>
                  <a-button style="margin-left: 8px" @click="reset">{{ $t('public.reset') }}</a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
        <vxe-toolbar custom>
          <template v-slot:buttons>
            <a-button style="margin-left:10px" type="primary" v-if="permissions.system_app_add" icon="plus"
              @click="add()">{{ $t('public.add') }}</a-button>
          </template>
        </vxe-toolbar>
        <vxe-table border resizable stripe highlight-apprent-row show-overflow highlight-hover-row export-config
          ref="xTable" :loading="loading" :data="tableData" :keyboard-config="{ isArrow: true }"
          @cell-dblclick="cellDBLClickEvent" :edit-config="{ trigger: 'click', mode: 'row' }">
          <!-- <vxe-table-column type="checkbox" fixed="left" align="center" :width="50"></vxe-table-column> -->
          <vxe-table-column field="name" fixed="left" title="app.name" align="center"></vxe-table-column>
          <vxe-table-column field="version" title="app.version" align="center"></vxe-table-column>
          <vxe-table-column field="rem" title="app.rem" align="center"></vxe-table-column>
          <vxe-table-column field="type" title="app.type" align="center">
            <template slot-scope="scope">
              <a-tag color="blue" type="primary" align="center" style=" margin-right: 0px">{{ scope.row.type | type
                }}</a-tag>
            </template>
          </vxe-table-column>
          <vxe-table-column field="delFlag" title="app.delFlag" align="center" width="150">
            <template slot-scope="scope">
              <a-select v-if="scope.row.edit" allowClear v-model="scope.row.delFlag" style="width:100%"
                :placeholder="$t('app.placeholder.delFlag')">
                <a-select-option :value="1">{{ $t('app.Flag1') }}</a-select-option>
                <a-select-option :value="0">{{ $t('app.Flag2') }}</a-select-option>
              </a-select>
              <a-tag v-else color="blue" type="primary" align="center" style=" margin-right: 0px;">{{
                $t('app.Flag.' + scope.row.delFlag) }}</a-tag>
              <!-- <a-select allowClear style="width: 100%" v-model="queryParam.type"
                :placeholder="$t('app.placeholder.type')">
                <a-select-option v-for="item in listtwo" :key="item.id" :label="item.label" :value="item.value">{{
                  item.label }}
                </a-select-option>
              </a-select> -->

            </template>
          </vxe-table-column>
          <vxe-table-column field="" title="操作" align="center">
            <template slot-scope="scope">
              <el-button v-if="!scope.row.edit" type="text" @click="handleChildrenEdit(scope)">编辑</el-button>
              <el-button v-if="scope.row.edit" type="text" @click="handleChildrenSave(scope)">保存</el-button>
              <el-button type="text" @click="handleChildrenDel(scope)" style="color:#f00;">删除</el-button>
              <!-- scope.row.edit && !scope.row.id &&  -->
            </template>
          </vxe-table-column>
        </vxe-table>
        <vxe-pager :loading="loading" :current-page="tablePage.currentPage" :apprent-page="tablePage.apprentPage"
          :page-size="tablePage.pageSize" :total="tablePage.total"
          :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
          @page-change="handlePageChange">
        </vxe-pager>
      </a-col>
      <!-- 添加弹出框 -->
      <app-Drawer ref="Drawer" @onOk="onOk" />
    </a-row>
  </a-card>
</template>
<script>
import { fetchList, getApptype, sysappversionupd, sysappversiondel } from '@/api/sysapp/sysapp'
import appDrawer from './appDrawer'
import { mapGetters } from 'vuex'
var that
export default {
  name: 'AppList',
  components: {
    appDrawer
  },
  data() {
    return {
      list: [],
      listtwo: [
        { label: '全部', value: '' },
        { label: '1', value: '' },
        { label: '0', value: '' },
      ],
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      loading: false,
      tableData: [],
      // 查询参数
      queryParam: {
        delFlag: 1
      }
    }
  },
  beforeCreate: function () {
    that = this
  },
  filters: {
    type: function (val) {
      var value
      that.list.forEach(i => {
        if (i.value === val) {
          value = i.label
        }
      })
      return value
    }
  },
  created() {
    this.getList()
    getApptype().then((res) => {
      this.list = res.data
    })
  },
  computed: {
    ...mapGetters(['permissions'])
  },
  methods: {
    handleChildrenDel(scope) {
      sysappversiondel({
        id: scope.row.id,
        type: scope.row.type,
      })
        .then(res => {
          this.$message.success(res.msg)
          this.getList()
        })
        .catch(err => {
          this.requestFailed(err)
        })
    },
    handleChildrenEdit(scope) {
      scope.row.edit = true
      this.$forceUpdate()
    },
    handleChildrenSave(scope) {
      sysappversionupd(
        {
          id: scope.row.id,
          type: scope.row.type,
          delFlag: scope.row.delFlag
        }
      )
        .then(() => {
          this.loading = false
          this.getList()
          this.$message.success('保存成功')
        })
        .catch((err) => {
          this.loading = false
          this.requestFailed(err)
        })
    },
    getList() {
      this.loading = true
      let delFlagvalue
      if (this.queryParam.delFlag == null) {
        delFlagvalue = 666
      } else {
        delFlagvalue = this.queryParam.delFlag
      }
      fetchList(
        Object.assign(
          // this.queryParam,
          {
            current: this.tablePage.currentPage,
            size: this.tablePage.pageSize,
            delFlag: delFlagvalue,
            type: this.queryParam.type
          },
        )
      )
        .then(res => {
          this.tableData = res.data.records
          this.tableData.forEach(item => {
            item.edit = false
          })
          this.tablePage.total = res.data.total
          this.tablePage.currentPage = res.data.current
          this.loading = false
        })
        .catch(e => {
          this.loading = false
        })
    },
    // 分页
    handlePageChange({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getList()
    },
    // 重置搜索内容
    reset() {
      this.queryParam = {}
      this.getList()
    },
    // 新增
    add() {
      this.$refs.Drawer.create({ title: this.$t('public.add') })
    },
    // 双击弹出编辑框
    cellDBLClickEvent({ row }) {
      this.$refs.Drawer.edit({ title: this.$t('public.Detailed') }, row)
    },
    onOk() {
      this.getList()
    }
  }
}
</script>
<style lang="less"></style>