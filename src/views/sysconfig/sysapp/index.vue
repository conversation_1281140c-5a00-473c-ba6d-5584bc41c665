<template>
  <div class="layout">
    <vTable ref="vTable" v-bind="vTableProps" @toolbarClick="handleToolbarClick" @cellDblclick="handleDblclick">
    </vTable>
    <CreateDialog ref="createRef" @refresh="handleSubmit"></CreateDialog>
  </div>
</template>
<script>
import vTable from '@/components/amtxts/vTable/vGrid.vue'
import { fetchList, getApptype, sysappversionupd, sysappversiondel } from '@/api/sysapp/sysapp'
import CreateDialog from './create.vue'
export default {
  name: 'AppList',
  components: {
    vTable, CreateDialog
  },
  data() {
    return {
      formData: [],
      vTableProps: {
        delete_key: "$ITEM",
        api_find: fetchList,
        api_delete: sysappversiondel,
        toolbarItems: [
          { label: 'APP管理', value: 'title' },
        ],
        formDataRaw: [
          { field: 'app.type', type: 'select', values:[],
          placeholder: this.$t('app.placeholder.type') },
          {
            field: 'app.delFlag', type: 'select',
            values: [
              {
                label: 'app.all', value: null
              },
              {
                label: 'app.Flag1', value: '1'
              },
              {
                label: 'app.Flag0', value: '0'
              }
            ]
          },
        ],
        tableColumn: [
          { field: "name", title: "app.name", },
          { field: "version", title: "app.version", },
          { field: 'rem', title: 'app.rem', },
          { field: 'type', title: 'app.type',
            slots: {
              default: ({ row }) => {
                return this.$createElement('el-tag', {
                  domProps: {
                    innerHTML: row.type == 0 ? "MES" : (row.type == 1 ? "barcode" : "mobileBarcode"),
                  },
                });
              },
            }
          },
          {
            field: 'delFlag', title: 'app.delFlag',
            slots: {
              default: ({ row }) => {
                return this.$createElement('el-tag', {
                  props: {
                    type: row.delFlag == 1 ? 'primary' : 'danger',
                  },
                  domProps: {
                    innerHTML: row.delFlag == 0 ? "无效" : "有效",
                  },
                });
              },
            },
          },
        ],
      }
    }
  },
  created() {
    this.setValues()
  },
  methods: {
    handleToolbarClick(params) {
      switch (params.code) {
        case 'create':
          this.$refs.createRef.handleVisible();
          break;
        default:
      }
    },
    handleIconClick(params) {
    },
    handleFormDataChange(params) {
    },
    handleChoose(params) {
    },
    handleSelectListEvent(params) {
    },
    async setValues(){
      await getApptype().then((res) => {
        const item = this.vTableProps.formDataRaw.find(item => item.field === 'app.type')
        if(item){
          item.values.push(...res.data) 
        }
    })
    },
    handleDblclick(param){
      this.$refs.createRef.handleEdit(param);
    },
    handleSubmit(){
      this.$refs.vTable.handleGet();
    }

  },
}
</script>
