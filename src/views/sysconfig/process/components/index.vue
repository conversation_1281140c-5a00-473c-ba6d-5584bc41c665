<template>
  <div>
    <a-row width="100%">
      <a-col :span="24">
        <a-input-search
          :autoFocus="autoFocus"
          :allowClear="allowClear"
          :defaultActiveFirstOption="defaultActiveFirstOption"
          :readOnly="readOnly"
          :dropdownMatchSelectWidth="dropdownMatchSelectWidth"
          :maxTagCount="maxTagCount"
          :maxTagTextLength="maxTagTextLength"
          :type="type"
          :multiple="multiple"
          :showArrow="showArrow"
          :size="size"
          :placeholder="placeholder"
          :value="value"
          optionFilterProp="children"
          :getPopupContainer="
            triggerNode => {
              return triggerNode.parentNode || document.body
            }
          "
          :filterOption="filterOption"
          @search="onClick"
          v-model="value"
        >
          <a-button
            ref="btn"
            :disabled="disabled"
            slot="enterButton"
          >
            <a-icon type="database" />
          </a-button>
          <!-- <app-button
            ref="btn"
            slot="append"
            :type="type"
            :disabled="disabled"
            @onClick="onClick"
          ></app-button > -->
        </a-input-search>
      </a-col>
    </a-row>
    <!-- 添加弹出框 -->
    <select-modal
      ref="modal"
      @onOk="onOk"
      @touch="touch($event)"
      :urls="urls"
      :tableForm="tableForm"
      :tableColumn="tableColumn"
      :multiple="multiple"
    />
  </div>
</template>
<script>
import selectModal from './selectModal'

export default {
  name: 'SelectList',
  components: {
    selectModal
  },
  data () {
    return {
      value: '',
      urls: '',
      param: Array,
      column: Array,
      tableForm: Object
    }
  },
  watch: {
    data: {
      handler (val) {
        this.$nextTick(() => {
          this.value = this.data
        })
      },
      // 监听到数据变化时立即调用
      immediate: true
    }
  },
  props: {
    // eslint-disable-next-line vue/require-default-prop
    placeholder: String,
    url: {
      type: String,
    },
    multiple: {
      type: Boolean,
      default: false
    },
    // eslint-disable-next-line vue/require-default-prop
    params: Array,
    // eslint-disable-next-line vue/require-default-prop
    data: {
      type: String
    },
    // eslint-disable-next-line vue/require-default-prop
    // value: String,
    allowClear: Boolean,
    autoClearSearchValue: Boolean,
    autoFocus: Boolean,
    defaultActiveFirstOption: Boolean,
    // eslint-disable-next-line vue/require-default-prop
    defaultValue: [String, Number, Array],
    readOnly: {
      type: Boolean,
      default: true
    },
    disabled: Boolean,
    dropdownMatchSelectWidth: Boolean,
    // eslint-disable-next-line vue/require-default-prop
    maxTagCount: Number,
    // eslint-disable-next-line vue/require-default-prop
    maxTagTextLength: Number,
    // eslint-disable-next-line vue/require-default-prop
    type: String,
    showArrow: Boolean,
    // eslint-disable-next-line vue/require-default-prop
    size: String,
    // eslint-disable-next-line vue/require-default-prop
    form: Object,
    // eslint-disable-next-line vue/require-default-prop
    name: String,
    // eslint-disable-next-line vue/require-default-prop
    // eslint-disable-next-line vue/require-default-prop
    tableColumn: Array
  },
  created () {
    this.urls = this.url
    this.column = this.tableColumn
    this.tableForm = this.form
  },

  methods: {

    touch (itm) {
      this.value = itm.name
      const obj = {
        name: this.name,
        id: itm.id,
        value: itm.name,
        data: itm.data
      }
      this.$emit('choose', { obj })
    },
    filterOption (input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
    },
    onClick () {
      this.$refs.modal.create({ title: '' })
    },
    onOk () {
      this.getSelect()
    }
  }
}
</script>
<style lang="less">
.el-input-group__append {
  padding: 0px !important;
  border: 0px !important;
}
</style>
