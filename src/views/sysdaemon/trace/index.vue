<template>
  <a-card :bordered="false">
    <a-row :gutter="8">
      <a-col :span="24">
        <div class="table-page-search-wrapper">
          <a-form layout="inline">
            <a-row :gutter="48">
              <a-col :md="6" :sm="24">
                <a-form-item :label="$t('trace.jobName')">
                  <a-input v-model="queryParam.traceName" :placeholder="$t('trace.jobName')" />
                </a-form-item>
              </a-col>
              <a-col :md="6" :sm="24">
                <a-form-item :label="$t('trace.state')">
                  <a-select
                    style="width: 100%"
                    v-model="queryParam.state"
                    :placeholder="$t('trace.state')"
                  >
                    <a-select-option value="TASK_STAGING">准备中</a-select-option>
                    <a-select-option value="TASK_RUNNING">执行中</a-select-option>
                    <a-select-option value="TASK_FINISHED">已完成</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :md="6" :sm="24">
                <span class="table-page-search-submitButtons" >
                  <a-button type="primary" @click="getList" >{{ $t('public.query') }}</a-button>
                  <a-button style="margin-left: 8px" @click="reset">{{ $t('public.reset') }}</a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
        <div>
          <vxe-table
            border
            resizable
            stripe
            highlight-current-row
            show-overflow
            highlight-hover-row
            export-config
            ref="xTable"
            :loading="loading"
            :data="tableData"
            @cell-dblclick="cellDBLClickEvent"
            :keyboard-config="{ isArrow: true }"
            :edit-config="{ trigger: 'click', mode: 'row' }"
          >
            <vxe-table-column field="jobName" fixed="left" title="trace.jobName" align="center"></vxe-table-column>
            <vxe-table-column field="originalTaskId" title="trace.originalTaskId" align="center"></vxe-table-column>
            <vxe-table-column field="taskId" title="trace.taskId" align="center"></vxe-table-column>
            <vxe-table-column field="slaveId" title="trace.slaveId" align="center"></vxe-table-column>
            <vxe-table-column field="source" title="trace.source" align="center"></vxe-table-column>
            <vxe-table-column field="executionType" title="trace.executionType" align="center"></vxe-table-column>
            <vxe-table-column field="shardingItem" title="trace.shardingItem" align="center"></vxe-table-column>
            <vxe-table-column field="state" title="trace.state" align="center"></vxe-table-column>
            <vxe-table-column field="message" title="trace.message" align="center"></vxe-table-column>
            <vxe-table-column field="creationTime" title="trace.creationTime" align="center"></vxe-table-column>
          </vxe-table>
          <vxe-pager
            :loading="loading"
            :current-page="tablePage.currentPage"
            :page-size="tablePage.pageSize"
            :total="tablePage.total"
            :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
            @page-change="handlePageChange"
          >
          </vxe-pager>
        </div>
      </a-col>
    </a-row>
    <!-- 弹出框 -->
    <log-Drawer
      ref="Drawer"
      @getList="getList"/>
  </a-card>

</template>
<script>
import { mapGetters } from 'vuex'
import { fetchList } from '@/api/sysdaemon/trace'
// import { gettraceStatus, gettraceExecute, gettraceType, getPolicy } from '@/api/system/dict'

import logDrawer from './logDrawer'
export default {
  name: 'DictItem',
  components: {
    logDrawer
  },
  data () {
    return {
      formEdit: '',
      visible: false,
      titled: '',
      confirmLoading: false,
      visibled: false,
      id: '',
      queryParam: {},

      tableData: [],
      loading: false,

      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      }
    }
  },
  computed: {
    ...mapGetters(['permissions'])
  },
  created () {
    // this.getList()
  },

  methods: {

    // 获取刷新搜索列表
    getList () {
      this.loading = true
      fetchList(
        Object.assign(
          {
            current: this.tablePage.currentPage,
            size: this.tablePage.pageSize
          }, this.queryParam
        )
      )
        .then(res => {
          this.tableData = res.data.records
          this.tablePage.total = res.data.total
          this.tablePage.currentPage = res.data.current
          this.loading = false
        })
        .catch(e => {
          this.loading = false
        })
    },
    // 分页
    handlePageChange ({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getList()
    },
    // 重置搜索内容
    reset () {
      this.queryParam = {}
    },
    // 双击弹出编辑框
    cellDBLClickEvent ({ row }) {
      this.$refs.Drawer.edit({ title: this.$t('public.Detailed') }, row)
    }

  }
}
</script>
