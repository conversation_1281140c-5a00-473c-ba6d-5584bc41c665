<template>
  <div>
    <vxe-table
      border
      resizable
      stripe
      highlight-current-row
      show-overflow
      highlight-hover-row
      export-config
      ref="xTable"
      :loading="loading"
      :data="tableData"
      :keyboard-config="{ isArrow: true }"
      :edit-config="{ trigger: 'click', mode: 'row' }"
    >
      <vxe-table-column field="model" fixed="left" title="tx.model" align="center"></vxe-table-column>
      <vxe-table-column field="uniqueKey" title="tx.uniqueKey" align="center"></vxe-table-column>
      <vxe-table-column field="ipAddress" title="tx.ipAddress" align="center"></vxe-table-column>
      <vxe-table-column field="channelName" title="tx.channelName" align="center"></vxe-table-column>

    </vxe-table>
  </div>
</template>
<script>
import axios from '@/router/axios'
export default {
  name: 'TxList',
  data () {
    return {
      loading: false,
      tableData: []
    }
  },
  created () {
    this.getList()
  },
  methods: {
    getList () {
      this.loading = true
      this.tableData = []
      axios({
        url: '/tx/admin/onlines',
        method: 'get'
      }).then(res => {
        this.loading = false
        this.tableData = res
      })
    }
  }
}
</script>
