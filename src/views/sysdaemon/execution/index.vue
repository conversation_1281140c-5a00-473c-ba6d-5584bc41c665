<template>
  <a-card :bordered="false">
    <a-row :gutter="8">
      <a-col :span="24">
        <div>
          <vxe-table
            border
            resizable
            stripe
            highlight-current-row
            show-overflow
            highlight-hover-row
            export-config
            ref="xTable"
            :loading="loading"
            :data="tableData"
            @cell-dblclick="cellDBLClickEvent"
            :keyboard-config="{ isArrow: true }"
            :edit-config="{ trigger: 'click', mode: 'row' }"
          >
            <!-- <vxe-table-column type="checkbox" fixed="left" align="center" :width="50"></vxe-table-column> -->
            <vxe-table-column field="id" fixed="left" title="exe.id" align="center"></vxe-table-column>
            <vxe-table-column field="jobName" title="exe.jobName" align="center"></vxe-table-column>
            <vxe-table-column field="taskId" title="exe.taskId" align="center"></vxe-table-column>
            <vxe-table-column field="hostname" title="exe.hostname" align="center"></vxe-table-column>
            <vxe-table-column field="ip" title="exe.ip" align="center"></vxe-table-column>
            <vxe-table-column field="shardingItem" title="exe.shardingItem" align="center"></vxe-table-column>
            <vxe-table-column field="executionSource" title="exe.executionSource" align="center"></vxe-table-column>
            <vxe-table-column field="failureCause" title="exe.failureCause" align="center"></vxe-table-column>
            <vxe-table-column field="isSuccess" title="exe.isSuccess" align="center"></vxe-table-column>
            <vxe-table-column field="startTime" title="exe.startTime" align="center"></vxe-table-column>
            <vxe-table-column field="completeTime" title="exe.completeTime" align="center"></vxe-table-column>
          </vxe-table>
          <vxe-pager
            :loading="loading"
            :current-page="tablePage.currentPage"
            :page-size="tablePage.pageSize"
            :total="tablePage.total"
            :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
            @page-change="handlePageChange"
          >
          </vxe-pager>
        </div>
      </a-col>
    </a-row>
    <!-- 弹出框 -->
    <log-Drawer
      ref="Drawer"
      @getList="getList"
    />
  </a-card>

</template>
<script>
import { mapGetters } from 'vuex'
import { fetchList } from '@/api/sysdaemon/execution'

import logDrawer from './logDrawer'
export default {
  name: 'DictItem',
  components: {
    logDrawer
  },
  data () {
    return {
      formEdit: '',
      visible: false,
      titled: '',
      confirmLoading: false,
      visibled: false,
      id: '',
      tableData: [],
      queryParam: {},
      loading: false,
      modeType: '', // 添加为0，编辑为1
      form: this.$form.createForm(this),
      // eslint-disable-next-line vue/no-dupe-keys
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      }
    }
  },
  computed: {
    ...mapGetters(['permissions'])
  },
  created () {
    // this.getList()
  },

  methods: {

    // 获取刷新搜索列表
    getList () {
      this.loading = true
      fetchList(
        Object.assign(
          {
            descs: 'create_time',
            current: this.tablePage.currentPage,
            size: this.tablePage.pageSize
          }, this.queryParam
        )
      )
        .then(res => {
          this.tableData = res.data.records
          this.tablePage.total = res.data.total
          this.tablePage.currentPage = res.data.current
          this.loading = false
        })
        .catch(e => {
          this.loading = false
        })
    },
    // 分页
    handlePageChange ({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getList()
    },

    // 双击弹出编辑框
    cellDBLClickEvent ({ row }) {
      this.$refs.Drawer.edit({ title: this.$t('public.Detailed') }, row)
    }

  }
}
</script>
