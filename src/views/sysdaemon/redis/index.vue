<template>
  <div>
    <a-row :span="24">
      <a-col :span="8">
        <ve-pie
          :title="chartTitle"
          :data="chartData"
          :settings="chartSettings"
          height="500px"
        ></ve-pie>
      </a-col>

      <a-col
        :span="14"
        :offset="2"
      >
        <li
          class="box"
          v-for="(i,index) in infomationCumulate.data"
          :key="index"
        >
          <a
            href="javascript:void(0);"
            class="item"
          >
            <span class="count">{{ i.count }}</span>
            <div>——</div>
            <div>{{ i.title }}</div>
          </a>
        </li>
      </a-col>
    </a-row>
    <a-row
      :span="24"
      style="margin-top:50px"
    >
      <a-col :span="12">
        <v-chart :options="memoryCumulate" />
      </a-col>
      <a-col :span="12">
        <v-chart :options="dbSizeCumulate" />
      </a-col>
    </a-row>
  </div>
</template>

<script>
import Vue from 'vue'
import VCharts from 'v-charts'
import ECharts from 'vue-echarts'
import 'echarts/lib/chart/line'
import 'echarts/lib/component/legend'
import 'echarts/lib/component/title'
import 'echarts/lib/component/tooltip'
import 'echarts/lib/component/toolbox'
import 'echarts/lib/component/markPoint'
import 'echarts/lib/component/markLine'
import 'echarts/lib/chart/pie'
import { fetchInfo } from '@/api/sysdaemon/redis'
Vue.use(VCharts)
export default {
  components: {
    'v-chart': ECharts
  },

  data () {
    this.chartSettings = {
      radius: 150,
      offsetY: 260,
      selectedMode: 'single',
      legendLimit: '0'
    }
    this.chartTitle = {
      text: '命令统计',
      textStyle: {
        color: '#235894',
        left: '30%'
      }
    }
    return {
      chartData: {
        columns: ['name', 'value'],
        rows: []
      },
      activeName: 'first',
      timer: null,
      memoryCumulate: {
        color: ['#409EFF', '#FFCC99'],
        title: {
          text: 'Redis 内存情况'
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['分配内存', '消耗峰值']
        },
        toolbox: {
          feature: {
            saveAsImage: {}
          }
        },
        xAxis: {
          type: 'category',
          data: []
        },
        yAxis: {
          type: 'value',
          axisLaba: {
            formatter: '{value} M'
          }
        },
        series: [{
          name: '分配内存',
          data: [],
          type: 'line',
          itemStyle: {
            normal: {
              lineStyle: {
                width: 3// 设置线条粗细
              }
            }
          },
          markPoint: {
            data: [
              { type: 'max', name: '最大值' },
              { type: 'min', name: '最小值' }
            ]
          },
          markLine: {
            data: [
              { type: 'average', name: '平均值' }
            ]
          }
        }, {
          name: '消耗峰值',
          data: [],
          type: 'line',
          itemStyle: {
            normal: {
              lineStyle: {
                color: '#ffcc99',
                width: 3// 设置线条粗细
              }
            }
          },
          markPoint: {
            data: [
              { type: 'max', name: '最大值' },
              { type: 'min', name: '最小值' }
            ]
          },
          markLine: {
            data: [
              { type: 'average', name: '平均值' }
            ]
          }
        }]
      },
      dbSizeCumulate: {
        color: ['#409EFF'],
        title: {
          text: 'Redis Key 数量'
        },
        tooltip: {
          trigger: 'axis'
        },
        toolbox: {
          feature: {
            saveAsImage: {}
          }
        },
        xAxis: {
          type: 'category',
          data: []
        },
        yAxis: {
          type: 'value',
          axisLaba: {
            formatter: '{value} 个'
          }
        },
        series: [{
          data: [],
          type: 'line',
          itemStyle: {
            normal: {
              lineStyle: {
                width: 3// 设置线条粗细
              }
            }
          },
          markPoint: {
            data: [
              { type: 'max', name: '最大值' },
              { type: 'min', name: '最小值' }
            ]
          }
        }]
      },

      infomationCumulate: {
        data: [
          {
            count: '',
            title: 'Redis版本'
          },
          {
            count: '',
            title: '运行模式'
          },
          {
            count: '',
            title: '监听端口'
          },
          {
            count: '',
            title: '已连客户端'
          },
          {
            count: '',
            title: '启动秒数'
          },
          {
            count: '',
            title: '启动天数'
          },
          {
            count: '',
            title: '内存限制'
          },
          {
            count: '',
            title: '系统内存'
          },
          {
            count: '',
            title: '是否启动AOF'
          },
          {
            count: '',
            title: 'RDB是否成功'
          },
          {
            count: '',
            title: '网络入口'
          },
          {
            count: '',
            title: '网络出口'
          }
        ]
      }
    }
  },
  mounted () {

  },
  // watch: {
  //   $route () {
  //     if (this.$route.path !== '/daemon/redis/index') {
  //       window.clearTimeout(this.timer)
  //     } else {
  //       this.timer = window.setInterval(() => {
  //         setTimeout(() => {
  //           this.getInfo()
  //         }, 0)
  //       }, 3000)
  //     }
  //   }
  // },
  created () {
    this.getInfo()
    this.timer = window.setInterval(() => {
      setTimeout(() => {
        this.getInfo()
      }, 0)
    }, 3000)
  },
  destroyed () {
    clearTimeout(this.timer)
    this.timer = null
  },
  methods: {

    getInfo () {
      fetchInfo().then(response => {
        this.memoryCumulate.series[0].data.push(parseFloat(response.data.info.used_memory_human))
        this.memoryCumulate.series[1].data.push(parseFloat(response.data.info.used_memory_peak_human))
        this.memoryCumulate.xAxis.data.push(response.data.time)
        this.dbSizeCumulate.series[0].data.push(parseInt(response.data.dbSize))
        this.dbSizeCumulate.xAxis.data.push(response.data.time)
        this.chartData.rows = response.data.commandStats

        // infomation
        this.infomationCumulate.data[0].count = response.data.info.redis_version
        this.infomationCumulate.data[1].count = response.data.info.redis_mode
        this.infomationCumulate.data[2].count = response.data.info.tcp_port
        this.infomationCumulate.data[3].count = response.data.info.connected_clients
        this.infomationCumulate.data[4].count = response.data.info.uptime_in_seconds
        this.infomationCumulate.data[5].count = response.data.info.uptime_in_days
        this.infomationCumulate.data[6].count = response.data.info.used_memory_peak_human
        this.infomationCumulate.data[7].count = response.data.info.used_memory_human
        this.infomationCumulate.data[8].count = response.data.info.aof_enabled
        this.infomationCumulate.data[9].count = response.data.info.rdb_last_bgsave_status
        this.infomationCumulate.data[10].count = response.data.info.instantaneous_input_kbps + 'kps'
        this.infomationCumulate.data[11].count = response.data.info.instantaneous_output_kbps + 'kps'

        // 删除第一个元素
        if (this.memoryCumulate.series[0].data.length > 7) {
          this.memoryCumulate.series[0].data.shift()
          this.memoryCumulate.series[1].data.shift()
          this.dbSizeCumulate.series[0].data.shift()
          this.memoryCumulate.xAxis.data.shift()
          this.dbSizeCumulate.xAxis.data.shift()
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.box {
  padding: 20px 0px;
  width: 25%;
  list-style: none;
  text-align: center;
  border: 1px;
  float: left;
  .count {
    margin: 8px, 0, 15px;
    font-weight: 700;
    font-size: 30px;
    color: #15a0ff;
  }
}
</style>
