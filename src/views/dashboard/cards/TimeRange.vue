<template>
  <div class="dropdown">
    <select-dialog v-bind="dialogProps" ref="modelRef" @touch="$emit('dialogSelect',$event)"></select-dialog>
    <el-dropdown @command="handleCommand" trigger='click'>
      <span class="date">{{ dateLabel }}</span>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item command="-1">过期</el-dropdown-item>
        <el-dropdown-item command="1">今天</el-dropdown-item>
        <el-dropdown-item command="2">明天</el-dropdown-item>
        <el-dropdown-item command="3">后天</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
    <span class="dep" @click="handleDep">{{ depLabel }}</span>
  </div>
</template>

<script>
import SelectDialog from '@/components/MySelectList/selectDialog.vue'

export default {
  components: { SelectDialog },
  data() {
    return {
      dialogProps:{
        urls: "mes/basicData/depPage",
        tableColumn: this.$Column.salmDep,
        multiple: false,
        tableForm: this.$Form.salmDep,
      },
    }
  },
  props: {
    depLabel: {
      type: String,
      default: '请选择部门'
    },
    dateRange: {
      type: String,
      default: '3'
    }
  },
  computed: {
    dateLabel() {
      return this.dateRange === '-1' ? '过期' : this.dateRange === '1' ? '今天' : this.dateRange === '2' ? '明天' : '后天';
    }
  },
  methods: {
    handleCommand(command) {
      this.$emit('updateDateRange', command);
    },
    handleDep() {
      this.$emit('openDepDialog');
      this.$refs.modelRef.create()
    }
  }
}
</script>

<style scoped lang="less">
.dropdown {
  display: flex;
  justify-content: flex-end;

  span {
    margin-right: 10px;

    &::after{
      content: '\02C5';
      margin-left: 5px;
    }
  }
}

.date {

  &:hover {
    cursor: pointer;
  }
}

.dep {
  &:hover {
    cursor: pointer;
  }
}
span {
  height: 16px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.5);
  font-size: 12px;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 16px;
  margin: 3px 0 0 6px;
}
</style>
