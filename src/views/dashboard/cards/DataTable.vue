<template>
  <div class="card-container">
    <div class="card-header">
      <span class="card-header-title">{{ $t(`dashboard.${title}`) }}</span>
      <DateRange v-if="params.paramType === 'date'"
        :depLabel="param.name || param.dep || '请选择部门'"
        :dateRange="param.dateRange"
        @updateDateRange="handleCommand($event)"
        @dialogSelect="handleSave"
      />
      <TimeRange v-if="params.paramType === 'time'"
        :depLabel="param.name || param.dep || '请选择部门'"
        :dateRange="param.dateRange"
        @updateDateRange="handleCommand($event)"
        @dialogSelect="handleSave"
      />
    </div>
    <vxe-grid ref="gridRef" border="inner" stripe resizable show-overflow show-header-overflow height="90%"
              :loading="loading" :columns="computedTableColumn" :data="tableData">
    </vxe-grid>
  </div>

</template>

<script>
import DateRange from '@/views/dashboard/cards/DateRange.vue'
import TimeRange from '@/views/dashboard/cards/TimeRange.vue'
import SelectDialog from '@/components/MySelectList/selectDialog.vue'
import { getUserStore, setUserStore } from '@/util/store'

export default {
  name: 'DataTable',
  components: { SelectDialog, DateRange, TimeRange },
  data() {
    return {
      loading: false,
      tableData: [],
      dialogProps:{
        urls: "mes/basicData/depPage",
        tableColumn: this.$Column.salmDep,
        multiple: false,
        tableForm: this.$Form.salmDep,
      },
      param: { },
    }
  },
  props: {
    title: {
      type: String,
      default: 'DataTable'
    },
    tableColumn: {
      type: Array,
      default: () => []
    },
    api_find: {
      type: Function,
      default: null,
    },
    columnMinWidth:{
      type: String,
      default: '80px'
    },
    params:{
      type: Object,
      required: true,
    }
  },
  computed: {
    computedTableColumn() {
      return [
        ...this.tableColumn.map(column => ({
          ...column,
          minWidth: this.columnMinWidth
        }))
      ];
    }
  },
  mounted() {
    this.param = getUserStore({name: `dashboard_table_${this.title}`}) || this.params;
    this.handleGet();
  },
  methods: {
    async handleGet() {
      if(this.loading) return;
      this.loading = true;
      if (!this.api_find || typeof this.api_find !== 'function') {
        this.loading = false;
        return this.$message.error('请配置 api_find 参数');
      }
      try {
        const param = this.param
        const res = await this.api_find(param);
        this.tableData = res.data.records || res.data;
        // this.tablePage.total = res.data.total;
        // this.tablePage.currentPage = res.data.current;
        return res;
      } catch (err) {
        // this.requestFailed(err)
        return Promise.reject(err);
      } finally {
        this.loading = false;
      }
    },
    handleCommand(command) {
      this.param.dateRange = command
      this.handleGet()
      setUserStore({ name: `dashboard_table_${this.title}`, content: this.param }); // 更新本地存储
    },
    handleSave(item){
      this.$set(this.param, 'dep', item.data.deptCode);
      this.$set(this.param, 'name', item.name);
      this.handleGet()
      setUserStore({ name: `dashboard_table_${this.title}`, content: this.param  }); // 更新本地存储
    }
  }
}
</script>

<style scoped lang="less">
.vxe-grid {

  ::v-deep .vxe-body--row {
    &.row--checked {
      background: var(--el-color-primary-light-8);
    }
  }

  ::v-deep .vxe-header--row {
    padding: 0;
    height: 35px;
    background-color: var(--el-color-primary-light-9);
    color: black !important;

    .vxe-header--column {
      font-weight: 500;
    }
  }

  ::v-deep .vxe-table--body-wrapper{
    scrollbar-width: none;
  }
}
.card-container{
  padding: 10px 10px;
  height: 100%;
  overflow-y: hidden;
  background-color: white;
}
.card-header{
  display: flex;
  justify-content: space-between;
}
.card-header-title {
  font-weight: bold;
  color: #2b2b2b;
  font-size: 16px;
}

</style>