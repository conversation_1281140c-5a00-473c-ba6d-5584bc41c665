<template>
  <div class="quick-container">
    <div class="quick-menu-title">
      {{ $t('dashboard.navigation') }}
    </div>
    <div class="quick-menu-item-list">
      <div v-if="menuList.length === 0" class="add-icon" @click="handleAddClick">
      </div>
      <div class="quick-menu-item" v-for="item in menuList" :key="item.path" @click="$router.push(item.path)" >
        <img :src="item.icon">
        <span class="item-title" :title="$t(item.meta.title)">
          {{ $t(item.meta.title) }}
        </span>
      </div>
    </div>
  </div>
</template>

<script>
import { getUserStore } from '@/util/store'

export default {
  name: 'QuickMenu',
  data() {
    return {
      menuList: [],
      icons: require.context('@/assets/Desktop/menu', false, /\.png$/), // 加载所有png图标
      iconIndex: 0, // 图标索引
    }
  },
  activated() {
    this.iconIndex = 0;
    this.loadFavorites();
  },
  mounted() {
    this.loadFavorites();
  },
  methods: {
    loadFavorites() {
      const storedFavorite = getUserStore({ name: 'favorite_menu' });
      this.menuList = storedFavorite || [];
      this.assignIconsInOrder();
    },
    assignIconsInOrder() {
      const iconCount = this.icons.keys().length;

      this.menuList = this.menuList.map(item => {
        const icon = this.icons(this.icons.keys()[this.iconIndex % iconCount]);
        this.iconIndex++;
        return { ...item, icon }; // 为每个菜单项分配图标
      });
    },
    handleAddClick() {
      this.$router.push('/menu/menu')
    }
  }
}
</script>

<style scoped lang="less">
.quick-container {
  background-color: white;
  display: flex;
  flex-direction: column;
  padding: 0 10px;
  height: 100%;
}

.quick-menu-title{
  font-weight: bold;
  color: #2b2b2b;
  font-size: 16px;
}


.quick-menu-item-list {
  display: flex;
  flex-wrap: wrap;
  overflow: auto;

  .quick-menu-item {
    width: 100px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    .item-title {
      padding: 0 5px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      max-width: 100%;
      color: rgba(48, 49, 51, 1);
    }

  }

  .add-icon {
    width: 50px;
    height: 50px;
    margin-left: 25px;
    border: 1px solid #ddd;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 8px;
    cursor: pointer;
    position: relative;
  }

  .add-icon::before,
  .add-icon::after {
    content: "";
    position: absolute;
    background-color: #888; /* 加号颜色 */
    width: 60%; /* 横线和竖线的长度 */
    height: 1px; /* 加号线条的粗细 */
  }

  .add-icon::before {
    transform: rotate(90deg); /* 垂直线 */
  }

  .add-icon::after {
    /* 水平线，无需额外旋转 */
  }
}


</style>
