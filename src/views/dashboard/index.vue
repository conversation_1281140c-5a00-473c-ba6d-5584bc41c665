<template>
  <div class="layout" style="background-color: #e2e7ef">
    <div class="grid-header">
      <span class="title"></span>
      <span class="switch">
          <div v-if="draggable" class="switch-item"  @click="handleRecover">
            <svg-icon :icon-class="'icon-refresh'" width="14px" height="14px"></svg-icon>
            <span>{{ $t('dashboard.recover') }}</span>
          </div>
          <div class="switch-item" @click="handledEdit">
            <svg-icon :icon-class="'icon-design'" width="14px" height="14px"></svg-icon>
            <span>{{ draggable ? $t('dashboard.close') : $t('dashboard.design') }}</span>
          </div>
        </span>
    </div>
    <grid-layout :layout.sync="layout"
                 :col-num="colNum"
                 :row-height="100"
                 :is-draggable="draggable"
                 :is-resizable="resizable"
                 :vertical-compact="true"
                 :use-css-transforms="true"
    >
      <grid-item v-for="item in layout"
                 :static="item.static"
                 :x="item.x"
                 :y="item.y"
                 :w="item.w"
                 :h="item.h"
                 :i="item.i"
                 :key="item.i"
                 @resized="resizedEvent"
                 @moved="movedEvent"
      >
        <span v-show="deletable" class="remove" @click="removeItem(item.i)">x</span>
        <component :is="widgetInfo[item.i]?.component" v-bind="widgetInfo[item.i]?.props"></component>
      </grid-item>
    </grid-layout>
  </div>
</template>

<script>
import { GridLayout, GridItem } from 'vue-grid-layout'
import { setUserStore, getUserStore } from '@/util/store'
import { unfinished, warn } from '@/api/dashboard'

export default {
  components: {
    GridLayout,
    GridItem
  },
  data() {
    return {
      layout: [],
      onlyRead:[
        {
          'x': 10,
          'y': 0,
          'w': 2,
          'h': 8,
          'i': '0'
        },
        {
          'x': 0,
          'y': 0,
          'w': 10,
          'h': 2,
          'i': '1'
        },
        {
          'x': 0,
          'y': 2,
          'w': 5,
          'h': 6,
          'i': '2'
        },
        {
          'x': 5,
          'y': 2,
          'w': 5,
          'h': 6,
          'i': '3'
        }
      ],
      widgetInfo: {},
      draggable: false,
      resizable: false,
      deletable: false,
      colNum: 12,
      index: 0,
      storeName: 'homeLayout',
      endWorkTime: ''
    }
  },
  created() {
    const storedLayout = getUserStore({ name: this.storeName }) || this.onlyRead

    this.layout = storedLayout.map(item => ({ x: item.x, y: item.y, w: item.w, h: item.h, i: item.i }))
    this.widgetInfo = {
      '0': { component: () => import('@/views/dashboard/cards/BaseColumn') },
      '1': { component: () => import('@/views/dashboard/cards/QuickMenu') },
      '2': {
        component: () => import('@/views/dashboard/cards/DataTable'),
        props: {
          title: 'todo',
          api_find: unfinished,
          params: {
            dep: '',
            dateRange: '3',
            paramType: 'date'
          },
          tableColumn: [
            { field: 'moNo', title: '工单号' },
            { field: 'zcNo', title: '工序号',  },
            { field: 'mrpNo', title: '品号',  },
            { field: 'prdName', title: '品名',  },
            { field: 'qty', title: '工单数量', },
            { field: 'qty_fin', title: '完工量',
              slots: {
                default: ({ row }) => {
                  return this.$createElement('el-tag', {
                    props: {
                      type: 'primary',
                    },
                    domProps: {
                      innerHTML: (row.qty_fin || 0),
                    },
                  });
                },
              },
            },
            { field: 'qty_unfin', title: '未完工量',
              slots: {
                default: ({ row }) => {
                  const qtyUnfin = row.qty - (row.qty_fin || 0);
                  return this.$createElement('el-tag', {
                    props: {
                      type: 'danger',
                    },
                    domProps: {
                      innerHTML: qtyUnfin,
                    },
                  });
                },
              },
            },
            { field: 'endDd', title: '预交日期', },
            { field: 'spc', title: '规格',},

          ]
        }
      },
      '3': {
        component: () => import('@/views/dashboard/cards/DataTable'),
        props: {
          title: 'warn',
          api_find: warn,
          params: {
            dep: '',
            dateRange: '-1',
            paramType: 'time'
          },
          tableColumn: [
            { field: 'moNo', title: '工单号' },
            { field: 'prdNo', title: '品号',  },
            { field: 'prdName', title: '品名',  },
            { field: 'qtyRtn', title: '送检数量', },
            { field: 'tiDd', title: '送检时间', },
            { field: 'overDd', title: '超期时间',
              slots: {
                default: ({ row }) => {
                  const now = new Date();
                  const overtime = now - new Date(row.tiDd);
                  return this.$createElement('el-tag', {
                    props: {
                      type: 'danger',
                    },
                    domProps: {
                      innerHTML: Math.floor(overtime / (1000 * 60 * 60 * 24)) + '天'
                    },
                  });
                },
              },
            },
            { field: 'spc', title: '规格', },
          ]
        }
      }
    }
  },
  mounted() {
    this.index = this.layout.length
  },
  methods: {
    addItem: function() {
      let maxY = Math.max(...this.layout.map(item => item.y))

      let currentRowItems = this.layout.filter(item => item.y === maxY)
      let currentRowWidth = currentRowItems.reduce((sum, item) => sum + item.w, 0)
      let newItemWidth = 4
      let newItemX = (currentRowWidth + newItemWidth <= this.colNum) ? currentRowWidth : 0
      let newItemY = (newItemX === 0) ? maxY + 1 : maxY

      let newItem = {
        x: newItemX,
        y: newItemY,
        w: newItemWidth,
        h: 2,
        i: this.index
      }

      this.layout.push(newItem)
      this.index++
      setUserStore({ name: this.storeName, content: this.layout })
    },
    removeItem: function(val) {
      const index = this.layout.map(item => item.i).indexOf(val)
      this.layout.splice(index, 1)
      setUserStore({ name: this.storeName, content: this.layout })
    },
    resizedEvent: function(i, newH, newW, newHPx, newWPx) {
      setUserStore({ name: this.storeName, content: this.layout })
    },
    movedEvent: function(i, newX, newY) {
      setUserStore({ name: this.storeName, content: this.layout })
    },
    handledEdit() {
      this.draggable = !this.draggable
      this.resizable = !this.resizable
      // this.deletable = !this.deletable
    },
    handleRecover() {
      this.layout = this.onlyRead.map(item => ({ ...item }));
      setUserStore({ name: this.storeName, content: this.layout });
    }
  }
}
</script>

<style scoped lang="less">
.layoutJSON {
  background: #ddd;
  border: 1px solid black;
  margin-top: 10px;
  padding: 10px;
}

.grid-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 20px;
  padding: 0 10px;

  .switch {
    display: flex;
    align-items: center;
    margin-left: 5px;
    font-size: 12px;
    color: #666666;
    cursor: pointer;

    .switch-item {
      display: flex;
      margin-left: 10px;
      align-items: center;
    }

  }

  svg {
    width: 14px;
    height: 14px;
    margin-right: 2px;
    color: var(--el-color-primary)
  }
}

.columns {
  -moz-columns: 120px;
  -webkit-columns: 120px;
  columns: 120px;
}

.remove {
  position: absolute;
  right: 2px;
  top: 0;
  cursor: pointer;
}

.vue-grid-layout {
  background: #e0e5ee;
}

::v-deep .vue-grid-item {
  border-radius: 2px;
  overflow: hidden;

  &:not(.vue-grid-placeholder) {
    background: none;
  }

  &.vue-grid-placeholder {
    background: var(--el-color-primary-light-7);
  }

  .resizing {
    opacity: 0.9;
  }

  .static {
    background: #cce;
  }

  .text {
    font-size: 24px;
    text-align: center;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
    height: 100%;
    width: 100%;
  }

  .no-drag {
    height: 100%;
    width: 100%;
  }

  .minMax {
    font-size: 12px;
  }

  .add {
    cursor: pointer;
  }
}

.vue-draggable-handle {
  position: absolute;
  width: 20px;
  height: 20px;
  top: 0;
  left: 0;
  background: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='10' height='10'><circle cx='5' cy='5' r='5' fill='#999999'/></svg>") no-repeat;
  background-position: bottom right;
  padding: 0 8px 8px 0;
  background-repeat: no-repeat;
  background-origin: content-box;
  box-sizing: border-box;
  cursor: pointer;
}

</style>