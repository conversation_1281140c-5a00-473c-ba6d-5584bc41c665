<template>
  <div>
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="48">
          <a-col
            :md="6"
            :sm="24"
          >
            <a-form-item :label="$t('message.title')">
              <a-input
                v-model="queryParam.title"
                :placeholder="$t('message.placeholder.title')"
              />
            </a-form-item>
          </a-col>
          <a-col
            :md="6"
            :sm="24"
          >
            <a-form-item :label="$t('message.state')">
              <a-select
                style="width: 100%"
                v-model="queryParam.state"
                :placeholder="$t('message.placeholder.state')"
              >
                <a-select-option :value="0">
                  {{$t('message.sta.0')}}
                </a-select-option>
                <a-select-option :value="1">
                  {{$t('message.sta.1')}}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col
            :md="6"
            :sm="24"
          >
            <span class="table-page-search-submitButtons">
              <a-button
                type="primary"
                @click="getList"
              >{{ $t('public.query') }}</a-button>
              <a-button
                style="margin-left: 8px"
                @click="reset"
              >{{ $t('public.reset') }}</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <vxe-toolbar custom>
      <template v-slot:buttons>
        <a-dropdown :trigger="['click']">
          <a-button>{{ $t('public.action') }}
            <a-icon type="down" />
          </a-button>
          <a-menu slot="overlay">
            <a-menu-item key="0">
              <a @click="dropdownMenuEvent('delete')"> {{$t('message.sta.1')}}</a>
            </a-menu-item>
          </a-menu>
        </a-dropdown>
        <a-button
          style="margin-left:10px"
          type="primary"
          icon="plus"
          @click="handleAdd()"
        >{{ $t('public.add') }}</a-button>
      </template>
    </vxe-toolbar>
    <vxe-table
      border
      resizable
      stripe
      highlight-current-row
      show-overflow
      highlight-hover-row
      export-config
      ref="xTable"
      :loading="loading"
      :data="tableData"
      @cell-dblclick="cellDBLClickEvent"
      :keyboard-config="{ isArrow: true }"
      :edit-config="{ trigger: 'click', mode: 'row' }"
    >
      <vxe-table-column
        type="checkbox"
        fixed="left"
        align="center"
        :width="50"
      ></vxe-table-column>
      <vxe-table-column
        field="title"
        title="message.title"
        align="center"
      ></vxe-table-column>
      <vxe-table-column
        field="state"
        title="message.state"
        align="center"
      >
        <template v-slot="scope">
          <span>{{scope.row.state===0? $t('message.sta.0'): $t('message.sta.1')}}</span>
        </template>
      </vxe-table-column>
      <vxe-table-column
        field="data"
        title="message.data"
        align="center"
      ></vxe-table-column>
      <vxe-table-column
        field="createName"
        title="message.createName"
        align="center"
      ></vxe-table-column>

    </vxe-table>
    <vxe-pager
      :loading="loading"
      :current-page="tablePage.currentPage"
      :page-size="tablePage.pageSize"
      :total="tablePage.total"
      :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
      @page-change="handlePageChange"
    >
    </vxe-pager>
    <Drawer
      ref="Drawer"
      @getList="getList"
    />
  </div>
</template>

<script>
import {
  getMsg,
  del
} from '@/api/aps/message'
import Drawer from './drawer'
export default {
  name:'message',
  components: {
    Drawer
  },
  data () {
    return {
      tableData: [],
      loading: false,
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      queryParam: {
        state: 0
      },
    }
  },
  created () {
    this.getList()
  },
  methods: {
    // 查询列表
    getList () {
      this.loading = true
      getMsg(
        Object.assign({
          current: this.tablePage.currentPage,
          size: this.tablePage.pageSize
        }, this.queryParam)
      )
        .then(res => {
          this.tableData = res.data.records
          this.tablePage.total = res.data.total
          this.tablePage.currentPage = res.data.current
          this.loading = false
        })
        .catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },

    reset () {
      this.queryParam = {}
    },
    // 分页
    handlePageChange ({
      currentPage,
      pageSize
    }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getList()
    },
    // 添加
    handleAdd () {
      this.$refs.Drawer.create({
        title: this.$t('public.add')
      })
    },
    cellDBLClickEvent ({
      row
    }) {
      this.$refs.Drawer.edit({
        title: this.$t('public.Detailed')
      }, row)
    },
    // 删除按钮
    dropdownMenuEvent (name) {
      switch (name) {
        case 'delete': {
          const selectRecords = this.$refs.xTable.getCheckboxRecords()
          if (selectRecords.length) {
            let ids = []
            selectRecords.forEach(i => {
              return ids.push(i.id)
            })
            const that = this
            this.$confirm({
              title: this.$t('public.del.title'),
              content: this.$t('message.c'),
              okText: this.$t('public.sure'),
              okType: 'danger',
              cancelText: this.$t('public.cancel'),
              onOk () {
                that.loading = true
                del(ids)
                  .then(() => {
                    that.loading = false
                    that.getList()
                    that.$bus.$emit("onslected")
                    that.$message.success(that.$t('public.success'))
                  })
                  .catch(err => {
                    that.$bus.$emit("onslected")
                    that.requestFailed(err)
                  })
                  .finally(() => {
                    that.loading = false
                  })
              },
              onCancel () { }
            })
          } else {
            this.loading = false
            this.$message.warning(this.$t('public.list'))
          }
          break
        }
      }
    }
  }

}
</script>

<style lang="less">
</style>
