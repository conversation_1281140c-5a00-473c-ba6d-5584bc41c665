<template>
  <div>
    <a-tabs
      defaultActiveKey="1"
      @change="callback"
    >
      <a-tab-pane
        :tab="$t('user.message')"
        key="1"
        forceRender
      >
        <a-row>
          <a-col>
            <a-form-model
              layout="horizontal"
              ref="ruleForm"
              :rules="rules"
              :model="form"
            >
              <a-row>
                <a-col>
                  <a-form-model-item
                    v-bind="formItemLayout"
                    :label="$t('user.username')"
                    prop="username"
                  >
                    <a-input
                      v-model="form.username"
                      :disabled="true"
                      :placeholder="$t('user.placeholder.username')"
                    />
                  </a-form-model-item>
                </a-col>
                <a-col>
                  <a-form-model-item
                    v-bind="formItemLayout"
                    :label="$t('user.name')"
                    prop="name"
                  >
                    <a-input
                      :disabled="formStatus"
                      v-model="form.name"
                      :placeholder="$t('user.placeholder.name')"
                    />
                  </a-form-model-item>
                </a-col>
                <a-col>
                  <a-form-model-item
                    v-bind="formItemLayout"
                    :label="$t('user.phone')"
                    prop="phone"
                  >
                    <a-input
                      :disabled="formStatus"
                      v-model="form.phone"
                      :placeholder="$t('user.placeholder.phone')"
                    />
                  </a-form-model-item>
                </a-col>
                <a-col>
                  <a-form-model-item
                    v-bind="formItemLayout"
                    :label="$t('user.phone1')"
                    prop="phone1"
                  >
                    <a-input
                      :disabled="formStatus"
                      v-model="form.phone1"
                      :placeholder="$t('user.placeholder.phone')"
                    />
                  </a-form-model-item>
                </a-col>
                <a-col>
                  <a-form-model-item
                    v-bind="formItemLayout"
                    :label="$t('user.mac')"
                    prop="mac"
                  >
                    <a-input
                      :disabled="formStatus"
                      :placeholder="$t('user.placeholder.mac')"
                      v-model="form.mac"
                    ></a-input>
                  </a-form-model-item>
                </a-col>
                <a-col>
                  <a-form-model-item
                    v-bind="formItemLayout"
                    :label="$t('user.statu')"
                    prop="lockFlag"
                  >
                    <a-radio-group
                      :disabled="true"
                      v-model="form.lockFlag"
                    >
                      <a-radio-button
                        value="0"
                        style="margin-right:20px"
                      >{{ $t('user.status.0') }}</a-radio-button>
                      <a-radio-button value="1">{{ $t('user.status.1') }}</a-radio-button>
                    </a-radio-group>
                  </a-form-model-item>
                </a-col>
              </a-row>
            </a-form-model>
            <a-row :gutter="16">
              <a-col
                class="gutter-row"
                :span="12"
                style="text-align:right"
              >
                <a-button
                  type="primary"
                  v-if="status==='0'"
                  :loading="loading"
                  @click="handleEdit()"
                >{{ $t('public.edit') }}</a-button>
                <a-button
                  type="primary"
                  v-if="status==='1'"
                  :loading="loading"
                  @click="handleok()"
                >{{ $t('public.to-update') }}</a-button>
              </a-col>
            </a-row>
          </a-col>
        </a-row>
      </a-tab-pane>
      <a-tab-pane
        :tab="$t('user.pass')"
        key="2"
        forceRender
      >
        <div>
          <a-form-model
            layout="horizontal"
            ref="password"
            :rules="rules1"
            :model="form"
          >
            <a-row>
              <a-col>
                <a-form-model-item
                  v-bind="formItemLayout"
                  :label="$t('user.oldpassword')"
                  prop="password"
                >
                  <a-input
                    type="password"
                    v-model="form.password"
                    :placeholder="$t('user.placeholder.password')"
                  />
                </a-form-model-item>
              </a-col>
              <a-col>
                <a-form-model-item
                  v-bind="formItemLayout"
                  :label="$t('user.password')"
                  prop="newpassword1"
                >
                  <a-input
                    type="password"
                    v-model="form.newpassword1"
                    :placeholder="$t('user.placeholder.password')"
                  />
                </a-form-model-item>
              </a-col>
              <a-col>
                <a-form-model-item
                  v-bind="formItemLayout"
                  :label="$t('user.password2')"
                  prop="newpassword2"
                >
                  <a-input
                    type="password"
                    v-model="form.newpassword2"
                    :placeholder="$t('user.placeholder.password')"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
          </a-form-model>
          <a-row :gutter="16">
            <a-col
              class="gutter-row"
              :span="12"
              style="text-align:right"
            >
              <a-button
                type="primary"
                :loading="loading"
                @click="handleok()"
              >{{ $t('public.to-update') }}</a-button>
            </a-col>
          </a-row>
        </div>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>
<script>
import { mapState, mapActions } from 'vuex'
import MySelectList from '@/components/MySelectList'
import axios from '@/router/axios'
export default {
  components: {
    MySelectList
  },
  data () {
    var validatePass = (rule, value, callback) => {
      if (this.form.password !== '') {
        if (value !== this.form.newpassword1) {
          callback(new Error(this.$t('user.placeholder.newpassword2')))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    return {
      key: '1',
      data: '',
      formStatus: true,
      loading: false,
      status: '0',
      form: {},
      form2: {},
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      rules: {
        username: [
          { required: true, message: this.$t('user.placeholder.username'), trigger: 'blur' }
        ],
        name: [
          { required: true, message: this.$t('user.placeholder.name'), trigger: 'blur' }
        ]
      },
      rules1: {
        // password: [{ required: true, min: 6, message: this.$t('user.placeholder.oldpassword'), trigger: 'change' }],
        newpassword1: [{ required: true, min: 6, message: this.$t('user.placeholder.newpassword1'), trigger: 'change' }],
        newpassword2: [{ required: true, validator: validatePass, trigger: 'blur' }]
      },
      formItemLayout: {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        }
      }
    }
  },
  computed: {
    ...mapState({
      userInfo: state => state.user.userInfo
    })
  },
  watch: {
    key () {
      this.getList()
      this.formStatus = true
      this.status = '0'
      this.$refs.ruleForm.resetFields()
      this.$refs.password.resetFields()
    }
  },
  created () {
    this.getList()
  },
  methods: {
    ...mapActions(['Logout']),
    getList () {
      const row = this.userInfo
      this.data = row.deptId
      this.form = {
        username: row.username,
        name: row.name,
        phone: row.phone,
        phone1: row.phone1,
        mac: row.mac,
        lockFlag: row.lockFlag
      }
    },
    callback (key) {
      this.key = key
    },
    handleEdit () {
      this.status = '1'
      this.formStatus = false
    },
    // 更新个人信息
    handleok () {
      if (this.key === '1') {
        this.$refs.ruleForm.validate(valid => {
          if (valid) {
            this.loading = true
            axios({
              url: '/admin/user/edit',
              method: 'post',
              data: this.form
            }).then((res) => {
              this.loading = false
              this.status = '0'
              this.$message.success(this.$t('public.success'))
            }).catch(() => {
              this.loading = false
              this.$message.error(this.$t('public.error'))
            })
          } else {
            this.loading = false
            this.$message.error(this.$t('public.error'))
            return false
          }
        })
      }
      if (this.key === '2') {
        this.$nextTick(() => {
          this.$refs.password.validate(valid => {
            if (valid) {
              this.loading = true
              axios({
                url: '/admin/user/edit',
                method: 'post',
                data: this.form
              }).then((res) => {
                this.loading = false
                this.status = '0'
                this.Logout().then(() => {
                  window.location.reload()
                })
                this.$message.success(this.$t('public.success'))
              }).catch(err => this.requestFailed(err))
                .finally(() => {
                  this.loading = false
                })
            } else {
              this.loading = false
              this.$message.error(this.$t('public.error'))
              return false
            }
          })
        })
      }
    }

  }
}
</script>