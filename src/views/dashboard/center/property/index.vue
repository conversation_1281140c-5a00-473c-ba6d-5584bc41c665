<template>
  <div>
    <a-spin :spinning="spinning">
      <a-tabs
        default-active-key="1"
        @change="callback"
      >
        <a-tab-pane
          key="1"
          tab="排程设置"
        >
          <a-row>
            <a-col :span='2'>
              <span>排程名称：</span>
            </a-col>
            <a-col :span='20'>
              <a-transfer
                :data-source="mockData"
                :titles="['可选项', '已选项']"
                :list-style="{
              width: '30%',
              height: '300px',
           }"
                :target-keys="targetKeys"
                :selected-keys="selectedKeys"
                :render="item => item.title"
                :disabled="disabled"
                @change="handleChange"
                @selectChange="handleSelectChange"
              />
            </a-col>
          </a-row>

          <a-row style="margin-top:30px">
            <a-col :span='2'>
              <span>排程日期：</span>
            </a-col>
            <a-col :span='20'>
              <a-checkbox-group
                v-model="checkeys"
                :options="optionsWithDisabled"
              >
              </a-checkbox-group>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <!-- offset='22' -->
            <a-col span="2">
              <a-button
                style="margin-top:20px"
                type="primary"
                :loading="loading"
                @click="handleOK()"
              >{{ $t('public.sure') }}</a-button>
              <!-- <a-button @click="onClose">{{ $t('public.cancel') }}</a-button> -->
            </a-col>
          </a-row>
        </a-tab-pane>

        <!-- <a-tab-pane
        key="2"
        tab="Tab 2"
        force-render
      >
      </a-tab-pane>
      <a-tab-pane
        key="3"
        tab="Tab 3"
      >
        Content of Tab Pane 3
      </a-tab-pane> -->
      </a-tabs>
    </a-spin>
  </div>
</template>
<script>
import { add } from '@/api/aps/property'
import {
  mapState
} from 'vuex'
const optionsWithDisabled = [
  { label: '星期一', value: '1' },
  { label: '星期二', value: '2' },
  { label: '星期三', value: '3' },
  { label: '星期思', value: '4' },
  { label: '星期五', value: '5' },
  { label: '星期六', value: '6' },
  { label: '星期七', value: '7' },
];
export default {
  data () {
    return {
      loading: false,
      spinning: false,
      mockData: [
        {
          key: '1',
          title: '客户优先级',
        },
        {
          key: '2',
          title: '预交日',

        },
        {
          key: '3',
          title: '共模',

        },
        {
          key: '4',
          title: '部分共模',
        },
        {
          key: '5',
          title: '上道制程完工日',
        }
      ],
      datas: '',
      targetKeys: [],
      optionsWithDisabled,
      checkeys: ['1', '2', '3', '4', '5', '6', '7'],
      selectedKeys: [],
      disabled: false
    }
  },
  computed: {
    ...mapState({
      sysUserSeeting: state => state.user
    })
  },
  created () {
    const seeting = this.sysUserSeeting.sysUserSeeting
    let ids = seeting.schedulingRules
    if (ids) {
      this.targetKeys = ids.split(',')
      if (seeting.days) {
        this.checkeys = seeting.days.split(',')
      }
    }
  },
  methods: {
    callback (e) {
  
    },
    handleChange (nextTargetKeys, direction, moveKeys) {
      if (moveKeys.length > 0 && direction === 'right') {
        moveKeys.forEach(i => {
          this.targetKeys.push(i)
        })
      }
      if (moveKeys.length > 0 && direction === 'left') {
        this.targetKeys = nextTargetKeys
      }
   
    },
    handleSelectChange (sourceSelectedKeys, targetSelectedKeys) {
      this.selectedKeys = [...sourceSelectedKeys, ...targetSelectedKeys]
    },
    handleOK () {
      this.loading = true
      this.spinning = true
      const obj = {
        schedulingRules: this.targetKeys.join(','),
        days: this.checkeys.join(',')
      }
      add(obj).then((res) => {
        this.loading = false
        this.spinning = false
        this.$message.success(this.$t('public.successaps'))
      }).catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
          this.spinning = false
        })

    }
  }
};
</script>