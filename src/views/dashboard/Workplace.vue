<template>
  <div class="wPlace" >
    <div  class="left">
      <WorkplaceCard style="margin-top: 4px;margin-bottom: 4px;"/>
      <div  class="left-container" :style="zWidth ? 'width:1590px':'width:1336px'">
          <splitpanes class="default-theme" horizontal style="width:100%;height: 100%;">
            <pane min-size="20" max-size="70" :size="55">
              <splitpanes :push-other-panes="false">
                <pane :size="50">
                  <el-card class="box-card1">
                    <div  class="clearfix">
                      <span class="c-header" >代办任务</span>
                      <el-button class="c-buttom" type="text">编辑</el-button>
                    </div>
                    <el-table
                      :data="tableData"
                      stripe
                      style="width: 100%"
                      :row-style="{ height: '32px' }"
                      :header-cell-style="{color:'#2b2b2b','font-weight': 'bold', 'background-color': '#EAEFEE', height: '32px' }">
                      <el-table-column
                        prop="date"
                        label="日期"
                        width="180"
                      >
                      </el-table-column>
                      <el-table-column
                        prop="name"
                        label="姓名"
                        width="180">
                      </el-table-column>
                      <el-table-column
                        prop="address"
                        label="地址"
                        :show-overflow-tooltip="true">
                      </el-table-column>
                      <el-table-column
                        prop="state"
                        label="状态">
                      </el-table-column>
                      <el-table-column
                        prop="test"
                        label="测试"
                      >
                      </el-table-column>
                    </el-table>
                  </el-card>
                </pane>
                <pane :size="50">
                  <el-card class="box-card1">
                    <div  class="clearfix">
                      <span class="c-header" >逾期任务</span>
                      <el-button class="c-buttom" type="text">编辑</el-button>
                    </div>
                    <el-table
                      :data="tableData"
                      stripe
                      style="width: 100%"
                      :row-style="{ height: '32px' }"
                      :header-cell-style="{color:'#2b2b2b','font-weight': 'bold', 'background-color': '#EAEFEE', height: '32px' }">
                      <el-table-column
                        prop="date"
                        label="日期"
                        width="180"
                      >
                      </el-table-column>
                      <el-table-column
                        prop="name"
                        label="姓名"
                        width="180">
                      </el-table-column>
                      <el-table-column
                        prop="address"
                        label="地址"
                        :show-overflow-tooltip="true">
                      </el-table-column>
                      <el-table-column
                        prop="state"
                        label="状态">
                      </el-table-column>
                      <el-table-column
                        prop="test"
                        label="测试">
                      </el-table-column>
                    </el-table>
                  </el-card>
                </pane>
              </splitpanes>
            </pane>
            <pane max-size="70" :size="45">
              <splitpanes :push-other-panes="false">
                <pane :size="50">
                  <el-card class="box-card2">
                    <div  class="clearfix">
                      <span class="c-header" >机器设备报表</span>
                      <el-select v-model="value" clearable placeholder="请选择" size="mini" class="custom-select" >
                        <el-option
                          v-for="item in options"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value">
                        </el-option>
                      </el-select>
                    </div>
                    <el-table
                      :data="tableData1"
                      stripe
                      style="width: 100%"
                      :row-style="{ height: '32px' }"
                      :header-cell-style="{color:'#2b2b2b','font-weight': 'bold', 'background-color': '#EAEFEE', height: '32px' }">
                      <el-table-column
                        prop="date"
                        label="日期"
                        width="180"
                      >
                      </el-table-column>
                      <el-table-column
                        prop="name"
                        label="姓名"
                        width="180">
                      </el-table-column>
                      <el-table-column
                        prop="address"
                        label="地址"
                        :show-overflow-tooltip="true">
                      </el-table-column>
                      <el-table-column
                        prop="state"
                        width="80"
                        label="状态">
                      </el-table-column>
                      <el-table-column
                        prop="test"
                        label="测试"
                      >
                      </el-table-column>
                    </el-table>
                  </el-card>
                </pane>
                <pane :size="50">
                  <el-card class="box-card2">
                    <div  class="clearfix">
                      <span class="c-header" >人员报表</span>
                      <el-select v-model="value" clearable placeholder="请选择" size="mini" class="custom-select" >
                        <el-option
                          v-for="item in options"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value">
                        </el-option>
                      </el-select>
                    </div>
                    <el-table
                      :data="tableData1"
                      stripe
                      style="width: 100%"
                      :row-style="{ height: '32px' }"
                      :header-cell-style="{color:'#2b2b2b','font-weight': 'bold', 'background-color': '#EAEFEE', height: '32px' }">
                      <el-table-column
                        prop="date"
                        label="日期"
                        width="180"
                      >
                      </el-table-column>
                      <el-table-column
                        prop="name"
                        label="姓名"
                        width="180">
                      </el-table-column>
                      <el-table-column
                        prop="address"
                        label="地址"
                        :show-overflow-tooltip="true">
                      </el-table-column>
                      <el-table-column
                        prop="state"
                        width="80"
                        label="状态">
                      </el-table-column>
                      <el-table-column
                        prop="test"
                        label="测试"
                      >
                      </el-table-column>
                    </el-table>
                  </el-card>
                </pane>
              </splitpanes>
            </pane>
          </splitpanes>
      </div>
    </div>
    <div  class="right">
      <div class="right-1">
        <div  class="clearfir">
          <span class="r-header" >常用功能</span>
          <el-button class="r-buttom" type="text">编辑</el-button>
        </div>
        <div class="right-1-container">
          <div class="container-1" v-for="(item, index) in 8" :key="index">
            <div class="container-content"></div>
            <div class="container-title"></div>
          </div>
        </div>
      </div>
      <WorkplaceRight  :normalData="normalData" :wHeight="'160px'" :cHeight="'120px'" :wTitle="'安灯异常'" />
      <WorkplaceRight  :normalData="molDing" :wHeight="'160px'" :cHeight="'120px'" :wTitle="'模治具'" />
      <WorkplaceRight  :normalData="noticeData" :wHeight="'270px'" :cHeight="'230px'" :wTitle="'检验通知'" />
    </div>
  </div>
</template>

<script>
import { Splitpanes, Pane } from 'splitpanes'
import 'splitpanes/dist/splitpanes.css'
import WorkplaceCard from "@/components/Home/WorkplaceCard";
import { timeFix } from '@/utils/util'
import { mapState, mapGetters } from 'vuex'
import PageHeader from '@/components/PageHeader'
import HeadInfo from '@/components/tools/HeadInfo'
import { Radar } from '@/components'
import WorkplaceRight from "@/components/Home/WorkplaceRight";

export default {
  name: 'Workplace',
  components: {
    // PageView,
    PageHeader,
    HeadInfo,
    Radar,
    WorkplaceCard,
    Splitpanes,
    Pane,
    WorkplaceRight
  },
  data () {
    return {
      loading: true,
      title: this.$store.state.user.info.name,
      description: null,
      linkList: [],
      extraImage: '',
      search: false,
      tabs: {},
      timeFix: timeFix(),
      // 刷新token锁
      refreshLock: false,
      // 刷新token的时间
      refreshTime: '',
      // 计时器
      timer: '',
      value: '全部',
      activeNames: ['1'],
      zWidth:true,
      // 安灯异常测试数据
      normalData:[
        {
        number:'21',
        name:'设备异常',
        state:'1',
        fState:'1'
      },{
        number:'18',
        name:'缺料异常',
        state:'4',
        fState:'4'
      },{
        number:'0',
        name:'工序异常',
        state:'3',
        fState:'3'
      },{
        number:'1',
        name:'材料异常',
        state:'1',
        fState:'1'
      }],
      // 表格测试数据
      tableData: [
        {
        date: '2016-05-02',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1518 弄',
        state:'1',
        test:'可用'
      }, {
        date: '2016-05-04',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1517 弄',
        state:'1',
        test:'可用'
      }, {
        date: '2016-05-01',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1519 弄',
        state:'1',
        test:'可用'
      }, {
        date: '2016-05-03',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1516 弄',
        state:'1',
        test:'可用'
      }, {
        date: '2016-05-03',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1516 弄',
        state:'1',
        test:'可用'
      }, {
        date: '2016-05-03',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1516 弄',
        state:'1',
        test:'可用'
      }, {
        date: '2016-05-03',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1516 弄',
        state:'1',
        test:'可用'
      }, {
        date: '2016-05-03',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1516 弄',
        state:'1',
        test:'可用'
      }, {
        date: '2016-05-04',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1517 弄',
        state:'1',
        test:'可用'
      }, {
        date: '2016-05-01',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1519 弄',
        state:'1',
        test:'可用'
      }, {
        date: '2016-05-03',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1516 弄',
        state:'1',
        test:'可用'
      }],
      // 下拉框测试数据
      options: [
        {
        value: '选项1',
        label: '全部'
      }, {
        value: '选项2',
        label: '天心天思'
      }],
      // 表格测试数据
      tableData1: [
        {
        date: '2016-05-02',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1518 弄',
        state:'1',
        test:'可用'
      }, {
        date: '2016-05-04',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1517 弄',
        state:'1',
        test:'可用'
      }, {
        date: '2016-05-01',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1519 弄',
        state:'1',
        test:'可用'
      }, {
        date: '2016-05-03',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1516 弄',
        state:'1',
        test:'可用'
      }, {
        date: '2016-05-03',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1516 弄',
        state:'1',
        test:'可用'
      }],
      // 模治具测试数据
      molDing:[
        {
            number:'21',
          name:'寿命逾期',
            state:'1',
            fState:'1'
          },{
            number:'18',
            name:'待保养',
            state:'3',
            fState:'3'
          },{
            number:'0',
            name:'设备保养',
            state:'4',
            fState:'4'
          }],
      // 检验通知测试数据
      noticeData:[
        {
          number:'21',
          name:'首检',
          state:'2',
          fState:'2'
        },{
          number:'0',
          name:'待保养',
          state:'5',
          fState:'5'
        },{
          number:'23',
          name:'设备保养',
          state:'2',
          fState:'2'
        },{
          number:'1',
          name:'寿命逾期',
          state:'2',
          fState:'2'
        },{
          number:'18',
          name:'待保养',
          state:'2',
          fState:'2'
        },{
          number:'0',
          name:'设备保养',
          state:'5',
          fState:'5'
        }]
    }
  },
  computed: {
    ...mapGetters(['avatar', 'expires_in', 'userInfo']),
    ...mapState({
      multiTab: state => state.app.multiTab
    }),
    // ...mapState({
    //   title: (state) => state.user.info.name,
    //   welcome: (state) => state.user.welcome
    // }),
  },
  created () {
    let tenant = this.$ls.get('SET_TENANT')
    let tenantId = this.$store.state.user.userInfo.tenantId
    tenant.forEach(e => {
      if (e.id === tenantId) {
        this.title = e.name + '—' + this.$store.state.user.info.name
      }
    });
    // 实时检测刷新token
    // this.refreshToken()
  },
  mounted () {
    this.$bus.$on('rIsActive',()=>{
      this.zWidth=!this.zWidth
    }),
    this.tabs = this.directTabs
    // this.getPageMeta()
  },
  updated () {
    // this.getPageMeta()
  },
  destroyed () {

  },
  methods: {
    // 实时检测刷新token
    refreshToken () {
      // this.refreshTime = setInterval(() => {
      //   const token = getStore({
      //     name: 'access_token',
      //     debug: true
      //   })

      //   if (validatenull(token)) {
      //     return
      //   }

      //   if (this.expires_in <= 1000 && !this.refreshLock) {
      //     this.refreshLock = true
      //     this.$store
      //       .dispatch('RefreshToken')
      //       .catch(() => {
      //         clearInterval(this.refreshTime)
      //       })
      //     this.refreshLock = false
      //   }
      //   this.$store.commit('SET_EXPIRES_IN', this.expires_in - 10)
      // }, 10000)
    },

  }
}
</script>

<style lang="less" scoped>

.wPlace{
  width: 99%;
  height: 100%;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  margin-left: 10px;
}
.left{
  background-color: #EFEFF4;
  flex: 0.80;
}
.left-container{
  margin-right:8px;
  //width:1336px;
  height:680px;
}
.clearfix {
  height: 40px;
  line-height: 40px;
  display: block;
  align-items: center;
}
.c-header{
  float: left;
  font-weight: bold;
  color: #2b2b2b;
  font-size: 18px;
}
.c-buttom{
  float: right;
  padding: 13px 20px;
}

.box-card1 /deep/ .el-card__header{
  padding: 5px 17px;
}
.custom-select{
  float: right;
}
.box-card1, .box-card2{
  width: 100%;
  height: 100%;
}
.box-card1 /deep/ .el-table .el-table__cell{
  padding: 3px 0;
}
.box-card2 /deep/ .el-table .el-table__cell{
  padding: 3px 0;
}
.custom-select /deep/ .el-input__inner {
  border: none;
  background-color:white; /* 调整选择框的背景颜色 */
  color: #0e0e0e;
  width: 130px; /* 调整选择框的宽度 */
  height: 20px;
  font-size: 14px;
}

.right{
  background-color: #EFEFF4;
  flex: 0.18;
  display: flex; /* 使用Flex布局 */
  flex-direction: column; /* 设置为垂直布局 */
  justify-content: space-between;
  align-items: center; /* 水平居中 */
  margin-right:10% ;
}
.right-1{
  width: 100%;
  height: 200px;
  background: #FFFFFF;
  border-radius: 8px;
}
.clearfir {
  height: 40px;
  line-height: 40px;
  display: block;
  align-items: center;
}
.r-header{
  float: left;
  font-weight: bold;
  color: #2b2b2b;
  font-size: 18px;
  padding-left: 16px;
}
.r-buttom{
  float: right;
  padding: 13px 20px;
}
.right-1-container{
  width: 100%;
  height: 160px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  //padding: 8px;
}
.container-1{
  width: 60px;
  height: 69px;
  margin: 2px;
  margin-left: 4px;
}
.container-content{
  background-color: #D8D8D8 ;
  width: 48px;
  height: 48px;
  border-radius: 8px;
  margin-left: 6px;
}
.container-title{
  margin-top: 2px;
  background-color: #D8D8D8 ;
  width: 60px;
  height: 17px;
  border-radius: 4px;
}
</style>
