<template>
    <el-dialog :title="title" :visible.sync="visible" width="75%" @close="onClose" :destroy-on-close="true" class='JustMake-dialog'>
      <div class="table-page-search-wrapper">
        <el-form :inline="true" label-position="right" label-width="120px" class="drawer-form">
          <el-row :gutter="48">
            <el-col :md="8" :sm="24">
              <el-form-item label="来源">
                <el-input v-model="queryForm.moNo" placeholder="请输入内容" />
              </el-form-item>
            </el-col>
            <el-col :md="8" :sm="24">
              <el-form-item label="需求客户">
                <el-input v-model="queryForm.cusNo" placeholder="请输入客户" />
              </el-form-item>
            </el-col>
            <el-col :md="8" :sm="24">
              <el-form-item label="客户订单">
                <el-input v-model="queryForm.cusOsNo" placeholder="请输入客户订单" />
              </el-form-item>
            </el-col>
            <el-col :md="8" :sm="24">
              <el-form-item label="开工日期起">
                <el-date-picker style="width:100%" v-model="staDd" placeholder="请选择日期" />
              </el-form-item>
            </el-col>
            <el-col :md="8" :sm="24">
              <el-form-item label="开工日期止">
                <el-date-picker style="width:100%" v-model="endDd" placeholder="请选择日期" />
              </el-form-item>
            </el-col>
            <el-col :md="8" :sm="24">
              <el-form-item label="工程案号">
                <el-select v-model="queryForm.casNo" placeholder="请输入工程案号" filterable @focus="casnClick">
                  <el-option v-for="item in casns" :key="item.casNo" :label="item.name" :value="item.casNo">
                    {{ item.name }}
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :md="8" :sm="24">
              <el-form-item label="单据类别">
                <el-select v-model="queryForm.bilType" placeholder="请输入单据类别" filterable @focus="bilSpcClick">
                  <el-option v-for="item in options" :key="item.spcNo" :label="item.name" :value="item.spcNo">
                    {{ item.name }}
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col style="float:right">
              <span class="table-page-search-submitButtons">
                <el-button size='small' type="primary" @click="getList">{{ $t('public.query') }}</el-button>
                <el-button size='small' style="margin-left: 8px" @click="reset">{{ $t('public.reset') }}</el-button>
              </span>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <vxe-table border resizable size='small' stripe highlight-current-row show-overflow highlight-hover-row
        export-config ref="xTable" :loading="loading" @cell-click="cellClickEvent" :data="tableData" height="300"
        :keyboard-config="{ isArrow: true }"
        :seq-config="{ startIndex: (tablePage.currentPage - 1) * tablePage.pageSize }"
        :radio-config="{ labelField: 'name', trigger: 'row' }">
        <vxe-table-column fixed="left" type='radio' align="center" :min-width="50"></vxe-table-column>
        <vxe-table-column fixed="left" type="seq" title="序号" align="center" :width="60"> </vxe-table-column>
        <vxe-table-column field="staDd" title="预开" align="center" :min-width="150"></vxe-table-column>
        <vxe-table-column field="moNo" :min-width="150" title="单号" align="center"></vxe-table-column>
        <vxe-table-column field="bilName" :min-width="150" title="单据类别" align="center"></vxe-table-column>
        <vxe-table-column field="endDd" :min-width="150" title="预完" align="center"></vxe-table-column>
        <vxe-table-column field="mrpNo" :min-width="150" title="成品代号" align="center"></vxe-table-column>
        <vxe-table-column field="prdName" :min-width="150" title="成品名称" align="center"></vxe-table-column>
      </vxe-table>
      <vxe-pager style="margin-top:10px" :loading="loading" :current-page="tablePage.currentPage"
        :page-size="tablePage.pageSize" :total="tablePage.total"
        :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']" @page-change="handlePageChange">
      </vxe-pager>
      <span slot="footer" class="dialog-footer">
      <el-button @click="onClose">{{ $t('public.cancel') }}</el-button>
      <el-button type="primary" @click="save">{{ $t('public.save') }}</el-button>
    </span>
    </el-dialog>
</template>
<script>
import moment from 'moment'
import { fetchList, queryCasn, querybilSpc } from '@/api/barcode/purchase/inspect'
export default {
  data() {
    return {
      queryForm: {
        moNo: '',
        bilType: '',
        casNo: '',
        cusNo: '',
        cusOsNo: ''
      },
      staDd: null,
      endDd: null,
      casns: [],
      options: [],
      spinning: false,
      title: '',
      spcDd: '',
      tableData: [],
      visible: false,
      loading: false,
      row: {},
      tablePage: {
        currentPage: 1,
        pageSize: 5,
        total: 0
      },
    }
  },
  methods: {
    onClose() {
      this.tableData = []
      this.spinning = false
      this.loading = false
      this.visible = false
      this.queryForm = {}
      this.reset()
    },
    casnClick() {
      queryCasn().then(response => {
        this.casns = response.data
      }).catch(err => this.requestFailed(err));
    },
    bilSpcClick() {
      querybilSpc(
        Object.assign({
          bilId: "MO"
        })
      ).then(response1 => {
        this.options = response1.data;
      }).catch(err => this.requestFailed(err));
    },
    getList() {
      this.tableData = []
      this.spinning = true
      let staDd
      let endDd
      if (this.staDd !== null) {
        staDd = moment(this.staDd).format('YYYY-MM-DD')
      } else {
        staDd = null
      }
      if (this.endDd !== null) {
        endDd = moment(this.endDd).format('YYYY-MM-DD')
      } else {
        endDd = null
      }
      fetchList(
        Object.assign(
          {
            current: this.tablePage.currentPage,
            size: this.tablePage.pageSize,
            moNo: this.queryForm.moNo,
            staDd: staDd,
            endDd: endDd,
            bilType: this.queryForm.bilType,
            casNo: this.queryForm.casNo,
            cusNo: this.queryForm.cusNo,
            cusOsNo: this.queryForm.cusOsNo,
          }
        )
      ).then(response => {
        this.tableData = response.data.records
        this.tablePage.total = response.data.total
        this.tablePage.currentPage = response.data.current
        this.spinning = false
      })
        .catch(err => this.requestFailed(err))
        .finally(() => {
          this.spinning = false
        })
    },
    reset() {
      this.queryForm = {
        moNo: '',
        endDd: '',
        bilType: '',
        casNo: '',
        cusNo: '',
        cusOsNo: ''
      }
      this.staDd = null
      this.endDd = null
    },
    cellClickEvent({
      row
    }) {
      this.queryForm.moNo = row.moNo
      this.row = row
    },
    // 添加弹框
    create(model, row) {
      this.row = row
      this.title = model.title
      this.visible = true
      // this.getList()
    },
    // 分页
    handlePageChange({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getList()
    },
    save() {

      this.$emit('getSouce', this.row)
      this.onClose()

    }
  }
}
</script>
<style lang="less" scoped>
::v-deep .el-form-item{
  margin-bottom: 5px;
}
</style>