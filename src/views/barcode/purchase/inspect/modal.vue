<template>
  <div>
    <el-dialog :title="title" destroyOnClose width="75%" :visible.sync="visible" class='JustMake-dialog'>
      <div>
        <vxe-table border size='small' resizable stripe highlight-current-row show-overflow height='300px'
          highlight-hover-row export-config ref="xTable" :loading="loading" :data="tableData"
          :keyboard-config="{ isArrow: true }"
          :seq-config="{ startIndex: (tablePage.currentPage - 1) * tablePage.pageSize }"
          :radio-config="{ labelField: 'name', trigger: 'row' }">
          <vxe-table-column fixed="left" type="seq" title="序号" align="center" :width="60"> </vxe-table-column>
          <vxe-table-column field="prdNo" :title="$t('insp.prdNo')" align="center" :min-width="150"></vxe-table-column>
          <vxe-table-column field="prdName" :min-width="150" :title="$t('insp.prdName')"
            align="center"></vxe-table-column>
          <vxe-table-column field="prdMark" :min-width="150" :title="$t('insp.prdMark')"
            align="center"></vxe-table-column>
          <vxe-table-column field="ut" :min-width="150" :title="$t('insp.ut')" align="center"></vxe-table-column>
          <vxe-table-column field="whName " :min-width="150" :title="$t('insp.whName')"
            align="center"></vxe-table-column>
          <vxe-table-column field="qty" :min-width="150" :title="$t('insp.qty')" align="center"></vxe-table-column>
          <vxe-table-column field="barNoCount" :min-width="150" :title="$t('insp.barNoCount')"
            align="center"></vxe-table-column>
          <vxe-table-column field="batNo" :min-width="150" :title="$t('insp.batNo')" align="center"></vxe-table-column>
          <vxe-table-column title="public.action" align="center" fixed="right" :min-width="180">
            <template v-slot="scope">
              <a-button size='small' type="primary" style="margin-right:10px" @click="edit(scope.row)">
                修改数量
              </a-button>
              <a-button type="primary" size='small' @click="del(scope.row)">
                删除
              </a-button>
            </template>
          </vxe-table-column>
        </vxe-table>
        <el-dialog title="修改数量" :visible="visible1" append-to-body>
          <el-form ref="ruleForm">
            <el-form-item label="修改数量">
              <el-input size='small' style="width:100%" v-model="updatrQty" :min="0" />
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button @click="handleCancel">{{ $t('public.cancel') }}</el-button>
          </span>
        </el-dialog>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="onClose">{{ $t('public.cancel') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import moment from 'moment'
import { fetchList3, del, upd } from '@/api/barcode/purchase/inspect'

export default {
  data() {
    return {
      spinning: false,
      title: '',
      spcDd: '',
      tableData: [],
      visible: false,
      visible1: false,
      updatrQty: 0,
      formItemLayout: {
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          }
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 15
          }
        }
      },
      loading: false,
      row: {},
      tablePage: {
        currentPage: 1,
        pageSize: 5,
        total: 0
      },
      id: ''
    }
  },
  methods: {
    onClose() {
      this.tableData = []
      this.spinning = false
      this.loading = false
      this.visible = false
    },
    handleCancel() {
      this.visible1 = false
    },

    getList() {
      this.tableData = []
      this.spinning = true
      fetchList3(
        Object.assign(
          {
            bilNo: this.id,
            bilType: "ML_ZP"
          }
        )
      ).then(res => {
        this.tableData = res.data
        this.spinning = false
      })
        .catch(err => this.requestFailed(err))
        .finally(() => {
          this.spinning = false
        })
    },
    // 添加弹框
    create(model, id) {
      this.id = id
      this.title = model.title
      this.getList()
      this.visible = true
    },
    edit(row) {
      this.row = row
      this.visible1 = true
    },
    handleOk() {
      upd(
        Object.assign({
          id: this.row.id,
          barNoCount: this.updatrQty
        })
      ).then(response => {
        if ("success" == response.msg) {
          this.getList()
          this.$message.success('操作成功！')
          this.$emit('getNumber', response.data)
          this.visible1 = false
        } else {
          this.$message.error('操作失败')
        }
      }).catch(err => this.requestFailed(err));
    },
    // 删除
    del(row) {
      del(
        Object.assign({
          id: row.id
        })
      ).then(response => {
        this.getList()
        this.$emit('getNumber', response.data)
      }).catch(err => this.requestFailed(err))
        .finally(() => {
          this.spinning = false
        })
    },
    // 分页
    handlePageChange({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getList()
    },
  }
}
</script>
