<template>
  <el-dialog :title="title" :visible.sync="visible" width="75%" :destroy-on-close="true" :close-on-click-modal="false"
    @close="onClose" class='JustMake-dialog'>
    <div class="table-page-search-wrapper">
      <el-form :inline="true" label-position="right" label-width="120px" class="drawer-form">
        <el-row :gutter="48">
          <el-col :md="8" :sm="24">
            <el-form-item label="来源">
              <el-input v-model="queryForm.moNo" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
          <el-col v-permission="inspection_query_barcode_qy" :md="8" :sm="24">
            <el-form-item label="条码（乔云）">
              <el-input @keyup.enter.native="getBarcodeScanner()" v-model="queryForm.barNo" placeholder="请输内容" />
            </el-form-item>
          </el-col>
          <el-col :md="8" :sm="24">
            <el-form-item label="业务员">
              <el-input v-model="queryForm.cusOsNo" placeholder="请输入业务员" />
            </el-form-item>
          </el-col>
          <el-col v-permission="inspection_query_barcode_js" :md="8" :sm="24">
            <el-form-item label="条码（嘉声）">
              <el-input v-model="queryForm.barNoJS" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
          <el-col :md="8" :sm="24">
            <el-form-item label="厂商">
              <el-input v-model="queryForm.cusNo" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
          <el-col :md="8" :sm="24">
            <el-form-item label="开工日期起">
              <el-date-picker style="width:100%" v-model="startDate"
                :placeholder="$t('scheduling.placeholder.spcDd')" />
            </el-form-item>
          </el-col>
          <el-col :md="8" :sm="24">
            <el-form-item label="开工日期止">
              <el-date-picker style="width:100%" v-model="endDate" :placeholder="$t('scheduling.placeholder.spcDd')" />
            </el-form-item>
          </el-col>
          <el-col :md="8" :sm="24">
            <el-form-item label="工程案号">
              <el-select show-search style="width:100%" v-model="queryForm.casNo" placeholder="请输入工程案号"
                :filterable="true" @focus="casnClick">
                <el-option v-for="item in casns" :key="item.casNo" :label="item.name" :value="item.casNo">
                  {{ item.name }}
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="8" :sm="24">
            <el-form-item label="单据类别">
              <el-select show-search style="width:100%" v-model="queryForm.bilType" placeholder="请输入单据类别"
                :filterable="true" @focus="bilSpcClick">
                <el-option v-for="item in options" :key="item.spcNo" :label="item.name" :value="item.spcNo">
                  {{ item.name }}
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col style="float:right">
            <span class="table-page-search-submitButtons">
              <el-button size='small' type="primary" @click="getList">{{ $t('public.query') }}</el-button>
              <el-button size='small' style="margin-left: 8px" @click="reset">{{ $t('public.reset') }}</el-button>
            </span>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <el-table :data="tableData" border stripe highlight-current-row @cell-click="cellClickEvent" :loading="loading" height="300">
      <el-table-column type="selection" width="60"></el-table-column>
      <el-table-column type="index" width="60" label="序号"></el-table-column>
      <el-table-column prop="osNo" label="单据号码" align="center" min-width="150"></el-table-column>
      <el-table-column prop="osDD" label="单据日期" align="center" min-width="150"></el-table-column>
      <el-table-column prop="cusNo" label="厂商代号" align="center" min-width="150"></el-table-column>
      <el-table-column prop="name" label="厂商名称" align="center" min-width="150"></el-table-column>
      <el-table-column prop="bilName" label="单据类别" align="center" min-width="150"></el-table-column>
    </el-table>
    <el-pagination style="margin-top:10px" :current-page="tablePage.currentPage" :page-size="tablePage.pageSize"
      :total="tablePage.total" @current-change="handlePageChange">
    </el-pagination>
    <span slot="footer" class="dialog-footer">
      <el-button @click="onClose">{{ $t('public.cancel') }}</el-button>
      <el-button type="primary" @click="save">{{ $t('public.save') }}</el-button>
    </span>
  </el-dialog>
</template>
<script>
import moment from 'moment'
import { getPrdtBarcode, fetchList, queryCasn, querybilSpc } from '@/api/barcode/purchase/inspection_bar'
export default {
  data() {
    return {
      inspection_query_barcode_qy: 'inspection_query_barcode_qy',
      inspection_query_barcode_js: 'inspection_query_barcode_js',
      queryForm: {
        moNo: '',
        bilType: '',
        casNo: '',
        cusNo: '',
        cusOsNo: ''
      },
      startDate: null,
      endDate: null,
      casns: [],
      options: [],
      spinning: false,
      title: '',
      spcDd: '',
      tableData: [],
      visible: false,
      loading: false,
      row: {},
      tablePage: {
        currentPage: 1,
        pageSize: 5,
        total: 0
      },
    }
  },
  methods: {
    getBarcodeScanner(params) {
      if (this.queryForm.barNo == "" || this.queryForm.barNo == null) {
        this.$message.error('请输入条码！')
        return
      }
      this.queryForm.barCode = "3"
      this.visible = false
      getPrdtBarcode(
        Object.assign(
          {
            code: this.queryForm.barNo,
            inputQty: this.form.inputQty,
            type: "PO",
            barCode: this.form.barCode,
            isCount: "4",
            qty1: this.form.qty1,
            cusNo: this.mfPos.cusNo,
            no: this.mfPos.osNo,
            wh: this.form.wh
          },
          params
        )
      ).then(response => {
        if ("success" == response.data.msg) {
          this.form.count = response.data.data[0].qty;
          this.tableData = response.data.data[0].temps;
          this.mfPos.cusNo = response.data.data[0].temps[0].cusNo;
          this.mfPos.osNo = response.data.data[0].no;
          this.$message({
            showClose: true,
            message: "扫码成功！",
            type: "success"
          });
        } else {
          this.$message({
            showClose: true,
            message: response.data.data,
            type: "error"
          });
        }
      }).catch(err => this.requestFailed(err));
    },
    onClose() {
      this.tableData = []
      this.spinning = false
      this.loading = false
      this.visible = false
      this.queryForm = {}
      this.reset()
    },
    casnClick() {
      queryCasn().then(response => {
        this.casns = response.data
      }).catch(err => this.requestFailed(err));
    },
    bilSpcClick() {
      querybilSpc(
        Object.assign({
          bilId: "PC"
        })
      ).then(response1 => {
        this.options = response1.data;
      }).catch(err => this.requestFailed(err));
    },
    getList() {
      this.tableData = []
      this.spinning = true
      let startDate
      let endDate
      if (this.startDate !== null) {
        startDate = moment(this.startDate).format('YYYY-MM-DD')
      } else {
        startDate = null
      }
      if (this.endDate !== null) {
        endDate = moment(this.endDate).format('YYYY-MM-DD')
      } else {
        endDate = null
      }
      fetchList(
        Object.assign(
          {
            current: this.tablePage.currentPage,
            size: this.tablePage.pageSize,
            osNo: this.queryForm.osNo,
            startDate: startDate,
            endDate: endDate,
            bilType: this.queryForm.bilType,
            casNo: this.queryForm.casNo,
            cusNo: this.queryForm.cusNo,
            salNo: this.queryForm.salNo,
          }
        )
      ).then(response => {
        this.tableData = response.data.records
        this.tablePage.total = response.data.total
        this.tablePage.currentPage = response.data.current
        this.spinning = false
      })
        .catch(err => this.requestFailed(err))
        .finally(() => {
          this.spinning = false
        })
    },
    reset() {
      this.queryForm = {
        moNo: '',
        endDd: '',
        bilType: '',
        casNo: '',
        cusNo: '',
        cusOsNo: ''
      }
      this.staDd = null
      this.endDd = null
    },
    cellClickEvent(row) {
      this.queryForm.moNo = row.moNo || ''
      this.row = row
    },
    // 添加弹框
    create(model, row) {
      this.row = row
      this.title = model.title
      this.visible = true
      // this.getList()
    },
    // 分页
    handlePageChange({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getList()
    },
    save() {

      this.$emit('getSouce', this.row)
      this.onClose()

    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-form-item{
  margin-bottom: 5px;
}
</style>