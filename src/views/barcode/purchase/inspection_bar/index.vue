<template>
  <div class="layout">
    <vToolbar :data="toolbarItem" @toolbarClick="handleToolbarClick"></vToolbar>
    <el-form ref="formRef" :model="queryParam" label-position="right" label-width="90px" class="search-form">
      <el-row :gutter="16">
        <el-col :xs="24" :sm="12" :md="12" :lg="6" :xl="6">
          <el-form-item :label="$t('insp.barNo')">
            <el-input v-model="queryParam.barNo"  @keyup.enter.native="getBarcode()" />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="12" :lg="6" :xl="6">
          <el-form-item :label="$t('insp.inputbatNo')">
            <el-input v-model="queryParam.inputbatNo"/>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <vxe-table ref="gridRef" border stripe resizable show-overflow show-header-overflow :height="tableHeight"
        :loading="loading" :data="tableData" :custom-config="{ mode: 'popup' }"
        :checkbox-config="{ trigger: 'row', highlight: true, range: true }" >
      <vxe-table-column fixed="left" type="seq" title="序号" align="center" :width="60"> </vxe-table-column>
      <vxe-table-column field="bilNo" :title="$t('insp.bilNo')" align="center" :min-width="120"></vxe-table-column>
      <vxe-table-column field="prdNo" :title="$t('insp.prdNo')" align="center" :min-width="120"></vxe-table-column>
      <vxe-table-column field="prdName" :title="$t('insp.prdName')" align="center" :min-width="120"></vxe-table-column>
      <vxe-table-column field="prdMark" :title="$t('insp.prdMark')" align="center"> </vxe-table-column>
      <vxe-table-column field="ut" :title="$t('insp.ut')" :min-width="90" align="center"></vxe-table-column>
      <vxe-table-column field="whName" :title="$t('insp.whName')" align="center"></vxe-table-column>
      <vxe-table-column field="qty" :title="$t('insp.qty')" align="center"></vxe-table-column>
      <vxe-table-column field="barNoCount" :title="$t('insp.barNoCount')" align="center"></vxe-table-column>
      <vxe-table-column field="batNo" :title="$t('insp.batNo')"align="center"></vxe-table-column>
      <vxe-table-column field="check" :title="$t('insp.check')" align="center"></vxe-table-column>
    </vxe-table>
    <Modal ref="modal" @getNumber="getNumber" />
    <ModalSou ref="modalSou" @getSouce="getSouce" />
  </div>
</template>

<script>
import { throttle } from 'lodash';
import { fetList, fetchList2 } from '@/api/barcode/purchase/inspection_bar'
import Modal from './modal'
import ModalSou from './ModalSou'
import vToolbar from '@/components/amtxts/vTable/vToolbar.vue'

export default {
  components: {
    vToolbar,
    Modal,
    ModalSou
  },
  data() {
    return {
      toolbarItem: [
        { label: '采购转送检', value: 'title', },
        { label: '已扫明细', value: 'scan', },
        { label: '选择来源菜单', value: 'menu', },
        { label: '提交', value: 'submit', },
      ],
      tableHeight: '500',
      barcode_inspection_bar_scanned: 'barcode_inspection_bar_scanned',
      barcode_inspection_bar_source: 'barcode_inspection_bar_source',
      barcode_inspection_bar_submit: 'barcode_inspection_bar_submit',
      List: [],
      osNo: '',
      spinning: false,
      tableData: [],
      loading: false,
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      queryParam: {},
      formItemLayout: {
        labelCol: {
          md: { span: 24 },
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          md: { span: 24 },
          xs: { span: 24 },
          sm: { span: 15 }
        }
      }
    }
  },
  created() {
    // this.getList()
  },
  mounted() {
    window.addEventListener('resize', this.handleResize);
    this.handleResize();
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize);
  },
  methods: {
    handleToolbarClick(params) {
      switch (params) {
        case 'scan':
          this.scanned()
          break
        case 'menu':
          this.source()
          break
        case 'submit':
          this.submit()
          break
        default:
          break
      }
    },
    handleResize: throttle(function () {
      if (this.$refs.gridRef && this.$refs.gridRef.$el && this.$refs.formRef && this.$refs.formRef.$el) {
        const tableOffset = this.$refs.gridRef.$el.offsetTop;
        this.tableHeight = window.innerHeight - tableOffset;
      }
    }, 200),
    getBarcode() {
      if (this.queryParam.barNo == '' || this.queryParam.barNo == null || this.queryParam.barNo == undefined) {
        return
      }
      // this.spinning = true
      getPrdtBarcode(
        Object.assign({
          code: this.queryParam.barNo,
          inputQty: this.queryParam.inputQty,
          // inputQtyType: true,
          type: 'ML_ZP',
          no: this.moNo,
          wh: this.queryParam.wh

          // code: this.form.barNo,
          // inputQty: this.form.inputQty,
          // type: "ML_ZP",
          // no: this.queryForm.moNo,
          // wh: this.form.wh
        })
      )
        .then(response => {

          if ('success' == response.msg) {
            this.queryParam.count = response.data[0].qty
            this.tableData = response.data[0].temps
            this.$message.success(this.$t('public.success'))
          } else {
            this.$message.error(response.data)
          }
        })
        .catch(err => this.requestFailed(err))
        .finally(() => {
          this.spinning = false
        })
    },
    handleSearch(e) {
      queryWh(
        Object.assign({
          wh: e,
          name: e
        })
      )
        .then(res => {

          this.List = res.data.records
        })
        .catch(err => this.requestFailed(err))
        .finally(() => { })
    },
    handleChange(e) {

    },
    // 已扫明细
    scanned() {
      this.$refs.modal.create({ title: '已扫明细' }, this.moNo)
    },
    source() {
      this.$refs.modalSou.create({ title: '来源单号' })
    },
    handleUse(row) {

    },
    getNumber(data) {
      this.tableData = data
    },
    reset() {
      this.data = {}
      this.queryParam = {}
      this.datawh = ''
      this.fbStartDd = null
      this.fbEndDd = null
      this.bjStartDd = null
      this.bjEndDd = null
    },
    // 分页
    handlePageChange({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getList()
    },
    getSouce(data) {
      this.osNo = data.osNo
      if (!this.osNo) {
        return
      }
      this.spinning = true
      fetchList2(
        Object.assign({
          bilNo: this.osNo
        })
      )
        .then(response => {
          this.tableData = response.data
          this.spinning = false
        })
        .catch(err => this.requestFailed(err))
        .finally(() => {
          this.spinning = false
        })
    },
    submit() {
      if (this.tableData.length > 0) {
        this.spinning = true
        insert(
          Object.assign({
            bilType: 'MO',
            bilNo: this.moNo
          })
        )
          .then(response => {
            if ('success' == response.msg) {
              this.$message.success(response.data)
              this.spinning = false
            } else {
              this.$message.error(response.data)
              this.spinning = false
            }
          })
          .catch(err => this.requestFailed(err))
          .finally(() => {
            this.spinning = false
          })
      } else {
        this.$message.error('请先扫码数据!')
      }

      // insert(
      //   Object.assign({
      //     bilNo: this.moNo,
      //     bilType: "MO"
      //   }).then(response => {
      //     if ("success" == response.data.msg) {
      //       this.$message.success(response.data.data)
      //     } else {
      //       this.$message.error(response.data.data)
      //     }
      //     //this.page.total = response.data.total;
      //     this.spinning = false
      //   }).catch(err => this.requestFailed(err))
      //     .finally(() => {
      //       this.spinning = false
      //     })
      // )
    }
  }
}
</script>
<style lang="less" scoped>
.el-form {
  ::v-deep .el-form-item {
    margin: 16px 0 0 0;
    padding: 0px 32px 0 8px;
  }

  &.search-form {
    background: #FFFFFF;
    padding: 3px 0 11px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);

    .el-row {
      margin: 0 16px;
    }
  }
}
</style>
