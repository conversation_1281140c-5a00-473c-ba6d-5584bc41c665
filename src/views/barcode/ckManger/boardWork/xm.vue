<template>
  <div class="layout">
    <TBGrid ref="vTable" v-bind="vTableProps" @formDataChange="handleFormsChange" @selectListEvent="handleSelectListEvent">
    </TBGrid>
  </div>
</template>

<script>
import { getBoxNo } from '@/api/barcode/boardWork'
import vTable from '@/components/amtxts/vTable/vGrid.vue'
import TBGrid from '@/components/amtxts/TBGrid'
import { getUserStore, setUserStore } from '@/util/store'
import { processPrintRequestsWL } from '@/utils/arPrintAgent'
import { fetchList as pageTemp } from '@/api/custommana/templateSettings'
export default {
  components: {
    vTable,TBGrid
  },
  data() {
    return {
      formData: [],
      boardInput: null,
      vTableProps: {
        delete_key: '$pgNo:$pgNo',
        array_item: true,
        api_find: getBoxNo,
        toolbarItems: [
        ],
        formDataRaw: [
          { field: 'boardWork.boxNo',  component: 'input',},
          { field: 'boardWork.zbNo',  component: 'input',},
          { field: 'boardWork.ckNo', component: 'selectList',
            props:{
              url: '/barcode/boardWork/pageCk',
              tableColumn: this.$Column.ck,
              form: this.$Form.ck,
            }
          },
          { field: 'boardWork.prdNo',  component: 'input', disabled:true },
          { field: 'boardWork.boxid',  component: 'select', values: [] },
          { field: 'boardWork.boxPrinter',  component: 'select', values: [] },
        ],
        tableColumn: [
          { type: 'seq', width: '80px' },
          { field: 'boxNo', title: 'boardWork.boxNo' },
        ],
      }
    }
  },
  mounted() {
    this.boardInput = document.querySelector('input[field="boardWork.boxNo"]');
    this.boardInput.onkeydown = (e) => {
      if(e.keyCode ===  13){ // 监听回车事件
        this.handleBoxNoEnter()
      }
    }
    this.boardInput = document.querySelector('input[field="boardWork.zbNo"]');
    this.boardInput.onkeydown = (e) => {
      if(e.keyCode ===  13){ // 监听回车事件
        this.handleZbNoEnter()
      }
    }
    // window.onfocus = () => {
    //   if (this.boardInput) {
    //     this.boardInput.select();
    //   }
    // };
    this.initPrinter()
  },
  methods: {
    handleSelectListEvent(param){
      switch (param.field){
        case "boardWork.ckNo":
          const $formData = this.$refs.vTable.formData
          const _data = param.obj.data
          $formData['ckNo'] = _data.ckNo
          $formData['prdNo'] = _data.prdNo
          this.getTemplateInfo(_data.prdNo)
          // this.boardInput.select()
          break
      }
    },
    handleBoxNoEnter() {
      const $gridRef = this.$refs.vTable.$refs.gridRef
      const $formData = this.$refs.vTable.formData
      const { boxNo } = $formData
      $gridRef.remove()
      $gridRef.insertAt({ boxNo}, -1)
    },
    handleZbNoEnter() {
      const $gridRef = this.$refs.vTable.$refs.gridRef
      const $formData = this.$refs.vTable.formData
      const { zbNo } = $formData
      $gridRef.remove()
      const params = Object.assign({
        current: $gridRef.tablePage.currentPage,
        size: $gridRef.tablePage.pageSize
      }, {boardNo:zbNo});
      getBoxNo(params).then((res)=>{
        res.data.records.forEach((item)=>{
          $gridRef.insertAt({ boxNo:item.boxNo }, -1)
        })
      })
    },
    handleFormsChange(param){
      if(param.code === "boardWork.boxPrinter"){
        this.boxPrinter = param.value
        setUserStore({name:"boxPrinter", content: param.result,type: false})
      }
    },
    handlePrintEvent: async function(){
      const $gridRef = this.$refs.vTable.$refs.gridRef
      const $formData = this.$refs.vTable.formData
      if (!$formData['boxid']) {
        this.$message.error('请选择套版')
        return
      }
      if (!$formData['boxPrinter']) {
        this.$message.error('请选择打印机')
        return
      }
      const selectRecords =  $gridRef.getCheckboxRecords();
      if (selectRecords.length < 1) {
        this.$message.error('请选择打印数据')
        return
      }
      const templateId = $formData['boxid']
      const boxPrinter = $formData['boxPrinter']
      const ckNo = $formData['ckNo']
      selectRecords.forEach((i) => {
        const boxNo = i['boxNo']
        processPrintRequestsWL('21296', boxNo + "*params1:" + ckNo + "*params2:" + boxNo, templateId, 1, boxPrinter, 300, 'zpl', true);
      })
      $gridRef.remove()
    },
    handleQuery(){
      this.handleZbNoEnter()
    },
    async initPrinter() {
      await fetch('http://localhost:8899/printers', {
        method: 'GET',
        mode: 'cors'
      }).then(res => res.json())  // 解析 JSON
        .then(data => {
          if (data.code === 0) {
            if (data.data != null) {
              // 设置值
              const printer = data.data.map(item => ({ label: item, value: item}))
              let item = this.vTableProps.formDataRaw.find(item => item.field === 'boardWork.boxPrinter')
              if(item){
                item.values = printer
              }
              // 初始化
              const boxPrinter = getUserStore({name: 'boxPrinter'})
              if(printer.some(item => item.value === boxPrinter)){
                this.$set(this.$refs.vTable.formData, 'boxPrinter', boxPrinter)
              }
            }
          } else {
            this.$message.error(this.$t('arc.printSettings.tipsMsg0'))
          }
        })
        .catch(err => {
          this.requestFailed(this.$t('arc.printSettings.tipsMsg1'))
        })  // 错误处理
    },
    async getTemplateInfo(prdNo){
      try{
        const templates = await pageTemp({prdNo})
        let _box = templates.data.records.filter(item => item?.prttype === 7).map(item => ({ //  prttype === 7 箱唛标签
          label: item.prttepName,
          value: item.prttepNo
        }))
        if(_box.length > 0){
          const item = this.vTableProps.formDataRaw.find(item => item.field === 'boardWork.boxid')
          if(item){
            item.values = _box
            this.$set(this.$refs.vTable.formData, 'boxid', item.values[0].value)
          }
        }

      }catch (err){
        this.requestFailed(err);
      }
    },
  },
}
</script>
<style lang="less" scoped>
</style>
