<template>
  <div class="layout">
    <TBGrid ref="vTable" v-bind="vTableProps" @formDataChange="handleFormsChange" @selectListEvent="handleSelectListEvent">
    </TBGrid>
  </div>
</template>

<script>
import { getBoardNo } from '@/api/barcode/boardWork'
import vTable from '@/components/amtxts/vTable/vGrid.vue'
import TBGrid from '@/components/amtxts/TBGrid'
import { getUserStore, setUserStore } from '@/util/store'
import { processPrintRequestsWL } from '@/utils/arPrintAgent'
import { fetchList as pageTemp } from '@/api/custommana/templateSettings'
export default {
  components: {
    vTable,TBGrid,
  },
  data() {
    return {
      formData: [],
      boardInput: null,
      vTableProps: {
        tablePage: {
          currentPage: 1,
          pageSize: 20,
          total: 0
        },
        api_find: getBoardNo,
        toolbarItems: [
        ],
        formDataRaw: [
          { field: 'boardWork.boxNo1',  component: 'input',},
          { field: 'boardWork.zbNo1',  component: 'input',},
          { field: 'boardWork.ckNo', component: 'selectList',
            props:{
              url: '/barcode/boardWork/pageCk',
              tableColumn: this.$Column.ck,
              form: this.$Form.ck,
            }
          },
          { field: 'boardWork.prdNo',  component: 'input', disabled:true },
          { field: 'boardWork.zbid',  component: 'select', values: [] },
          { field: 'boardWork.zbPrinter',  component: 'select', values: [] },
        ],
        tableColumn: [
          { type: 'seq', width: '80px' },
          { field: 'boardNo', title: 'boardWork.zbNo' },
        ],

      }
    }
  },
  mounted() {
    this.boardInput = document.querySelector('input[field="boardWork.boxNo1"]');
    this.boardInput.onkeydown = (e) => {
      if(e.keyCode ===  13){ // 监听回车事件
        this.handleBoxNoEnter()
      }
    }
    this.boardInput = document.querySelector('input[field="boardWork.zbNo1"]');
    this.boardInput.onkeydown = (e) => {
      if(e.keyCode ===  13){ // 监听回车事件
        this.handleZbNoEnter()
      }
    }
    // window.onfocus = () => {
    //   if (this.boardInput) {
    //     this.boardInput.select();
    //   }
    // };
    this.initPrinter()
  },
  methods: {
    handleSelectListEvent(param){
      switch (param.field){
        case "boardWork.ckNo":
          const $formData = this.$refs.vTable.formData
          const _data = param.obj.data
          $formData['ckNo'] = _data.ckNo
          $formData['prdNo'] = _data.prdNo
          this.getTemplateInfo(_data.prdNo)
          // this.boardInput.select()
          break
      }
    },
    handleBoxNoEnter() {
      const $gridRef = this.$refs.vTable.$refs.gridRef
      const $formData = this.$refs.vTable.formData
      const { boxNo1 } = $formData
      $gridRef.remove()
      const params = Object.assign({
        current: $gridRef.tablePage.currentPage,
        size: $gridRef.tablePage.pageSize
      }, {boxNo:boxNo1});
      getBoardNo(params).then((res)=>{
        res.data.records.forEach((item)=>{
          $gridRef.insertAt({ id: item.id, boardNo:item.boardNo }, -1)
        })
      })
    },
    handleZbNoEnter() {
      const $gridRef = this.$refs.vTable.$refs.gridRef
      const $formData = this.$refs.vTable.formData
      const { zbNo1 } = $formData
      $gridRef.remove()
      $gridRef.insertAt({ boardNo: zbNo1 }, -1)
    },
    handleFormsChange(param){
      if(param.code === "boardWork.zbPrinter"){
        this.boxPrinter = param.value
        setUserStore({name:"zbPrinter", content: param.result,type: false})
      }
    },
    handlePrintEvent: async function(){
      const $gridRef = this.$refs.vTable.$refs.gridRef
      const $formData = this.$refs.vTable.formData
      if (!$formData['zbid']) {
        this.$message.error('请选择套版')
        return
      }
      if (!$formData['zbPrinter']) {
        this.$message.error('请选择打印机')
        return
      }
      const selectRecords =  $gridRef.getCheckboxRecords();
      if (selectRecords.length < 1) {
        this.$message.error('请选择打印数据')
        return
      }
      const zbPrinter = $formData['zbPrinter']
      const templateId = $formData['zbid']
      for (let i = 0; i < selectRecords.length; i++) {
        const record = selectRecords[i]
        const id = record['id']
        await processPrintRequestsWL('21296', id, templateId, 1, zbPrinter, 600, 'esc', false)
      }
      $gridRef.remove()
    },
    handleQuery(){
      this.handleBoxNoEnter()
    },
    async initPrinter() {
      await fetch('http://localhost:8899/printers', {
        method: 'GET',
        mode: 'cors'
      }).then(res => res.json())  // 解析 JSON
        .then(data => {
          if (data.code === 0) {
            if (data.data != null) {
              // 设置值
              const printer = data.data.map(item => ({ label: item, value: item}))
              let item = this.vTableProps.formDataRaw.find(item => item.field === 'boardWork.zbPrinter')
              if(item){
                item.values = printer
              }
              // 初始化
              const zbPrinter = getUserStore({name:'zbPrinter'})
              if(printer.some(item => item.value === zbPrinter)){
                this.$set(this.$refs.vTable.formData, 'zbPrinter', zbPrinter)
              }
            }
          } else {
            this.$message.error(this.$t('arc.printSettings.tipsMsg0'))
          }
        })
        .catch(err => {
          this.requestFailed(this.$t('arc.printSettings.tipsMsg1'))
        })  // 错误处理
    },
    async getTemplateInfo(prdNo){
      try{
        const templates = await pageTemp({prdNo})
        let _zb = templates.data.records.filter(item => item?.prttype === 8).map(item => ({ //  prttype === 8 栈板标签
          label: item.prttepName,
          value: item.prttepNo
        }))
        if(_zb.length > 0) {
          const item = this.vTableProps.formDataRaw.find(item => item.field === 'boardWork.zbid')
          if(item){
            item.values = _zb
            this.$set(this.$refs.vTable.formData, 'zbid', item.values[0].value)
          }
        }
             }catch (err){
        this.requestFailed(err);
      }
    },
  },
}
</script>
<style lang="less" scoped>
</style>
