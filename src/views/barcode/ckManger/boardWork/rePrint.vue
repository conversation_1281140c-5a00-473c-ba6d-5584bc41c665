<template>
  <div class='table-container'>
    <header class="toolbar">
      <div class="toolbar-title" >
        <span
          :class="{ active: showIn1 }"
          @click="change1">箱唛重打</span>
      </div>
      <div class="toolbar-title" >
        <span
          :class="{ active: showIn2 }"
          @click="change2">板码重打</span>
      </div>
      <div class="toolbar-btn" >
        <el-button v-for="(item, index) in toolbarItems" :key="index" @click="handleClick(item.value)" type="text"
                   :disabled="item.disabled">
          <svg-icon :icon-class="item.icon || 'icon-custom-field'" />
          {{ $t(item.label) }}
        </el-button>
      </div>
    </header>
    <main>
      <XM ref="xm" v-show="showIn1"></XM>
      <BM  ref="bm" v-show="showIn2"></BM>
    </main>
  </div>
</template>

<script>

import dispatched from "@/views/mes/dispatchworker/components/dispatched";
import BM from "./bm.vue";
import XM from './xm.vue'
import {mapGetters} from "vuex";
import getToobar from "@/utils/getToobar";
export default {
  name: 'dispatchWorker',
  components:{
    dispatched,BM,XM
  },
  props: {
    data: {
      type: Array,
      default: () => []
    },
  },
  data() {
    return {
      showIn1:true,
      showIn2:false,
      drawer: false,
      toolbarItems1: [
        { label: this.$t('toolbar.query'), value: 'query',icon: 'icon-query'},
        { label: this.$t('toolbar.create'), value: 'create',disabled:true,visible: true},
      ],
      toolbarItems2: [
        { label: this.$t('toolbar.query'), value: 'query',icon: 'icon-query'},
        { label: this.$t('toolbar.create'), value: 'create',disabled:true,visible: true},
      ],
      toolbarItems: [],
    }
  },
  mounted() {
    this.change1()
  },
  computed: {
    ...mapGetters(['permissions'])
  },
  methods: {
    handleClick(params) {
      switch (params) {
        case 'print'://打印
          if(this.showIn1) this.$refs.xm.handlePrintEvent()
          if(this.showIn2) this.$refs.bm.handlePrintEvent()
          break;
        case 'query':
          if(this.showIn1) this.$refs.xm.handleQuery()
          else if (this.showIn2) this.$refs.bm.handleQuery()
          break;
      }
    },
    change1(){
      this.showIn1=true
      this.showIn2=false
      const field = this.$route.meta.permission
      this.toolbarItems = getToobar.initializeToolbarItems(this.toolbarItems1,this.permissions,field)
      this.$refs.xm.handleQuery();
    },
    change2(){
      this.showIn1=false
      this.showIn2=true
      const field = this.$route.meta.permission
      this.toolbarItems = getToobar.initializeToolbarItems(this.toolbarItems2,this.permissions,field)
      this.$refs.bm.handleQuery();
    },
  },


}
</script>

<style scoped lang="less">
.toolbar {
  display: flex;
  height: 45px;
  background: #FFFFFF;
  overflow: hidden;
  white-space: nowrap;
  position: relative;
  z-index: 1;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.1);

  .toolbar-title {
    display: flex;
    justify-content: center;
    align-items: center;

    &::after {
      content: '';
      display: block;
      width: 1px;
      height: 22px;
      background: rgba(0, 0, 0, 0.2);
      margin: 0 16px;
    }

    span {
      height: 20px;
      line-height: 20px;
      margin-left: 16px;
    }
    span.active {
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      color: #000000;
      font-style: normal;
    }
    span:hover{
      font-weight: 600;
    }
  }

  .toolbar-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;

    ::v-deep .el-button {
      border: none;
      box-shadow: none;
      border-radius: 0;
      height: 45px;
      margin-left: 0;
      padding: 8px 15px;
      color: rgba(0, 0, 0, 0.85);
      font-size: 14px;

      span {
        display: flex;
        align-items: center;

        svg {
          color: var(--el-color-primary);
          margin-right: 3px;
        }
      }

      &:hover {
        color: white;
        background-color: var(--el-color-primary);

        svg {
          color: white;
        }
      }

      &.is-disabled {
        color: #747474;
        pointer-events: none;
        cursor: default;

        svg {
          color: #747474;
        }
      }
    }
  }
}
</style>