<template>
	<div class="layout">
		<vTable ref="vTable" v-bind="vTableProps" :print-type="printType" @toolbarClick="handleToolbarClick" @selectListEvent="handleSelectListEvent">
		</vTable>
	</div>
</template>
<script>
import vTable from '@/components/amtxts/vTable/vGrid.vue'
import { getByCkNo } from '@/api/barcode/boardWork'
import { fetchList as pageTemp } from '@/api/custommana/templateSettings'
export default {
	components: {
		vTable
	},
	data() {
		return {
      printType: 0,
			vTableProps: {
				api_find: getByCkNo,
				toolbarItems: [
					{ label: '打印出货仓', value: 'title', },
					{ label: 'boardWork.quickPrint', value: 'quickPrint', },
				],
				formDataRaw: [
          { field: 'boardWork.ckNo', component: 'selectList',
            props:{
              url: '/barcode/boardWork/pageCk',
              tableColumn: this.$Column.ck,
              form: this.$Form.ck,
            }
          },
          { field: 'boardWork.boardNo',  component: 'input'},
          { field: 'boardWork.whid',  component: 'select', values: []  },
          { field: 'boardWork.sendWh',  component: 'input', disabled:true },
				],
				tableColumn: [
          { type: 'seq', width: '80px' },
          { field: 'boxNo', title: 'boardWork.boxNo' },
          { field: 'prdNo', title: 'boardWork.prdNo' },
          { field: 'prdName', title: 'boardWork.prdName' },
          { field: 'boardNo', title: 'boardWork.boardNo' },
				],
			}
		}
	},
  mounted() {
    this.boardInput = document.querySelector('input[field="boardWork.boardNo"]');
    this.boardInput.onkeydown = async (e) => {
      if(e.keyCode ===  13){ // 监听回车事件
        const res = await this.$refs.vTable.handleGet()
        this.afterQuery(res.data.records[0])
      }
    }
  },
	methods: {
		handleToolbarClick(params) {
			switch (params.code) {
				case 'quickPrint':
          this.triggerPrint(this.$refs.vTable.formData['ckNo'])
					break
        case 'query':
          this.afterQuery(params.result.data.records[0])
          break
				default:
			}
		},
		handleSelectListEvent(param) {
      switch (param.field){
        case "boardWork.ckNo":
          const $formData = this.$refs.vTable.formData
          const _data = param.obj.data
          $formData['ckNo'] = _data.ckNo
          break
      }
		},
    async handleQuery(param){
      await getByCkNo(param).then(res => {
        console.log(res)
      })
    },
    async getTemplateInfo(prdNo){
      try{
        const templates = await pageTemp({prdNo})
        let _wh = templates.data.records.filter(item => item?.prttype === 9).map(item => ({ //  prttype === 8 出货仓标签
          label: item.prttepName,
          value: item.prttepNo
        }))
        if(_wh.length === 0) return
        const item = this.vTableProps.formDataRaw.find(item => item.field === 'boardWork.whid')
        if(item){
          item.values = _wh
          this.$set(this.$refs.vTable.formData, 'whid', item.values[0].value)
        }
      }catch (err){
        this.requestFailed(err);
      }
    },
    triggerPrint(id){
      const templateId = this.$refs.vTable.formData['whid']
      if(id === '' || id === null){
        this.$message.warning(`${this.$t("boardWork.ckNo")} ${this.$t("public.notNull")}`)
        return
      }
      if(templateId == null || templateId === ''){
        this.$message.warning(`${this.$t("boardWork.whid")} ${this.$t("public.notNull")}`)
        return
      }
      let params = 'timeKey=1&bill_type=21315&condition=id:' + id
      const URI = encodeURIComponent(params)
      const reportID = templateId + '|' + URI
      this.$refs.vTable.$refs.printDialog.JsViewerPrint(reportID) // 调用打印接口
    },
    afterQuery(result){
      const $formData = this.$refs.vTable.formData
      if(result === undefined || result === null){
        const item = this.vTableProps.formDataRaw.find(item => item.field === 'boardWork.whid')
        if(item){
          item.values = []
          $formData['whid'] = null
        }
        $formData['sendWh'] = null
        return
      }
      this.getTemplateInfo(result.prdNo)
       $formData['ckNo'] = result.ckNo
       $formData['sendWh'] = result.sendWh
    }
	},
}
</script>

