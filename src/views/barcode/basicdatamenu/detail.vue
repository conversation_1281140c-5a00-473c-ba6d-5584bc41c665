<template>
  <div>
    <!-- 单据页面设计-视窗 -->
    <docDialog :docDialogVisible.sync="docDialogVisible" :form-config="formConfig" :grid-config="gridConfig"></docDialog>
    <!-- 自定义栏位设计-视窗 -->
    <defDialog :defDialogVisible.sync="defDialogVisible" :form-config="formConfig" :grid-config="gridConfig"></defDialog>

    <!-- 工具栏 操作按钮 -->
    <dToolbar :data="toolbarItems" @toolbarClick="handleToolbarClick" :dropSetShow="dropSetShow"
      @handOpenDocDia="docDialogVisible = true" @handOpenDefDia="defDialogVisible = true"></dToolbar>

    <!-- 数据 表头vxe-form及表身vxe-grid -->
    <el-collapse v-model="activeNames">
      <el-collapse-item style="margin-bottom: 10px" name="1">
        <template #title>
          基础信息
        </template>
        <dForm ref="dForm" :formConfig="formConfig" :billData.sync="billData">
        </dForm>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script>
import docDialog from "@/components/def/DocDialog.vue";
import defDialog from "@/components/def/DefDialog.vue";
import dGrid from "@/components/def/DGrid.vue"
import dForm from "@/components/def/DForm.vue"
import dToolbar from "@/components/def/DToolbar.vue"
import useFieldDefine from "@/utils/def/useFieldDefine";

import { chuwsave } from '@/api/salm'
import Vue from 'vue'
export default {
  components: {
    docDialog, defDialog,
    dGrid, dForm, dToolbar
  },

  data() {
    return {
      defDialogVisible: false,
      docDialogVisible: false,
      activeNames: ['1', '2'],

      formConfig: {
        //装箱单
        PGM: "CHUWCONFIG",
        MENU_NAME: "CHUWCONFIG",
        TABLE_NAME: "WMS_PRD_AND_CHUW",

        initItems: null,
        items: [
          {
            span: 24,
            children: []
          }
        ],
        rules: {
          whName: [
            { required: true, message: this.$t('barcodeMenu.placeholder.whName') }
          ],
          wh: [
            { required: true, message: this.$t('barcodeMenu.placeholder.wh') }
          ],
          qty: [
            { required: true, message: this.$t('barcodeMenu.placeholder.qty') }
          ],
        },
      },

      formItems: [
        {
          span: 24,
          children: [
            { field: 'chuw', title: 'barcodeMenu.chUw', span: 6, itemRender: { name: 'VxeInput', props: { placeholder: '' } } },
            { field: 'chuwName', title: 'barcodeMenu.chUwName', span: 6, itemRender: { name: 'VxeInput', props: { placeholder: '' } } },
            { field: 'whName', title: 'barcodeMenu.whName', span: 6, itemRender: { name: 'VxeInput', props: { placeholder: '' } } },
            { field: 'wh', title: 'barcodeMenu.wh', span: 6, itemRender: { name: 'VxeInput', props: { placeholder: '' } } },
            { field: 'prdNo', title: 'barcodeMenu.prdNo', span: 6, itemRender: { name: 'VxeInput', props: { placeholder: '', } } },
            { field: 'prdMark', title: 'barcodeMenu.prdMark', span: 6, itemRender: { name: 'VxeInput', props: { placeholder: '' } } },
            { field: 'qty', title: 'barcodeMenu.qty', span: 6, itemRender: { name: 'VxeInput', props: { type: "number", placeholder: '' } } },
            { field: 'rem', title: 'barcodeMenu.rem', span: 6, itemRender: { name: 'VxeInput', props: { placeholder: '' } } },
          ]
        }
      ],

      gridConfig: {
        TABLE_NAME: "",
        columns: [
        ],
        rules: {
        }
      },

      billData: {
        pgm: 'WMSCHUW',
        formData: {
        },
        gridData: []
      },

      toolbarItems: [
        { value: 'remove', disabled: true },
        { value: 'edit', disabled: true },
        { label: '保存', value: 'save', icon: 'icon-save' },
      ],
      dropSetShow: true,
    }
  },

  mounted() {
    this.formConfig.items = this.formItems
    //单据设计页面用 initItems, initColumns
    this.formConfig.initItems = JSON.parse(JSON.stringify(this.formItems[0].children))
    this.gridConfig.initColumns = JSON.parse(JSON.stringify(this.gridConfig.columns))
    //初始化, 页面栏位信息加载 及动态渲染
    useFieldDefine.init(this.formConfig, this.gridConfig, this.billData)
    this.initData()
  },
  activated() {
    this.initData()
  },
  watch: {
    //自定义栏位设计 关闭, 触发更新栏位数据
    defDialogVisible(newVal) {
      if (newVal === false) {
        useFieldDefine.init(this.formConfig, this.gridConfig, this.billData)
      }
    },
    //单据页面设计 关闭, 触发更新栏位数据
    docDialogVisible(newVal) {
      if (newVal === false) {
        useFieldDefine.init(this.formConfig, this.gridConfig, this.billData)
      }
    },
  },
  methods: {
    initData() {
      let { code, row } = this.$route.params;
      switch (code) {
        case 'create':
          this.handNewData()
          break;
        case 'edit':
          this.handleSetValue(row);
          break;
        default:
          this.billData.formData = this.billData.formData || {};
      }
    },
    handleToolbarClick(params) {
      switch (params) {
        case 'create':
          this.handNewData()
          break;
        case 'save':
          this.handSaveData()
          break;
        case 'query':
          this.$router.push({name:'chuwIndex', params:{ code: 'query' }})
          break;
        default:
      }
    },
    //新增  
    handNewData() {
      this.$refs.dForm.reset()
    },
    //保存, 验证及过滤空数据
    async handSaveData() {
      //验证
      let flag = true;
      let currF = this.billData.formData
      if (currF.moNo === null || currF.moNo === "") {
        flag = false
      }

      for (let gridIndex in this.billData.gridData) {
        let curr = this.billData.gridData[gridIndex]
        if (curr.moNo === null || curr.moNo === "") {
          flag = false
          break
        }
      }
      if (!flag) {
        this.$message.warning("数据不完整, 请先输入!")
        return;
      }

      await chuwsave({
        ...this.billData.formData,
      }).then((res) => {
        if (res.code === 0) {
          this.initData(res.data)
          this.$message.success('保存成功')
        } else {
          this.$message.error('保存失败')
          return
        }
      }).catch((err) => {
        this.requestFailed(err)
      }).finally(() => {
      })
    },
    // 编辑
    handleSetValue(param) {
      this.billData.formData = param
    }
  }
}

</script>

<style lang="less" scoped>
.el-collapse {
  ::v-deep .el-collapse-item__header.is-active {
    border-bottom: 1px solid darkgrey;
  }

  ::v-deep .el-collapse-item__header {
    border-bottom: 1px solid darkgrey;
    font-size: 17px;
    font-weight: 520;
    padding-left: 20px;
  }

  ::v-deep .el-collapse-item__content {
    padding: 5px 5px;
    //border-bottom: red 5px solid;
  }
}
</style>
