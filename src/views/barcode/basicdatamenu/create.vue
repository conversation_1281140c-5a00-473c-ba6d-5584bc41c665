<template>
	<div>
		<!-- 新增弹窗 -->
		<el-drawer :title="$t(title)" :visible.sync="drawer" size="40%" ref="drawerRef" class="JustMake-drawer">
			<div class="drawer-content">
				<el-form ref="drawerFormRef" :model="formData" label-position="right" label-width="100px" class="drawer-form">
					<el-row>
						<el-col v-for="(item, index) in formDataRaw" :key="index" :span="item.span || 12">
							<el-form-item :label="$t(item.field)" :title="$t(item.field)" :prop="getPropName(item.field)"
								:rules="item.rules">
								<Forms :data="item" v-model="formData[getPropName(item.field)]" @selectListEvent="handleSelectItem">
								</Forms>
							</el-form-item>
						</el-col>
					</el-row>
				</el-form>
			</div>
			<div class="drawer-footer">
				<el-button @click="handleCancel">{{ $t('public.cancel') }}</el-button>
				<el-button type="primary" @click="handleCreate" :loading="drawerLoading">{{ drawerLoading ? 'Loading ...' :
					$t('public.sure')
					}}</el-button>
			</div>
		</el-drawer>
	</div>
</template>

<script>
import Forms from '@/components/Forms/index.vue'
import { getTogto, prdpage, whpageCw, whpageWh, chuwsave } from '@/api/salm'
export default {
	components: {
		Forms,
	},
	data() {
		return {
			api_save: chuwsave,
			drawer: false,
			drawerLoading: false,
			title: 'public.add',
			status: '0', // 0 新增 1 编辑
			formData: {},
			currentRow: {},
			formDataRaw: [
				{
					field: 'barcodeMenu.chUw', type: 'selectList',
					props: {
						url: "/barcode/wh/pageCw",
						tableColumn: [
							{ type: 'radio', width: 50 },
							{ field: 'chUw', title: 'barcodeMenu.chUw', align: 'center', },
							{ field: 'whName', title: 'barcodeMenu.whName', align: 'center', },
							{ field: 'name', title: 'barcodeMenu.name', align: 'center', },
						],
						form: {
							data: {
								chUw: '',
							},
							items: [
								{ field: 'name', title: 'barcodeMenu.name', itemRender: { name: '$input', } },
								{ field: 'whName', title: 'barcodeMenu.whName', itemRender: { name: '$input', } },
								{
									itemRender: {
										name: '$buttons',
										children: [
											{ props: { type: 'submit', content: 'public.query', status: 'primary' } },
											{ props: { type: 'reset', content: 'public.reset' } }]
									}
								}
							]
						},
					},
					// rules: [{ required: false, message: this.$t('barcodeMenu.placeholder.chUw') }]
				},
				{
					field: 'barcodeMenu.whName', type: 'selectList',
					props: {
						url: "/barcode/wh/pageWh",
						tableColumn: [
							{ type: 'radio', width: 50 },
							{ field: 'wh', title: 'barcodeMenu.wh', align: 'center', },
							{ field: 'whName', title: 'barcodeMenu.whName', align: 'center', },
						],
						form: {
							data: {
								whName: '',
							},
							items: [
								{ field: 'whName', title: 'barcodeMenu.whName', itemRender: { name: '$input', } },
								{
									itemRender: {
										name: '$buttons',
										children: [
											{ props: { type: 'submit', content: 'public.query', status: 'primary' } },
											{ props: { type: 'reset', content: 'public.reset' } }]
									}
								}
							]
						},
					},
					rules: [{ required: true, message: this.$t('barcodeMenu.placeholder.whName') }]
				},
				{
					field: 'barcodeMenu.wh', type: 'input', disabled: true,
					rules: [{ required: true, message: this.$t('barcodeMenu.placeholder.wh') }]
				},
				{
					field: 'barcodeMenu.name', type: 'selectList',
					props: {
						url: "/barcode/prd/page",
						tableColumn: [
							{ type: 'radio', width: 50 },
							{ field: 'prdNo', title: 'barcodeMenu.prdNo', align: 'center', },
							{ field: 'name', title: 'barcodeMenu.name', align: 'center', },
						],
						form: {
							data: {
								prdNo: '',
								name: '',
							},
							items: [
								{ field: 'name', title: 'barcodeMenu.name', itemRender: { name: '$input', } },
								{
									itemRender: {
										name: '$buttons',
										children: [
											{ props: { type: 'submit', content: 'public.query', status: 'primary' } },
											{ props: { type: 'reset', content: 'public.reset' } }]
									}
								}
							]
						},
					},
					// rules: [{ required: true, message: this.$t('barcodeMenu.placeholder.name') }]
				}, {
					field: 'barcodeMenu.prdNo', type: 'input', disabled: true,
					// rules: [{ required: true, message: this.$t('barcodeMenu.placeholder.prdNo') }]
				}, {
					field: 'barcodeMenu.prdMark', type: 'input',
					// rules: [{ required: true, message: this.$t('barcodeMenu.placeholder.prdMark') }]
				}, {
					field: 'barcodeMenu.qty', type: 'input',
					rules: [{ required: true, message: this.$t('barcodeMenu.placeholder.qty') }]
				}, {
					field: 'barcodeMenu.rem', type: 'input',
					// rules: [{ required: true, message: this.$t('barcodeMenu.placeholder.rem') }]
				},

			],
		}
	},
	watch: {
		status: function (newVal) {
			if (newVal === '0') {
				this.title = 'public.add'
				this.api_save = chuwsave
			} else {
				this.title = 'public.edit'
				this.api_save = chuwsave
			}
		}
	},
	methods: {
		handleVisible() {
			this.status = '0'
			this.formData = {}
			this.$nextTick(() => {
				this.$refs.drawerFormRef.clearValidate()
			})
			this.drawer = !this.drawer
		},
		getPropName(field) {
			return field.includes('.') ? field.split('.').pop() : field;
		},
		handleCancel() {
			this.formData = {}
			this.$nextTick(() => {
				this.$refs.drawerFormRef.clearValidate()
			})
			this.drawer = false
		},
		async handleCreate() {
			if (this.drawerLoading) {
				return;
			}
			if (!this.api_save || typeof this.api_save !== 'function') {
				return this.$message.error('请配置 api_save 参数');
			}
			// 表单校验
			try {
				await this.$refs.drawerFormRef.validate();
			} catch (error) {
				return;
			}
			let result = null;
			try {
				this.drawerLoading = true;
				result = await this.api_save(this.formData)
				if (result.code == 0) {
					this.$message.success(this.$t('public.success'))
					this.$emit('refresh')
					this.drawer = false;
				}
			} catch (err) {
				console.error(err)
				this.$message.error(err || this.$t('public.error'));
			} finally {
				this.drawerLoading = false;
				// this.$emit('toolbarClick', { code: 'create', result });
			}
		},
		handleEdit(row) {
			this.currentRow = row
			this.status = '1'
			this.drawer = true
			this.$nextTick(() => {
				this.formData = Object.assign({}, row) // 拷贝数据
			})
		},
		handleSelectItem(param) {
			console.log(param);
			if (param.field == 'barcodeMenu.name' && param.obj.id) {
				this.formData.prdNo = param.obj.id
			}
			if (param.field == 'barcodeMenu.whName' && param.obj.data) {
				this.formData.wh = param.obj.data.wh
				this.formData.whName = param.obj.data.whName
			}
		}
	},
}
</script>
