<template>
  <div class="layout">
    <vTable ref="vTable" v-bind="vTableProps" @toolbarClick="handleToolbarClick" @selectListEvent="handleSelectItem" :print-type="printType">
    </vTable>
  </div>
</template>
<script>
import vTable from '@/components/amtxts/vTable/vGrid.vue'
import { chuwdel, chuwquery, } from '@/api/salm'
export default {
  components: {
    vTable, 
  },
  data() {
    return {
      printType: 0,
      vTableProps: {
        delete_key: '$ITEM',
        api_find: chuwquery,
        api_delete: chuwdel,
        toolbarItems: [
          { label: '储位存放设定', value: 'title' },
        ],
        formDataRaw: [
          {
            field: 'barcodeMenu.wh', /* type: 'selectList', */ type:'input',
            props: {
              url: "/barcode/wh/pageCw",
              tableColumn: [
                { type: 'radio', width: 50 },
                { field: 'chUw', title: 'barcodeMenu.chUw', align: 'center', },
                { field: 'chUwName', title: 'barcodeMenu.chUwName', align: 'center', },
                { field: 'wh', title: 'barcodeMenu.wh', align: 'center', },
                { field: 'whName', title: 'barcodeMenu.whName', align: 'center', },
              ],
              form: {
                data: {
                  wh: '',
                },
                items: [
                  { field: 'chUwName', title: 'barcodeMenu.chUwName', itemRender: { name: '$input', } },
                  { field: 'whName', title: 'barcodeMenu.whName', itemRender: { name: '$input', } },
                  {
                    itemRender: {
                      name: '$buttons',
                      children: [
                        { props: { type: 'submit', content: 'public.query', status: 'primary' } },
                        { props: { type: 'reset', content: 'public.reset' } }]
                    }
                  }
                ]
              },
            },
          },
        ],
        tableColumn: [
          { field: "chuw", title: "barcodeMenu.chUw", },
          { field: "chuwName", title: "barcodeMenu.chUwName", },
          { field: "wh", title: "barcodeMenu.wh", },
          { field: "whName", title: "barcodeMenu.whName", },
          { field: 'prdNo', title: 'barcodeMenu.prdNo', },
          { field: 'prdMark', title: 'barcodeMenu.prdMark', },
          { field: 'qty', title: 'barcodeMenu.qty', },
          { field: 'rem', title: 'barcodeMenu.rem', },
        ],
      }
    }
  },
  activated(){
    const { code } = this.$route.params
    if(code == 'query'){
      this.handleSubmit();
    }
  },
  methods: {
    handleToolbarClick(params) {
      switch (params.code) {
        case 'create':
          this.$router.push({ name: 'chuwDetail' , params:{ code: 'create'} })
          break;
        case 'edit':
          const clonedRow = { ...params.row }; // 副本，防止修改关联原对象
          this.$router.push({ name: 'chuwDetail', params: { code: 'edit', row: clonedRow }  })
          break;
        default:
      }
    },
    handleSubmit() {
      this.$refs.vTable.handleGet();
    },
    handleSelectItem(param) {
      this.$refs.vTable.formData['wh'] = param.obj.data.wh
    }
  },
}
</script>
