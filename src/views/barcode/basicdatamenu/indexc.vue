<template>
  <div class="consrm">
    <div style="text-align:right;margin-right:16px;position: absolute;top: 94px;right:0px;z-index:999">
      <a-button size='small' type="primary" style="margin-left:10px" v-if="permissions['chuwConfig_add']"
        @click="add()">{{ $t('public.add') }}</a-button>
      <!-- <a-button
                size='small'
                type="primary"
                style="margin-left:10px"
               
                :loading1="loading1"
              >{{ $t('public.synchro') }}</a-button> -->
      <!-- @click="handletog" -->

      <a-button size='small' type="primary" style="margin-left: 8px" v-if="permissions['chuwConfig_del']"
        @click="dropdownMenuEvent('remove')">{{ $t('public.delete') }}</a-button>
      <!-- <a-button
                      size='small'
                      type="primary"
                      style="margin-left: 8px"
                      @click="reset"
                    >{{ $t('public.reset') }}</a-button> -->
    </div>
    <el-tabs type="border-card">
      <el-tab-pane label="储位存放设定">
        <a-card :bordered="false">
          <a-spin :spinning="spinning">
            <a-row :gutter="8">
              <!-- <a-col :span="4">
          <div style="height:600px;overflow:scroll">
          <my-tree
            ref="tree"
            @onSelect="onSelect"
            :url="props.url"
            :params="props.params"
          ></my-tree>
          </div>
        </a-col> -->
              <a-col :span="24">
                <div class="table-page-search-wrapper">
                  <a-form layout="inline">
                    <a-row :gutter="48">
                      <!-- <a-col
                  :md="8"
                  :sm="24"
                >
                  <a-form-item label="设备状态名称">
                    <a-input
                      v-model="queryParam.name"
                      placeholder="请输入设备状态名称"
                    />
                  </a-form-item>
                </a-col>
                
                <a-col
                  :md="8"
                  :sm="24"
                >
                  <a-form-item label="转单标记">
                    <el-select
                        v-model="queryParam.zdFlag"
                        placeholder="请选择转单标记"
                        size="mini"
                        clearable
                        style="max-width:200px; width:100%;"
                      >
                    
                        <el-option
                          v-for="item in biltypedatatwo"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        ></el-option>
                      </el-select>
                  </a-form-item>
                </a-col> -->
                      <a-col :xs="24" :sm="24" :md="12" :lg="7" :xl="7">
                        <a-form-item :label="$t('储位')" v-bind="formItemLayout">
                          <el-popover placement="bottom" width="450" trigger="click" v-model="visiblechu"
                            ref="dcPopover" title="">

                            <div id="popover">
                              <el-input slot="reference" v-model="condition.chUw" clearable placeholder="请选择"
                                style="width:100%;" @focus="zsMcchu" @input="zsMcinputchu" size="mini"></el-input>
                              <el-table :data="chuList" @row-click="handdlechu"
                                style="width: 100%;overflow:scroll;height:350px;" border size="mini">
                                <el-table-column prop="chUw" label="储位"></el-table-column>
                                <el-table-column prop="chUwName" label="储位名称"></el-table-column>
                                <el-table-column prop="wh" label="仓库"></el-table-column>
                                <el-table-column prop="whName" label="仓库名称"></el-table-column>
                              </el-table>
                              <div style="display: flex;justify-content: space-between;margin: 2px">
                                <el-pagination background :page-sizes="[20, 30, 50, 100, 500]" :page-size="20"
                                  :pager-count="5" @size-change="pageSizeChangechu"
                                  :current-page="tablePagechu.currentPage" @current-change="currentChangechu"
                                  layout="total,sizes,prev, pager, next" :total="totalCountchu"></el-pagination>
                              </div>

                            </div>
                            <el-input slot="reference" v-model="condition.chUw" clearable placeholder="请选择"
                              style="width:100%;" @focus="zsMcchu" @input="zsMcinputchu" size="mini"></el-input>
                            <!-- @clear="handleEmpty" -->
                          </el-popover>

                        </a-form-item>
                      </a-col>
                      <a-col :xs="24" :sm="24" :md="12" :lg="7" :xl="7">
                        <a-form-item :label="$t('仓库')" v-bind="formItemLayout">
                          <el-popover placement="bottom" width="450" trigger="click" v-model="visiblecang"
                            ref="dcPopover" title="">
                            <div id="popover">
                              <el-input slot="reference" v-model="condition.wh" clearable placeholder="请选择"
                                style="width:100%;" @focus="zsMccang" @input="zsMcinputcang" size="mini"></el-input>
                              <el-table :data="cangList" @row-click="handdlecang"
                                style="width: 100%;overflow:scroll;height:350px;" border size="mini">
                                <el-table-column prop="wh" label="仓库"></el-table-column>
                                <el-table-column prop="whName" label="仓库名称"></el-table-column>
                              </el-table>
                              <div style="display: flex;justify-content: space-between;margin: 2px">
                                <el-pagination background :page-sizes="[20, 30, 50, 100, 500]" :page-size="20"
                                  :pager-count="6" @size-change="pageSizeChangecang"
                                  :current-page="tablePagecang.currentPage" @current-change="currentChangecang"
                                  layout="total,sizes,prev, pager, next" :total="totalCountcang"></el-pagination>
                              </div>
                            </div>
                            <el-input slot="reference" v-model="condition.wh" clearable placeholder="请选择"
                              style="width:100%;" @focus="zsMccang" @input="zsMcinputcang" size="mini"></el-input>
                            <!-- @clear="handleEmpty" -->
                          </el-popover>

                        </a-form-item>
                      </a-col>
                      <a-col :xs="24" :sm="24" :md="12" :lg="7" :xl="7">
                        <a-form-item :label="$t('品号')" v-bind="formItemLayout">
                          <el-popover placement="bottom" width="500" trigger="click" v-model="visibletwo"
                            ref="dcPopover" title="">

                            <div id="popover">
                              <el-input slot="reference" v-model="condition.prdNo" clearable placeholder="请选择"
                                style="width:100%;" @focus="zsMc" @input="zsMcinput" size="mini"></el-input>
                              <el-table :data="prdNoList" @row-click="handdle"
                                style="width: 100%;overflow:scroll;height:350px;" border size="mini">
                                <el-table-column prop="prdNo" label="代号"></el-table-column>
                                <el-table-column prop="name" label="名称"></el-table-column>
                              </el-table>
                              <div style="display: flex;justify-content: space-between;margin: 2px">
                                <el-pagination background :page-sizes="[20, 30, 50, 100, 500]" :page-size="20"
                                  :pager-count="6" @size-change="pageSizeChangetwo"
                                  :current-page="tablePagetwo.currentPage" @current-change="currentChangetwo"
                                  layout="total,sizes,prev, pager, next" :total="totalCounttwo"></el-pagination>
                              </div>

                            </div>
                            <el-input slot="reference" v-model="condition.prdNo" clearable placeholder="请选择"
                              style="width:100%;" @focus="zsMc" @input="zsMcinput" size="mini"
                              :disabled="formIndex"></el-input>
                            <!-- @clear="handleEmpty" -->
                          </el-popover>

                        </a-form-item>
                      </a-col>
                      <a-col :md="8" :sm="24" style="margin-top:5px;">
                        <span class="table-page-search-submitButtons">
                          <a-button size='small' type="primary" @click="search"
                            v-if="permissions['chuwConfig_search']">{{ $t('public.query') }}</a-button>
                        </span>
                      </a-col>
                    </a-row>
                  </a-form>
                </div>
                <vxe-toolbar custom>
                  <template v-slot:buttons>
                    <!-- <a-dropdown :trigger="['click']">
                <a-button size='small'>
                  {{ $t('public.action') }}
           
                </a-button>
                <a-menu slot="overlay">
                  <a-menu-item key="0">
                    <a @click="dropdownMenuEvent('remove')">{{ $t('public.delete') }}</a>
                  </a-menu-item>
                </a-menu>
              </a-dropdown> -->

                  </template>
                </vxe-toolbar>
                <vxe-table size='small' border resizable stripe highlight-current-row show-overflow highlight-hover-row
                  export-config ref="xTable" :loading="loading" :data="tableData" :keyboard-config="{ isArrow: true }"
                  @cell-dblclick="cellDBLClickEvent" :edit-config="{ trigger: 'click', mode: 'row' }"
                  @radio-change="handleRadioChange">
                  <vxe-table-column type="radio" fixed="left" align="center" :width="50"></vxe-table-column>
                  <vxe-table-column field="chuw" fixed="left" title="储位" align="center"></vxe-table-column>
                  <vxe-table-column field="wh" fixed="left" title="仓库" align="center"></vxe-table-column>
                  <vxe-table-column field="prdNo" title="品号" align="center">
                  </vxe-table-column>
                  <vxe-table-column field="prdName" title="品名" align="center"></vxe-table-column>
                  <vxe-table-column field="prdMark" title="特征" align="center"></vxe-table-column>
                  <vxe-table-column field="qty" title="存放数量" align="center"></vxe-table-column>
                  <vxe-table-column field="rem" title="备注" align="center"></vxe-table-column>
                </vxe-table>
                <vxe-pager :loading="loading" :current-page="tablePage.currentPage" :page-size="tablePage.pageSize"
                  :total="tablePage.total"
                  :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
                  @page-change="handlePageChange"></vxe-pager>
              </a-col>
              <!-- 添加弹出框 -->
              <salm-drawer ref="modal" @onOk="onOk" />
            </a-row>
          </a-spin>
        </a-card>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import Vue from 'vue'
import { tenantList } from '@/api/login'
import { DEFAULT_TENANT_ID } from '@/store/mutation-types'
import { qcItmpage, spcLstdel, getTog, getTogto, spcLstpage, getAllStateSetPage, equStateSetdel, chuwdel, chuwquery, prdpage, whpageCw, whpageWh } from '@/api/salm'
import { deptTree } from '@/api/admin/dept'
import salmDrawer from './salmDrawer'
import { mapGetters } from 'vuex'
import MyTree from '@/components/MyTree'
export default {
  name: 'SalmList',
  components: {
    salmDrawer,
    MyTree
  },
  data() {
    return {
      node: {},
      spinning: false,
      props: {
        // url: '/admin/dept/lazyTree',
        url: '/mes/qcItm/allQcitm',
        params: 'qcItm' // 索引
        // deptId
      },
      tablePage: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      },
      loading: false,
      loading1: false,
      // 查询参数
      queryParam: {},
      tableData: [],
      biltypedatatwo: [
        { label: '全部', value: '' },
        { label: '是', value: 'T' },
        { label: '否', value: 'F' },
      ],
      radiorow: {},
      visibletwo: false,
      condition: {
        prdNo: '',
        prdName: '',
        wh: '',
        whName: '',
        chUw: '',
        chUwName: '',
      },
      prdNoList: [],
      cangList: [],
      chuList: [],
      tablePagetwo: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      },
      tablePagecang: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      },
      tablePagechu: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      },
      visiblechu: false,
      visiblecang: false,
      totalCountcang: '',
      totalCountchu: ''
    }
  },
  created() {
    this.getList()
    this.prdNoListmethod()
    this.cangmethod()
    this.chuweimethod()
  },
  computed: {
    ...mapGetters(['permissions'])
  },
  methods: {
    zsMcchu() {
      this.chuweimethod()
    },
    zsMcinputchu() {
      this.tablePagechu.currentPage = 1
      this.chuweimethod()
    },
    zsMccang() {
      this.cangmethod()
    },
    zsMcinputcang() {
      this.tablePagecang.currentPage = 1
      this.cangmethod()
    },
    zsMc() {
      this.prdNoListmethod()
    },
    zsMcinput() {
      this.tablePagetwo.currentPage = 1
      this.prdNoListmethod()
    },
    pageSizeChangechu(pageSize) {
      this.tablePagechu.pageSize = pageSize;
      this.chuweimethod()
    },
    currentChangechu(currentPage) {
      this.tablePagechu.currentPage = currentPage;
      this.chuweimethod()
    },
    pageSizeChangetwo(pageSize) {
      this.tablePagetwo.pageSize = pageSize;
      this.prdNoListmethod()
    },
    currentChangetwo(currentPage) {
      this.tablePagetwo.currentPage = currentPage;
      this.prdNoListmethod()
    },
    pageSizeChangecang(pageSize) {
      this.tablePagecang.pageSize = pageSize;
      this.cangmethod()
    },
    currentChangecang(currentPage) {
      this.tablePagecang.currentPage = currentPage;
      this.cangmethod()
    },



    handdle(row, event, column) {
      this.condition.prdNo = row.prdNo
      this.condition.prdName = row.name
      this.condition.prdMark = row.prdMark
      this.visibletwo = false;
    },
    handdlechu(row) {
      this.condition.chUw = row.chUw
      this.condition.chUwName = row.chUwName
      this.condition.wh = row.wh
      this.condition.whName = row.whName
      this.visiblechu = false;
    },
    handdlecang(row) {
      this.condition.wh = row.wh
      this.condition.whName = row.whName
      this.visiblecang = false;
    },
    cangmethod() {
      this.loading = false
      whpageWh(
        {
          current: this.tablePagecang.currentPage,
          size: this.tablePagecang.pageSize,
          wh: this.condition.wh
        }).then(response => {
          this.cangList = response.data.records
          this.totalCountcang = response.data.total
        }).catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    chuweimethod() {
      this.loading = false
      whpageCw({
        current: this.tablePagechu.currentPage,
        size: this.tablePagechu.pageSize,
        chUw: this.condition.chUw
      }).then(response => {
        this.chuList = response.data.records
        this.totalCountchu = response.data.total
      }).catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    prdNoListmethod() {
      this.loading = false
      prdpage(
        {
          current: this.tablePagetwo.currentPage,
          size: this.tablePagetwo.pageSize,
          prdNo: this.condition.prdNo,
        }).then(response => {
          this.prdNoList = response.data.records
          this.totalCounttwo = response.data.total
        }).catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    handleRadioChange({ row }) {
      this.radiorow = row

    },
    search() {
      this.tablePage.currentPage = 1
      this.getList()
    },
    // 人员同步
    async handletog() {
      this.loading1 = true
      this.spinning = true
      const TENANT_ID = Vue.ls.get(DEFAULT_TENANT_ID)
      const tenant = await tenantList()
      const newArr = tenant.data.find(i => {
        return i.value === TENANT_ID
      })
      const dataSourceVo = newArr.dataSourceVo
      if (JSON.stringify(dataSourceVo) !== '{}') {
        try {
          const res = await deptTree()
          if (res.data.length !== 0) {
            const res1 = await getTog()
            if (res1.data > 0) {
              const res2 = await getTogto()
              if (res2) {
                this.loading1 = false
                this.spinning = false
                this.$message.success(this.$t('public.success'))
                this.getList()
              } else {
                this.$message.error(this.$t('public.error'))
                this.loading1 = false
                this.spinning = false
              }
            } else {
              this.spinning = false
              this.loading1 = false
              this.$message.success(this.$t('public.success'))
              this.getList()
            }
          } else {
            this.spinning = false
            this.loading1 = false
            this.$notification.error({
              message: this.$t('public.del.title'),
              description: this.$t('public.dept_tog')
            })
          }
        } catch (err) {
          this.loading1 = false
          this.spinning = false
        }
      } else {
        this.loading = false
        this.spinning = false
        this.$notification.error({
          message: this.$t('public.del.title'),
          description: this.$t('public.set')
        })
      }
    },
    getList() {
      // fetchList
      this.loading = true
      chuwquery(
        Object.assign(
          {
            // stateId: this.queryParam.stateId,
            // name: this.queryParam.name,
            // zdFlag: this.queryParam.zdFlag,
            current: this.tablePage.currentPage,
            size: this.tablePage.pageSize,
            ...this.condition
            // flag:1
          }
        )
      )
        .then(res => {
          this.tableData = res.data.records
          this.tableData.forEach((item, index) => {
            item.addid = index + 1
          })
          this.tablePage.total = res.data.total
          this.tablePage.currentPage = res.data.current
          this.loading = false
        })
        .catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    // 分页
    handlePageChange({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getList()
    },
    onSelect(node) {


      // this.queryParam.dep = node.dataRef.deptCode
      this.queryParam.qcItm = node.dataRef.qcItm
      this.node = node
      this.getList()
    },
    // 重置搜索内容
    reset() {
      this.node = ''
      this.queryParam = {}
      // this.$refs.tree.onLoadData(this.node)
      this.getList()
    },
    // 新增
    add() {
      const obj = {
        id: this.node.dataRef === undefined ? '' : this.node.dataRef.id,
        name: this.node.dataRef === undefined ? '' : this.node.dataRef.name
      }
      this.$refs.modal.create({ title: this.$t('public.add') }, obj)
    },
    // 双击弹出编辑框
    cellDBLClickEvent({ row }) {
      this.$refs.modal.edit({ title: this.$t('public.Detailed') }, row)
    },
    onOk() {
      this.reset()
      // this.$refs.tree.onLoadData(this.node)
    },
    // 删除按钮
    dropdownMenuEvent(name) {
      switch (name) {
        case 'remove': {

          // const selectRecords = this.$refs.xTable.getCheckboxRecords()
          if (this.radiorow) {
            // const arr = []
            // selectRecords.forEach(i => {
            //   return arr.push(i.stateId)
            // })
            const that = this
            this.$confirm({
              title: this.$t('public.del.title'),
              content: this.$t('public.del.content'),
              okText: this.$t('public.sure'),
              okType: 'danger',
              cancelText: this.$t('public.cancel'),
              onOk() {
                // delAll
                that.loading = true

                let wmsBarcodeCusFrom = that.radiorow
                chuwdel(wmsBarcodeCusFrom)
                  .then(() => {
                    that.getList()
                    that.loading = false
                    // that.$refs.tree.getTree()
                    that.$message.success(that.$t('public.success'))
                  })
                  .catch(err => that.requestFailed(err))
                  .finally(() => {
                    that.loading = false
                  })
              },
              onCancel() {
                that.loading = false
              }
            })
          } else {
            this.$message.warning(this.$t('public.list'))
          }
          break
        }
      }
    }

  }
}
</script>
<style lang="less"></style>
