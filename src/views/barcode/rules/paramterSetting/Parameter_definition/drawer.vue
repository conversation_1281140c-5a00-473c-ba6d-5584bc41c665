<template>
  <div>
    <a-drawer
      placement="right"
      :closable="false"
      @close="onClose"
      :visible="visible"
      :destroyOnClose="true"
      width="70%"
    >
      <template slot="title">
        <span class="title-name">{{ title }}</span>
        <span
          v-if="this.modeType!=='0'"
          class="title-age"
        >
          <a-dropdown v-permission="barcode_parameter_setting_del">
            <a-button class="ant-dropdown-link">
              {{ $t('public.action') }}
              <a-icon type="down" />
            </a-button>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="delet()">{{ $t('public.delete') }}</a>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>
      </template>
      <a-form-model
        layout="horizontal"
        ref="ruleForm"
        :rules="rules"
        :model="form"
      >
        <a-row>
          <a-col>
            <a-form-model-item
              v-bind="formItemLayout"
              :label="$t('paramterSetting.zc01')"
              prop="zc01"
            >
              <a-input
                :max-length="10"
                v-model="form.zc01"
                :disabled="formStatus"
                :placeholder="$t('paramterSetting.placeholder.zc01')"
              />
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-model-item
              v-bind="formItemLayout"
              :label="$t('paramterSetting.zc02')"
              prop="zc02"
            >
              <a-input
                :max-length="10"
                v-model="form.zc02"
                :disabled="formStatus"
                :placeholder="$t('paramterSetting.placeholder.zc02')"
              />
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-model-item
              v-bind="formItemLayout"
              :label="$t('paramterSetting.cskz')"
            >
              <a-radio-group
                :disabled="disabled"
                v-model="form.cskz"
              >
                <a-radio-button
                  value="T"
                  style="margin-right:20px"
                >{{ $t('public.T') }}</a-radio-button>
                <a-radio-button value="F">{{ $t('public.F') }}</a-radio-button>
              </a-radio-group>
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-model-item
              v-bind="formItemLayout"
              :label="$t('paramterSetting.wgkz')"
            >
              <a-radio-group
                :disabled="disabled"
                v-model="form.wgkz"
              >
                <a-radio-button
                  value="T"
                  style="margin-right:20px"
                >{{ $t('public.T') }}</a-radio-button>
                <a-radio-button value="F">{{ $t('public.F') }}</a-radio-button>
              </a-radio-group>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
      <a-row :gutter="16">
        <a-col
          class="gutter-row"
          :span="12"
          style="text-align:right"
        >
          <a-button
            type="primary"
            :loading="loading"
            v-if="modeType==='0'"
            @click="handleOK()"
            v-permission="barcode_parameter_setting_save"
          >{{ $t('public.save') }}</a-button>
          <a-button
            type="primary"
            v-if="modeType==='1'"
            @click="handleMenuClick()"
            v-permission="barcode_parameter_setting_save"
          >{{ $t('public.edit') }}</a-button>
          <a-button
            type="primary"
            :loading="loading"
            v-if="modeType==='2'"
            @click="handleEdit()"
            v-permission="barcode_parameter_setting_save"
          >{{ $t('public.save') }}</a-button>
        </a-col>
        <a-col
          class="gutter-row"
          :span="12"
          style="text-align:left"
        >
          <a-button @click="onClose">{{ $t('public.cancel') }}</a-button>
        </a-col>
      </a-row>
    </a-drawer>
  </div>
</template>

<script>
import { add, edit, del, check } from '@/api/barcode/paramterSetting/Parameter_definition'
export default {
  data () {
    return {
      barcode_parameter_setting_save: 'barcode_parameter_setting_save',
      barcode_parameter_setting_del: 'barcode_parameter_setting_del',
      title: '',
      disabled: false,
      visible: false,
      formStatus: false,
      loading: false,
      modeType: '',
      dep: '',
      row: {},
      rule: false,
      form: {
        cskz: 'T',
        wgkz: 'T'
      },
      rules: {
        zc01: [
          { required: true, message: this.$t('paramterSetting.placeholder.zc01'), trigger: 'blur' }
        ],
        zc02: [
          { required: true, validator: this.handlePass, trigger: 'blur' }
        ]
      },
      formItemLayout: {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        }
      }
    }
  },
  created () {

  },
  methods: {
    // 校验
    handlePass (rule, value, callback) {
      if (this.rule === true) {
        if (value) {
          check({
            type: 'zckz',
            id: value,
            itmId: ''
          }).then(response => {
           
            const result = response.msg
            if (result == 'fail') {
              callback(new Error('该工序已重复 请重新输入！'))
            } else {
              callback()
            }
          }).catch(err => this.requestFailed(err))
        } else {
          callback(new Error('请输入二级工序！'))
        }
      }

    },
    // 取消
    onClose () {
      this.rule = false
      this.loading = false
      this.visible = false
      this.form = {
        cskz: 'T',
        wgkz: 'T'
      }
    },
    create (model, row) {
      this.rule = true
      this.title = model.title
      this.modeType = '0'
      this.visible = true
      this.formStatus = false
      this.disabled = false
    },
    // 点击编辑按钮
    handleMenuClick () {
      this.rule = false
      this.title = this.$t('public.edit')
      this.formStatus = true
      this.modeType = '2'
      this.disabled = false
    },
    edit (model, row) {
      this.rule = false
      this.disabled = true
      this.title = model.title
      this.modeType = '1'
      this.row = row
      this.formStatus = true
      this.visible = true
      this.form = {
        cskz: row.cskz,
        wgkz: row.wgkz,
        zc01: row.zc01,
        zc02: row.zc02
      }
    },
    // 添加确认
    handleOK () {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          this.loading = true
       
          add(this.form).then((res) => {
            this.loading = false
            this.onClose()
            this.$emit('getList')
            this.$message.success(this.$t('public.success'))
          }).catch(err => this.requestFailed(err))
            .finally(() => {
              this.loading = false
            })
        } else {
          this.loading = false
          this.$message.error(this.$t('public.error'))
          return false
        }
      })
    },
    delet () {
      const that = this
      this.$confirm({
        title: this.$t('public.del.title'),
        content: this.$t('public.del.content'),
        okText: this.$t('public.sure'),
        okType: 'danger',
        cancelText: this.$t('public.cancel'),
        onOk () {
          that.loading = true
          del({ zc02: that.row.zc02 })
            .then(() => {
              that.loading = false
              that.onClose()
              that.$emit('getList')
              that.$message.success(that.$t('public.success'))
            })
            .catch(err => that.requestFailed(err))
            .finally(() => {
              that.loading = false
            })
        },
        onCancel () {
          that.loading = false
        }
      })
    },
    // 确认编辑
    handleEdit () {
      this.loading = true
      edit(this.form).then((res) => {
        this.loading = false
        this.onClose()
        this.$emit('getList')
        this.$message.success(this.$t('public.success'))
      }).catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
      // this.$refs.ruleForm.validate(valid => {
      //   if (valid) {
      //     this.loading = true
      //     edit(this.form).then((res) => {
      //       this.loading = false
      //       this.onClose()
      //       this.$emit('getList')
      //       this.$message.success(this.$t('public.success'))
      //     })
      //   } else {
      //     this.loading = false
      //     this.$message.error(this.$t('public.error'))
      //     return false
      //   }
      // })
    }
  }
}
</script>
