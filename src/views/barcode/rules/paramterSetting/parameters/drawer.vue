<template>
  <div>
    <a-drawer
      placement="right"
      :closable="false"
      @close="onClose"
      :visible="visible"
      :destroyOnClose="true"
      width="70%"
    >
      <template slot="title">
        <span class="title-name">{{ title }}</span>
        <span
          v-if="this.modeType!=='0'"
          class="title-age"
        >
          <a-dropdown v-permission="barcode_parameters_drawer_del">
            <a-button class="ant-dropdown-link">
              {{ $t('public.action') }}
              <a-icon type="down" />
            </a-button>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="delet()">{{ $t('public.delete') }}</a>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>
      </template>
      <a-form-model
        layout="horizontal"
        ref="ruleForm"
        :rules="rules"
        :model="form"
      >
        <a-row>
          <a-col span='12'>
            <a-form-model-item
              v-bind="formItemLayout"
              :label="$t('attribute_definition.paraNo')"
              prop="paraNo"
            >
              <a-input
                :max-length="20"
                v-model="form.paraNo"
                :disabled="disabled"
                :placeholder="$t('attribute_definition.placeholder.paraNo')"
              />
            </a-form-model-item>
          </a-col>

          <a-col span='12'>
            <a-form-model-item
              v-bind="formItemLayout"
              :label="$t('attribute_definition.vMax')"
            >
              <a-input-number
                :disabled="formStatus"
                style="width:100%"
                :min="0"
                :placeholder="$t('attribute_definition.placeholder.vMax')"
                v-model="form.vMax"
                :max="9999"
              />
            </a-form-model-item>
          </a-col>
          <a-col span='12'>
            <a-form-model-item
              v-bind="formItemLayout"
              :label="$t('attribute_definition.vMin')"
            >
              <a-input-number
                :disabled="formStatus"
                style="width:100%"
                :min="0"
                :placeholder="$t('attribute_definition.placeholder.vMin')"
                v-model="form.vMin"
                :max="9999"
              />
            </a-form-model-item>
          </a-col>

          <a-col span='12'>
            <a-form-model-item
              v-bind="formItemLayout"
              :label="$t('attribute_definition.prdNo')"
              prop="prdNo"
            >
              <a-input
                :max-length="20"
                v-model="form.prdNo"
                :disabled="disabled"
                :placeholder="$t('attribute_definition.placeholder.prdNo')"
              />
            </a-form-model-item>
          </a-col>
          <a-col span='12'>
            <a-form-model-item
              v-bind="formItemLayout"
              :label="$t('attribute_definition.valid')"
            >
              <a-select
                :disabled="formStatus"
                style="width: 100%"
                :placeholder="$t('attribute_definition.placeholder.valid')"
                v-model="form.valid"
              >
                <a-select-option value="1">有效</a-select-option>
                <a-select-option value="2">无效</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col span='12'>
            <a-form-model-item
              v-bind="formItemLayout"
              :label="$t('attribute_definition.zcNo1')"
            >
              <a-input
                v-model="form.zcNo1"
                :disabled="disabled"
                :placeholder="$t('attribute_definition.placeholder.zcNo1')"
              />
            </a-form-model-item>
          </a-col>
          <a-col span='12'>
            <a-form-model-item
              v-bind="formItemLayout"
              :label="$t('attribute_definition.zcNo2')"
            >
              <a-input
                v-model="form.zcNo2"
                :disabled="disabled"
                :placeholder="$t('attribute_definition.placeholder.zcNo2')"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
      <a-row :gutter="16">
        <a-col
          class="gutter-row"
          :span="12"
          style="text-align:right"
        >
          <a-button
            type="primary"
            :loading="loading"
            v-if="modeType==='0'"
            @click="handleOK()"
            v-permission="barcode_parameters_drawer_save"
          >{{ $t('public.save') }}</a-button>
          <a-button
            type="primary"
            v-if="modeType==='1'"
            @click="handleMenuClick()"
            v-permission="barcode_parameters_drawer_save"
          >{{ $t('public.edit') }}</a-button>
          <a-button
            type="primary"
            :loading="loading"
            v-if="modeType==='2'"
            @click="handleEdit()"
            v-permission="barcode_parameters_drawer_save"
          >{{ $t('public.save') }}</a-button>
        </a-col>
        <a-col
          class="gutter-row"
          :span="12"
          style="text-align:left"
        >
          <a-button @click="onClose">{{ $t('public.cancel') }}</a-button>
        </a-col>
      </a-row>
    </a-drawer>
  </div>
</template>

<script>
import { add, edit, del } from '@/api/barcode/paramterSetting/parameters'
import { check } from '@/api/barcode/paramterSetting/Parameter_definition'
export default {
  data () {
    return {
      barcode_parameters_drawer_del: 'barcode_parameters_drawer_del',
      barcode_parameters_drawer_save: 'barcode_parameters_drawer_save',
      title: '',
      disabled: false,
      visible: false,
      formStatus: false,
      loading: false,
      modeType: '',
      dep: '',
      row: {},
      form: {
      },
      rule: false,
      rules: {
        paraNo: [
          { required: true, validator: this.handlePass, trigger: 'blur' }
        ]
      },
      formItemLayout: {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        }
      }
    }
  },
  created () {

  },
  methods: {
    handlePass (rule, value, callback) {
      if (this.rule) {
        if (value) {
          check({
            type: 'pro',
            id: value,
          }).then(response => {
            const result = response.msg
            if (result == 'fail') {
              callback(new Error('该代号已重复 请重新输入！'))
            } else {
              callback()
            }
          }).catch(err => this.requestFailed(err))
        } else {
          callback(new Error('请输入参数代号！'))
        }
      }

    },
    // 取消
    onClose () {
      this.rule = false
      this.loading = false
      this.visible = false
      this.form = {}
    },
    create (model, row) {
      this.rule = true
      this.title = model.title
      this.modeType = '0'
      this.visible = true
      this.formStatus = false
      this.disabled = false
    },
    // 点击编辑按钮
    handleMenuClick () {
      this.rule = false
      this.title = this.$t('public.edit')
      this.formStatus = false
      this.modeType = '2'
      this.disabled = true
    },
    edit (model, row) {
      this.rule = false
      this.disabled = true
      this.title = model.title
      this.modeType = '1'
      this.row = row
      this.formStatus = true
      this.visible = true
      this.form = {
        ...row
      }
    },
    // 添加确认
    handleOK () {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          this.loading = true
          add(this.form).then((res) => {
            this.loading = false
            this.onClose()
            this.$emit('getList')
            this.$message.success(this.$t('public.success'))
          }).catch(err => this.requestFailed(err))
            .finally(() => {
              this.loading = false
            })
        } else {
          this.loading = false
          this.$message.error(this.$t('public.error'))
          return false
        }
      })
    },
    delet () {
      const that = this
      this.$confirm({
        title: this.$t('public.del.title'),
        content: this.$t('public.del.content'),
        okText: this.$t('public.sure'),
        okType: 'danger',
        cancelText: this.$t('public.cancel'),
        onOk () {
          that.loading = true
          del({ paraNo: that.row.paraNo })
            .then(() => {
              that.loading = false
              that.onClose()
              that.$emit('getList')
              that.$message.success(that.$t('public.success'))
            })
            .catch(err => that.requestFailed(err))
            .finally(() => {
              that.loading = false
            })
        },
        onCancel () {
          that.loading = false
        }
      })
    },
    // 确认编辑
    handleEdit () {
      this.loading = true
      edit(this.form).then((res) => {
        this.loading = false
        this.onClose()
        this.$emit('getList')
        this.$message.success(this.$t('public.success'))
      }).catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
      // this.$refs.ruleForm.validate(valid => {
      //   if (valid) {
      //     this.loading = true
      //     edit(this.form).then((res) => {
      //       this.loading = false
      //       this.onClose()
      //       this.$emit('getList')
      //       this.$message.success(this.$t('public.success'))
      //     })
      //   } else {
      //     this.loading = false
      //     this.$message.error(this.$t('public.error'))
      //     return false
      //   }
      // })
    }
  }
}
</script>
