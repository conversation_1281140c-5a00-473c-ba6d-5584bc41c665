<template>
<div class="consrm">
<div  style="text-align:right;margin-right:16px;position: absolute;top: 102px;right:0px;z-index:999">
        <a-button size="small" style="margin-left:10px" type="primary" icon="plus" @click="handleAdd()" v-permission="barcode_attribute_add">{{
          $t('public.add')
        }}</a-button>
         <a-button size="small" type="primary" style="margin-left: 8px" @click="reset" v-permission="barcode_attribute_reset">{{ $t('public.reset') }}</a-button>
  </div>
<el-tabs type="border-card">
      <el-tab-pane label="工序参数-属性定义">
  <a-card :bordered="false">
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="48">
          <a-col :md="6" :sm="24">
            <a-form-item :label="$t('parameters.zcNo')">
              <a-input v-model="queryParam.zcNo" :placeholder="$t('parameters.placeholder.zcNo')" />
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24">
            <a-form-item :label="$t('parameters.paraNo')">
              <a-input v-model="queryParam.paraNo" :placeholder="$t('parameters.placeholder.paraNo')" />
            </a-form-item>
          </a-col>

          <a-col :md="6" :sm="24">
            <span class="table-page-search-submitButtons">
              <a-button size="small" type="primary" @click="search" v-permission="barcode_attribute_search">{{ $t('public.query') }}</a-button>
             
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <vxe-toolbar custom>
      <template v-slot:buttons>
        <!-- <a-dropdown :trigger="['click']">
          <a-button>{{ $t('public.action') }}<a-icon type="down"/></a-button>
          <a-menu slot="overlay">
            <a-menu-item key="0">
              <a @click="dropdownMenuEvent('closed ')">{{ $t('submission.closed') }}</a>
            </a-menu-item>
          </a-menu>
        </a-dropdown> -->
      
      </template>
    </vxe-toolbar>
    <vxe-table
      border
      size="small"
      resizable
      stripe
      highlight-current-row
      show-overflow
      highlight-hover-row
      export-config
      ref="xTable"
      :loading="loading"
      :data="tableData"
      @cell-dblclick="cellDBLClickEvent"
      :keyboard-config="{ isArrow: true }"
      :edit-config="{ trigger: 'click', mode: 'row' }"
    >
      <vxe-table-column :min-width="90" field="paraNo" title="parameters.paraNo" align="center"></vxe-table-column>
      <vxe-table-column :min-width="90" field="name" title="parameters.name" align="center"></vxe-table-column>
      <vxe-table-column :min-width="90" field="type" title="parameters.type" align="center">
        <template slot-scope="scope">
          <span color="blue" type="primary">{{ scope.row.type | val }}</span>
        </template>
      </vxe-table-column>
      <vxe-table-column field="bi" title="parameters.bl" :min-width="70" align="center">
        <template slot-scope="scope">
          <span color="blue" type="primary">{{ scope.row.bi | bi }}</span>
        </template>
      </vxe-table-column>
      <vxe-table-column :min-width="70" field="dd" title="parameters.dd" align="center"></vxe-table-column>
      <vxe-table-column :min-width="70" field="vmax" title="parameters.v_max" align="center"></vxe-table-column>
      <vxe-table-column field="vmin" :min-width="70" title="parameters.v_min" align="center"></vxe-table-column>
      <vxe-table-column :min-width="70" field="vlen" title="parameters.v_len" align="center"></vxe-table-column>
      <vxe-table-column field="zcNo" :min-width="90" title="parameters.zcNo" align="center"></vxe-table-column>
      <vxe-table-column :min-width="90" field="zcJb" title="parameters.zc_jb" align="center"></vxe-table-column>
      <vxe-table-column :min-width="90" field="rem" title="parameters.rem" align="center"></vxe-table-column>
      <vxe-table-column :min-width="90" field="unit" title="parameters.unit" align="center"></vxe-table-column>
      <vxe-table-column :min-width="90" field="tygx" title="parameters.tygx" align="center">
        <template slot-scope="scope">
          <span color="blue" type="primary">{{ scope.row.tygx | tygx }}</span>
        </template>
      </vxe-table-column>
    </vxe-table>
    <vxe-pager
      :loading="loading"
      :current-page="tablePage.currentPage"
      :page-size="tablePage.pageSize"
      :total="tablePage.total"
      :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
      @page-change="handlePageChange"
    >
    </vxe-pager>
    <Drawer ref="Drawer" @getList="getList" />
  </a-card>
  </el-tab-pane>
</el-tabs>
</div>
</template>

<script>
import { fetchList } from '@/api/barcode/paramterSetting/attribute_definition'
import Drawer from './drawer'
export default {
  components: {
    Drawer
  },
  data() {
    return {
      barcode_attribute_search: 'barcode_attribute_search',
      barcode_attribute_reset: 'barcode_attribute_reset',
      barcode_attribute_add: 'barcode_attribute_add',
      tableData: [],
      loading: false,
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      queryParam: {
        zcNo: '',
        paraNo: ''
      },
      formItemLayout: {
        labelCol: {
          md: { span: 24 },
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          md: { span: 24 },
          xs: { span: 24 },
          sm: { span: 15 }
        }
      }
    }
  },
  filters: {
    val(theme) {
      const themeMap = {
        '1': '数值',
        '2': '字符',
        '3': '日期',
        '4': '日期+时间',
        '5': '罗列标准'
      }
      return themeMap[theme]
    },
    bi(val) {
      const themeMap = {
        '1': '必录',
        '0': '可控'
      }
      return themeMap[val]
    },
    tygx(val) {
      const themeMap = {
        '0': '统一参数',
        '1': '个性参数'
      }
      return themeMap[val]
    }
  },
  created() {
    this.getList()
  },
  methods: {
    search() {
      this.tablePage.currentPage = 1
      this.getList()
    },
    // 查询列表
    getList() {
      this.loading = true
      // eslint-disable-next-line no-undef
      fetchList(
        Object.assign({
          current: this.tablePage.currentPage,
          size: this.tablePage.pageSize,
          paraNo: this.queryParam.paraNo,
          zcNo: this.queryParam.zcNo
        })
      )
        .then(res => {
          this.tableData = res.data.records
          this.tablePage.total = res.data.total
          this.tablePage.currentPage = res.data.current
          this.loading = false
        })
        .catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    reset() {
      this.data = {}
      this.queryParam = {
        zcNo: '',
        paraNo: ''
      }
    },
    // 分页
    handlePageChange({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getList()
    },
    // 添加
    handleAdd() {
      this.$refs.Drawer.create({ title: this.$t('public.add') })
    },
    cellDBLClickEvent({ row }) {
      this.$refs.Drawer.edit({ title: this.$t('public.Detailed') }, row)
    }
  }
}
</script>
<style lang="less"></style>
