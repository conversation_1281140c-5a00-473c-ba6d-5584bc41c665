<template>
  <div>
    <a-drawer
      placement="right"
      :closable="false"
      @close="onClose"
      :visible="visible"
      :destroyOnClose="true"
      width="70%"
    >
      <template slot="title">
        <span class="title-name">{{ title }}</span>
        <span v-if="this.modeType !== '0'" class="title-age">
          <a-dropdown v-permission="barcode_heat_treatment_del">
            <a-button class="ant-dropdown-link">
              {{ $t('public.action') }}
              <a-icon type="down" />
            </a-button>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="delet()">{{ $t('public.delete') }}</a>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>
      </template>
      <div style="height:100%;overflow:auto;">
        <a-form-model layout="horizontal" ref="ruleForm" :rules="rules" :model="form">
          <a-row>
            <a-col>
              <!-- v-if="this.modeType==='0'" -->
              <a-form-model-item v-bind="formItemLayout" :label="$t('heat_treatment.rclNo')" prop="rclNo">
                <a-input
                  :max-length="20"
                  v-model="form.rclNo"
                  :disabled="disabled"
                  :placeholder="$t('heat_treatment.placeholder.rclNo')"
                />
              </a-form-model-item>
            </a-col>
            <!-- <a-col v-else>
              <a-form-model-item
                v-bind="formItemLayout"
                :label="$t('heat_treatment.rclNo')"
              >
                <a-input
                  :max-length='20'
                  v-model="form.rclNo"
                  :disabled="formStatus"
                  :placeholder="$t('heat_treatment.placeholder.rclNo')"
                />
              </a-form-model-item>
            </a-col> -->
            <a-col>
              <a-form-model-item v-bind="formItemLayout" :label="$t('heat_treatment.rclFs')">
                <a-input
                  v-model="form.rclFs"
                  :disabled="formStatus"
                  :placeholder="$t('heat_treatment.placeholder.rclFs')"
                />
              </a-form-model-item>
            </a-col>
            <a-col>
              <a-form-model-item v-bind="formItemLayout" :label="$t('heat_treatment.rclLxh')">
                <a-input
                  v-model="form.rclLxh"
                  :disabled="formStatus"
                  :placeholder="$t('heat_treatment.placeholder.rclLxh')"
                />
              </a-form-model-item>
            </a-col>
            <a-col>
              <a-form-model-item v-bind="formItemLayout" :label="$t('heat_treatment.rclWd')">
                <a-input-number
                  :disabled="formStatus"
                  style="width:100%"
                  :min="0"
                  :placeholder="$t('heat_treatment.placeholder.rclWd')"
                  v-model="form.rclWd"
                  :max="999"
                />
              </a-form-model-item>
            </a-col>
            <a-col>
              <a-form-model-item v-bind="formItemLayout" :label="$t('heat_treatment.rclBwHour')">
                <a-input-number
                  :disabled="formStatus"
                  style="width:100%"
                  :min="0"
                  :placeholder="$t('heat_treatment.placeholder.rclBwHour')"
                  v-model="form.rclBwHour"
                  :max="999"
                />
              </a-form-model-item>
            </a-col>
            <a-col>
              <a-form-model-item v-bind="formItemLayout" :label="$t('heat_treatment.moNoRem')">
                <a-input
                  v-model="form.moNoRem"
                  :disabled="formStatus"
                  :placeholder="$t('heat_treatment.placeholder.moNoRem')"
                />
              </a-form-model-item>
            </a-col>
            <a-col>
              <a-form-model-item v-bind="formItemLayout" :label="$t('heat_treatment.vaild')">
                <a-radio-group :disabled="formStatus" v-model="form.valid">
                  <a-radio-button value="1" style="margin-right:20px">{{ $t('public.T') }}</a-radio-button>
                  <a-radio-button value="2">{{ $t('public.F') }}</a-radio-button>
                </a-radio-group>
              </a-form-model-item>
            </a-col>
            <a-col>
              <a-form-model-item v-bind="formItemLayout" label="创建时间">
                <a-date-picker
                  placeholder="创建时间"
                  style="width:100%"
                  :disabled="formStatus"
                  v-model="form.createTime"
                />
              </a-form-model-item>
            </a-col>
            <a-col>
              <a-form-model-item v-bind="formItemLayout" :label="$t('heat_treatment.man')">
                <a-input
                  v-model="form.man"
                  :disabled="formStatus"
                  :placeholder="$t('heat_treatment.placeholder.man')"
                />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </div>
      <a-row :gutter="16">
        <a-col class="gutter-row" :span="12" style="text-align:right">
          <a-button type="primary" :loading="loading" v-if="modeType === '0'" @click="handleOK()" v-permission="barcode_heat_treatment_save">{{
            $t('public.save')
          }}</a-button>
          <a-button type="primary" v-if="modeType === '1'" @click="handleMenuClick()" v-permission="barcode_heat_treatment_save">{{ $t('public.edit') }}</a-button>
          <a-button type="primary" :loading="loading" v-if="modeType === '2'" @click="handleEdit()" v-permission="barcode_heat_treatment_save">{{
            $t('public.save')
          }}</a-button>
        </a-col>
        <a-col class="gutter-row" :span="12" style="text-align:left">
          <a-button @click="onClose">{{ $t('public.cancel') }}</a-button>
        </a-col>
      </a-row>
    </a-drawer>
  </div>
</template>

<script>
import { add, edit, del } from '@/api/barcode/paramterSetting/heat_treatment'
import { check } from '@/api/barcode/paramterSetting/Parameter_definition'
import moment from 'moment'
export default {
  data() {
    return {
      barcode_heat_treatment_save: 'barcode_heat_treatment_save',
      barcode_heat_treatment_del: 'barcode_heat_treatment_del',
      title: '',
      disabled: false,
      visible: false,
      formStatus: false,
      loading: false,
      modeType: '',
      dep: '',
      row: {},
      form: {
        valid: '1'
      },
      rule: false,
      rules: {
        rclNo: [{ required: true, validator: this.handlePass, trigger: 'blur' }]
      },
      formItemLayout: {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        }
      }
    }
  },
  created() {},
  methods: {
    // 校验
    handlePass(rule, value, callback) {
      if (this.rule) {
        if (value) {
          check({
            type: 'rcl',
            id: value
          }).then(response => {
            const result = response.msg
            if (result == 'fail') {
              callback(new Error('该热处理炉号已重复 请重新输入！'))
            } else {
              callback()
            }
          }).catch(err => this.requestFailed(err))
        } else {
          callback(new Error('请输入热处理炉号！'))
        }
      }
    },
    // 取消
    onClose() {
      this.rule = false
      this.loading = false
      this.visible = false
      this.form = {
        valid: '1'
      }
    },
    create(model, row) {
      this.rule = true
      this.title = model.title
      this.modeType = '0'
      this.visible = true
      this.formStatus = false
      this.disabled = false
    },
    // 点击编辑按钮
    handleMenuClick() {
      this.rule = false
      this.title = this.$t('public.edit')
      this.formStatus = false
      this.modeType = '2'
      this.disabled = true
    },
    edit(model, row) {
      this.rule = false
      this.disabled = true
      this.title = model.title
      this.modeType = '1'
      this.row = row
      this.formStatus = true
      this.visible = true
      this.form = {
        man: row.man,
        moNoRem: row.moNoRem,
        rclBwHour: row.rclBwHour,
        rclFs: row.rclFs,
        rclLxh: row.rclLxh,
        rclNo: row.rclNo,
        rclWd: row.rclWd,
        vali: row.valid,
        createTime: row.createTime ? moment(row.createTime).format('YYYY-MM-DD HH:mm:ss') : null
      }
    },
    // 添加确认
    handleOK() {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          let obj = {
            man: this.form.man,
            moNoRem: this.form.moNoRem,
            rclBwHour: this.form.rclBwHour,
            rclFs: this.form.rclFs,
            valid: this.form.valid,
            rclLxh: this.form.rclLxh,
            rclNo: this.form.rclNo,
            rclWd: this.form.rclWd,
            createTime: this.form.createTime ? moment(this.form.createTime).format('YYYY-MM-DD') : null
          }
          this.loading = true
          add(obj)
            .then(res => {
              this.loading = false
              this.onClose()
              this.$emit('getList')
              this.$message.success(this.$t('public.success'))
            })
            .catch(err => this.requestFailed(err))
            .finally(() => {
              this.loading = false
            })
        } else {
          this.loading = false
          this.$message.error(this.$t('public.error'))
          return false
        }
      })
    },
    delet() {
      const that = this
      this.$confirm({
        title: this.$t('public.del.title'),
        content: this.$t('public.del.content'),
        okText: this.$t('public.sure'),
        okType: 'danger',
        cancelText: this.$t('public.cancel'),
        onOk() {
          that.loading = true
          del({ rclNo: that.row.rclNo })
            .then(() => {
              that.loading = false
              that.onClose()
              that.$emit('getList')
              that.$message.success(that.$t('public.success'))
            })
            .catch(err => that.requestFailed(err))
            .finally(() => {
              that.loading = false
            })
        },
        onCancel() {
          that.loading = false
        }
      })
    },
    // 确认编辑
    handleEdit() {
      this.loading = true
      let obj = {
        man: this.form.man,
        moNoRem: this.form.moNoRem,
        rclBwHour: this.form.rclBwHour,
        rclFs: this.form.rclFs,
        valid: this.form.valid,
        rclLxh: this.form.rclLxh,
        rclNo: this.form.rclNo,
        rclWd: this.form.rclWd,
        createTime: this.form.createTime ? moment(this.form.createTime).format('YYYY-MM-DD') : null
      }
      edit(obj)
        .then(res => {
          this.loading = false
          this.onClose()
          this.$emit('getList')
          this.$message.success(this.$t('public.success'))
        })
        .catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
      // this.$refs.ruleForm.validate(valid => {
      //   if (valid) {
      //     this.loading = true
      //     let obj = {
      //       man: this.form.man,
      //       moNoRem: this.form.moNoRem,
      //       rclBwHour: this.form.rclBwHour,
      //       rclFs: this.form.rclFs,
      //       valid: this.form.valid,
      //       rclLxh: this.form.rclLxh,
      //       rclNo: this.form.rclNo,
      //       rclWd: this.form.rclWd,
      //       createTime: this.form.createTime ? moment(this.form.createTime).format('YYYY-MM-DD') : null,
      //     }
      //     edit(obj).then((res) => {
      //       this.loading = false
      //       this.onClose()
      //       this.$emit('getList')
      //       this.$message.success(this.$t('public.success'))
      //     })
      //   } else {
      //     this.loading = false
      //     this.$message.error(this.$t('public.error'))
      //     return false
      //   }
      // })
    }
  }
}
</script>
