<template>
  <div>
    <!-- 选择人员模态框 -->
    <a-modal :title="title" destroyOnClose width="60%" :visible="visible" :confirmLoading="confirmLoading"
      :footer="null" @cancel="Cancel">
      <div :style="{height:heights}" style="overflow:auto">
        <a-row :gutter="16">
          <a-col :span="6">
            <el-tree :data="treeData" :props="defaultProps" :highlight-current="true"
              @node-click="handleNodeClick"></el-tree>
          </a-col>
          <a-col :span="18">
            <PO_TI v-if="cid==='PO_TI'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <SO_SA v-if="cid==='SO_SA'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <TA_SB v-if="cid==='TA_SB'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <PO_PC v-if="cid==='PO_PC'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />

            <PC_PS v-if="cid==='PC_PS'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <PC_ZG v-if="cid==='PC_ZG'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <ML_ZP v-if="cid==='ML_ZP'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <TI_TG v-if="cid==='TI_TG'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <MF_PS v-if="cid==='MF_PS'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <MF_ZG v-if="cid==='MF_ZG'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <TI_TY v-if="cid==='TI_TY'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <TI_T7 v-if="cid==='TI_T7'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <TI_T6 v-if="cid==='TI_T6'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <YS_XJ v-if="cid==='YS_XJ'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <TB_TG v-if="cid==='TB_TG'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />

            <TB_YS v-if="cid==='TB_YS'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <TB_TI v-if="cid==='TB_TI'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <TGs v-if="cid==='TGs'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <TB_CZ v-if="cid==='TB_CZ'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <QT_TB v-if="cid==='QT_TB'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <BOX_TB v-if="cid==='BOX_TB'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <CPD v-if="cid==='CPD'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <DBD v-if="cid==='DBD'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <DJD v-if="cid==='DJD'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <DS_IC v-if="cid==='DS_IC'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <DS_MC v-if="cid==='DS_MC'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <DZD v-if="cid==='DZD'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <!-- 增加 -->
            <CWSJ v-if="cid==='CWSJ'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <CWDB v-if="cid==='CWDB'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <!-- 增加 -->
            <MO_LD v-if="cid==='MO_LD'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <TZ_LD v-if="cid==='TZ_LD'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <LD_MO v-if="cid==='LD_MO'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <CK_SA v-if="cid==='CK_SA'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <QC_PS v-if="cid==='QC_PS'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />

            <CKs v-if="cid==='CKs_SA'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <ML_MO v-if="cid==='ML_MO'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <ML_TL v-if="cid==='ML_TL'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <ML_TZ v-if="cid==='ML_TZ'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <ML_M2 v-if="cid==='ML_M2'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <ML_TG v-if="cid==='ML_TG'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <TG_M2 v-if="cid==='TG_M2'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <QL_ML v-if="cid==='QL_ML'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <QL_M3 v-if="cid==='QL_M3'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <QL_M2 v-if="cid==='QL_M2'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <QL_M4 v-if="cid==='QL_M4'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <QL_M5 v-if="cid==='QL_M5'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <QL_M6 v-if="cid==='QL_M6'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <SQ_SW v-if="cid==='SQ_SW'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <SQ_WX v-if="cid==='SQ_WX'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <MO_MM v-if="cid==='MO_MM'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <YP_MM v-if="cid==='YP_MM'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <TP_MM v-if="cid==='TP_MM'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <T6_M0 v-if="cid==='T6_M0'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <SC v-if="cid==='SC'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <TZ v-if="cid==='TZ'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <WZ v-if="cid==='WZ'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <ZC v-if="cid==='ZC'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <MV v-if="cid==='MV'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <S0_SY v-if="cid==='S0_SY'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <SN_SI v-if="cid==='SN_SI'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <TZDBG v-if="cid==='TZDBG'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <FC_IM v-if="cid==='FC_IM'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <QM_MM v-if="cid==='QM_MM'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <TY_YB v-if="cid==='TY_YB'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <TY_YK v-if="cid==='TY_YK'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <PC_PB v-if="cid==='PC_PB'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <XX_PB v-if="cid==='XX_PB'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <YX_SB v-if="cid==='YX_SB'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" /> 
            <Q7_TC v-if="cid==='Q7_TC'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <Q7_TC v-if="cid==='TI_PC'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <SH v-if="cid==='SH'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <SRMSH v-if="cid==='SRMSH'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <LY_DY v-if="cid==='LY_DY'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <TM_CX v-if="cid==='TM_CX'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <TM_XG v-if="cid==='TM_XG'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <QC_TM v-if="cid==='QC_TM'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <ZX_ZY v-if="cid==='ZX_ZY'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <CX_ZY v-if="cid==='CX_ZY'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <CC_KC v-if="cid==='CC_KC'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <CW_KC v-if="cid==='CW_KC'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <QM_GZ v-if="cid==='QM_GZ'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <MO_KG v-if="cid==='MO_KG'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <QM_ZP v-if="cid==='QM_ZP'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <PG_WG v-if="cid==='PG_WG'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <SL_YZ v-if="cid==='SL_YZ'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />

            <MJ_WG v-if="cid==='MJ_WG'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
            <MJ_BX v-if="cid==='MJ_BX'" :obj="obj" :cid="cid" @Cancel="Cancel" :row="row" />
          </a-col>
        </a-row>
      </div>
    </a-modal>
  </div>
</template>
<script>

  import {
    PO_TI, SO_SA, TA_SB, PO_PC, PC_PS, PC_ZG, ML_ZP, TI_TG, MF_PS, MF_ZG, TI_TY, TI_T7, TI_T6, TB_TG, TGs, TB_YS,TB_TI, TB_CZ, QT_TB, BOX_TB, CPD, DBD, DJD, DS_MC, DS_IC, DZD, CWSJ, CWDB, MO_LD, TZ_LD, LD_MO, CK_SA, QC_PS, CKs, ML_MO,
    ML_TZ, ML_M2, ML_TG, TG_M2, QL_ML, QL_M3, QL_M2, QL_M4, QL_M5, QL_M6, SQ_SW, SQ_WX, MO_MM, YP_MM, TP_MM, T6_M0, SC, TZ, WZ, ZC, MV, S0_SY, SN_SI, TZDBG, FC_IM, QM_MM, TY_YB,
    TY_YK, PC_PB,XX_PB,YX_SB,Q7_TC,TI_PC, SH, SRMSH,YS_XJ,ML_TL,LY_DY,TM_CX,TM_XG,QC_TM,CW_KC,CC_KC,CX_ZY,ZX_ZY,QM_GZ,MO_KG,QM_ZP,PG_WG,SL_YZ,MJ_BX,MJ_WG
  } from './formList'
  export default {
    name: 'SetList',
    components: {
      PO_TI, SO_SA, TA_SB, PO_PC, PC_PS, PC_ZG, ML_ZP, TI_TG, MF_PS, MF_ZG, TI_TY, TI_T7, TI_T6, TB_TG, TGs, TB_YS,TB_TI, TB_CZ, QT_TB, BOX_TB, CPD, DBD, DJD, DS_MC, DS_IC, DZD, CWSJ, CWDB, MO_LD, TZ_LD, LD_MO, CK_SA, QC_PS, CKs, ML_MO, ML_TZ, ML_M2, ML_TG, TG_M2, QL_ML, QL_M3, QL_M2, QL_M4, QL_M5, QL_M6, SQ_SW, SQ_WX, MO_MM, YP_MM, TP_MM, T6_M0, SC, TZ, WZ, ZC, MV, S0_SY, SN_SI, TZDBG,
      FC_IM, QM_MM, TY_YB, TY_YK, PC_PB,XX_PB,YX_SB,Q7_TC,TI_PC, SH, SRMSH,YS_XJ,ML_TL,LY_DY,TM_CX,TM_XG,QC_TM,CW_KC,CC_KC,CX_ZY,ZX_ZY,QM_GZ,MO_KG,QM_ZP,PG_WG,SL_YZ,MJ_BX,MJ_WG
    },
    data() {
      return {
        title: '',
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this),
        whlist: [],
        userList: [],
        row: {},
        formItemLayout: {
          labelCol: {
            xs: { span: 24 },
            sm: { span: 7 },
            md: { span: 8 },
            lg: { span: 7 }
          },
          wrapperCol: {
            xs: { span: 5 },
            sm: { span: 16 },
            md: { span: 17 },
            lg: { span: 16 }
          }
        },
        treeData: [
          {
            id: 1,
            label: '进货管理',
            children: [
              {
                id: 11,
                label: '送检单',
                children: [
                  {
                    id: 'PO_TI',
                    label: '采购→送检'
                  },
                  {
                    id: 'PO_PC',
                    label: '采购→进货'
                  },
                  {
                    id: 'PC_PS',
                    label: '验收→进货'
                  },
                  {
                    id: 'PC_ZG',
                    label: '验收→暂估'
                  },
                  {
                    id: 'ML_ZP',
                    label: '制成品→送检'
                  },
                  // {
                  //   id: 'TI_TG',
                  //   label: '托工→送检'
                  // },
                  {
                    id: 'MF_PS',
                    label: '特殊检验→进货'
                  },
                  {
                    id: 'MF_ZG',
                    label: '特殊检验→暂估'
                  },
                  {
                    id: "FC_IM",
                    label: "FCIM装箱"
                  }
                ]
              },
              // {
              //   id: 12,
              //   label: '验收单',
              //   children: [
              //     {
              //       id: 'TI_TY',
              //       label: '进货送检→验收'
              //     },
              //     {
              //       id: 'TI_T7',
              //       label: '托工送检→验收'
              //     },
              //     {
              //       id: 'TI_T6',
              //       label: '制成品送检→验收'
              //     }
              //   ]
              // },
              {
                id: 13,
                label: "退回单",
                children: [
                  {
                    id: "TY_YB",
                    label: "进货验收→验收退回"
                  },
                  {
                    id: "TY_YK",
                    label: "托工验收→验收退回"
                  },
                  {
                    id: "PC_PB",
                    label: "进货-进货退回"
                  },
                  {
                    id: "XX_PB",
                    label: "进货退回申请→进货退回"
                  },
                  {
                    id: "YX_SB",
                    label: "销货退回申请→销货退回"
                  },
                  {
                    id: "Q7_TC",
                    label: "托工退回申请->托工退回"
                  },
                  {
                    id: "TI_PC",
                    label: "采购免检->进货"
                  }

                ]
              },
              // {
              //   id: 14,
              //   label: "送货单",
              //   children: [
              //     // {
              //     //   id: "SH",
              //     //   label: "送货→送检"
              //     // },
              //     // {
              //     //   id: "SRMSH",
              //     //   label: "SRM送货→送检"
              //     // },
              //
              //   ]
              // }
            ]
          },
          {
            id: 2,
            label: '托工管理',
            children: [
              {
                id: 'TB_TG',
                label: '托工→托工缴回'
              },
              {
                id: 'TGs',
                label: '批量托工→托工缴回'
              },
              {
                id: 'TB_YS',
                label: '验收→托工缴回'
              },
              {
                id: 'TB_TI',
                label: '免检→托工缴回'
              },
              {
                id: 'TB_CZ',
                label: '完工品→托工缴回'
              },
              {
                id: 'QT_TB',
                label: '特殊检验→托工缴回'
              },
              // {
              //   id: 'BOX_TB',
              //   label: '周转箱→托工缴回'
              // }
            ]
          },
          {
            id: 3,
            label: '仓储管理',
            children: [
              {
                id: "CPD",
                label: "初盘单"
              },
              {
                id: 'DBD',
                label: '调拨单'
              },
              {
                id: 'DS_IC',
                label: '申请→库存调拨'
              },
              // 调拨申请(库存)→调拨单
              // {
              //   id: 'DS_MC',
              //   label: '申请→原料调拨'
              // },
              // 调拨申请→调拨单
              {
                id: "DZD",
                label: "入库(调增单)"
              },
              // 增加
              {
                id: "CWSJ",
                label: "储位上架"
              },
              {
                id: "CWDB",
                label: "储位下架"
              },
              // 增加
              {
                id: "DJD",
                label: "出库(调减单)"
              },
              {
                id: 'ZX_ZY',
                label: '装箱作业'
              },
              {
                id: 'CX_ZY',
                label: '拆箱作业'
              },
              {
                id: 'CC_KC',
                label: '仓储库存查询'
              },
              {
                id: 'CW_KC',
                label: '储位库存查询'
              },
            ]
          },
          {
            id: 4,
            label: '模具管理',
            children: [
              {
                id: 41,
                label: '模具领用',
                children: [
                  {
                    id: 'MO_LD',
                    label: '制令→模具领用'
                  },
                  {
                    id: 'TZ_LD',
                    label: '通知→模具领用'
                  }
                ]
              },
              {
                id: 'LD_MO',
                label: '模具归还'
              },
              {
                id: 'MJ_BX',
                label: '模具报修单'
              },
              {
                id: 'MJ_WG',
                label: '模具完工单'
              }
            ]
          },
          {
            id: 5,
            label: '销货管理',
            children: [
              {
                id: 'CK_SA',
                label: '出库→销货'
              },
              {
                id: 'CKs_SA',
                label: '批量出库→销货'
              },
              {
                id: 'QC_PS',
                label: '特殊检验→销货'
              },
              {
                id: 'SO_SA',
                label: '受订→销货'
              },
              {
                id: 'TA_SB',
                label: '销货验收-销货退回'
              },
            ]
          },
          {
            id: 6,
            label: '生产管理',
            children: [
              // {
                // id: 63,
                // label: '领料单',
                // children: [
                  // {
                  //   id: 'ML_MO',
                  //   label: '制令→领料'
                  // },
                  // {
                  //   id: 'ML_TZ',
                  //   label: '通知单→领料'
                  // },
                  // {
                  //   id: 'ML_M2',
                  //   label: '通知单→退料'
                  // },
                  // {
                  //   id: 'ML_TG',
                  //   label: '托工→领料'
                  // },
                  // {
                  //   id: 'TG_M2',
                  //   label: '托工→退料'
                  // },
                  // {
                  //   id: 'QL_ML',
                  //   label: '申请→领料'
                  // },
                  // {
                  //   id: 'QL_M3',
                  //   label: '申请→补料'
                  // },
                  // {
                  //   id: 'QL_M2',
                  //   label: '申请→退料'
                  // },
                  // {
                  //   id: 'QL_M4',
                  //   label: '申请→托工领料'
                  // },
                  // {
                  //   id: 'QL_M5',
                  //   label: '申请→托工退料'
                  // },
                  // {
                  //   id: 'QL_M6',
                  //   label: '申请→托工补料'
                  // }
                // ]
              // },
              // {
              //   id: 64,
              //   label: '非生产领料单',
              //   children: [
              //     {
              //       id: 'SQ_SW',
              //       label: '非生产申请→领料'
              //     },
              //     {
              //       id: "SQ_WX",
              //       label: "非生产申请→退料"
              //     }
              //   ]
              // },
              {
                id: 66,
                label: '缴库单',
                children: [
                  {
                    id: 'MO_MM',
                    label: '制令→缴库'
                  },
                  {
                    id: 'YP_MM',
                    label: '制令样品→缴库'
                  },
                  {
                    id: 'TP_MM',
                    label: '验收→缴库'
                  },
                  // {
                  //   id: 'T6_M0',
                  //   label: '免检→缴库'
                  // },
                  {
                    id: "QM_MM",
                    label: "特殊成品检验→缴库"
                  },
                  {
                    id: 'T6_M0',
                    label: '制成品免检→缴库'
                  },
                ]
              },
              {
                id: 67,
                label: '报工管理',
                children: [
                  {
                    id: 'SC',
                    label: '制成品+制程报工'
                  },
                  {
                    id: 'TZ',
                    label: '通知单报工'
                  },
                  {
                    id: 'WZ',
                    label: '完工品报工'
                  },
                  {
                    id: 'ZC',
                    label: '制令+制程报工'
                  },
                  {
                    id: 'MV',
                    label: '制程转移单'
                  }
                ]
              },
              // {
              //   id: 68,
              //   label: "工序派工管理",
              //   children: [
              //     {
              //       id: 'GX',
              //       label: '工序派工'
              //     },
              //     {
              //       id: 'PG',
              //       label: '派工完工'
              //     },
              //   ]
              // },
              // {
              //   id: 69,
              //   label: "通知单报工管理",
              //   children: [
              //     {
              //       id: 'ZY',
              //       label: '分配作业人员'
              //     },
              //     {
              //       id: 'TZDBG',
              //       label: '通知单报工'
              //     },
              //     {
              //       id: 'TBC',
              //       label: '通知单报参数'
              //     }
              //   ]
              // },
              // {
              //   id: 68,
              //   label: "自动匹配来源单",
              //   children: [
              //     {
              //       id: 681,
              //       label: "免检转缴库"
              //     },
              //     {
              //       id: 682,
              //       label: "制令转缴库"
              //     }
              //   ]
              // }
              {
                id: "QM_GZ",
                label: "工作组"
              },
              {
                id: "MO_KG",
                label: "制令开工"
              },
              {
                id: "QM_ZP",
                label: "装配单"
              },
              {
                id: "PG_WG",
                label: "派工完工(PDA)"
              },
              {
                id: "SL_YZ",
                label: "上料验证"
              },
            ]
          },
          {
            id: 7,
            label: '品检管理',
            children: [
              {
                id: 11,
                label: '验收单',
                children: [
                  {
                    id: 'TI_TY',
                    label: '进货送检→验收'
                  },
                  {
                    id: 'TI_T7',
                    label: '托工送检→验收'
                  },
                  {
                    id: 'TI_T6',
                    label: '制成品送检→验收'
                  },
                  {
                    id: 'YS_XJ',
                    label: '巡检'
                  }
                ]
              },
              {
                id: 12,
                label: '送检单',
                children: [
                  {
                    id: 'TI_TG',
                    label: '托工→送检'
                  },
                  {
                    id: "SH",
                    label: "送货→送检"
                  },
                  {
                    id: "SRMSH",
                    label: "SRM送货→送检"
                  },
                ]
              }
            ]
          },
          {
            id: 8,
            label: '领料管理',
            children: [
              {
                id: 'ML_TZ',
                label: '通知单→领料'
              },
              {
                id: 'ML_M2',
                label: '通知单→退料'
              },
              {
                id: 'ML_MO',
                label: '制令→领料'
              },
              {
                id: 'ML_TL',
                label: '制令→退料'
              },
              {
                id: 'ML_TG',
                label: '托工→领料'
              },
              {
                id: 'TG_M2',
                label: '托工→退料'
              },
              {
                id: 'QL_ML',
                label: '申请→领料'
              },
              {
                id: 'QL_M3',
                label: '申请→补料'
              },
              {
                id: 'QL_M2',
                label: '申请→退料'
              },
              {
                id: 'QL_M4',
                label: '申请→托工领料'
              },
              {
                id: 'QL_M5',
                label: '申请→托工退料'
              },
              {
                id: 'QL_M6',
                label: '申请→托工补料'
              },
              {
                id: 'SQ_SW',
                label: '非生产申请→领料'
              },
              {
                id: "SQ_WX",
                label: "非生产申请→退料"
              },
              {
                id: 'DS_MC',
                label: '申请→原料调拨'
              },
            ]
          },
          {
            id: 9,
            label: '条码管理',
            children: [
              {
                id: 'QC_TM',
                label: '期初贴码'
              },
              {
                id: 'TM_XG',
                label: '条码修改'
              },
              {
                id: 'TM_CX',
                label: '条码查询'
              },
              {
                id: 'LY_DY',
                label: '蓝牙打印'
              }
            ]
          },
          {
            id: 10,
            label: '设备管理',
            children: [
              {
                id: 'SN_SI',
                label: '设备报修单'
              }
              , {
                id: "S0_SY",
                label: "设备完工单"
              }
            ]
          }
        ],
        // 传递值
        cid: '',
        obj: {
          subname: '',
          // name字段
          fidArr: ['DbdAllocateDeptName', 'DbdAllocateWhName', 'DbdApplyDeptName', 'DbdIncomingDeptName', 'DbdIncomingUserName', 'DbdIncomingWhName',
            'DsmcInStorage', 'SIdeptName', 'SNdeptName', 'abnormal', 'abnormalWh', 'appointWh', 'batch', 'qtyTyep', 'examine', 'cwkz', 'exceed', 'exceedProportion', 'force', 'inputQty', 'tfTiz', 'chuwChk',
            'installedWh', 'mvZcNo', 'zcNo', 'scanning', 'separator', 'source', 'apart', 'srmPrnNo', 'qcFlag', 'refreshMo', 'trunc', 'storage', 'saveT5', 'controlyDay', 'fcim', 'lstInd', 'prdInd', 'box', 'dz', 'completionRecord', 'scanBatNo', 'popup', 'remark', 'change',
            'currencies', 'digit', 'automaticWh', 'showDetails', 'fcimTime', 'moSl', 'mj1', 'mjly', 'mj3', 'batOrder', 'prdtCode', 'prdtPk', 'isSplit', 'add', 'warehousing', 'repeat', 'qtyOrqty1',
            'qty1Orqty', 'ygNo', 'picking', 'batchRequisite', 'qtyPrc', 'amount', 'moLy', 'moOrder', 'moLybg', 'moOrderbg', 'presentDept', 'workOrder', 'group', 'confirmQty', 'batchInventory', 'automaticDbd', 'SelectInWh', 'sourceType',
            'automaticTw', 'automaticPc', 'automaticList', 'ckWh', 'selectData', 'continuousScanning', 'qycw', 'ysbs', 'stime', 'amtChk', 'vprd', 'chkPrdt1Cst', 'limit', 'dep', 'ckqty', 'ckDep', 'oneTbilNo', 'location', 'bigDate', 'smallDate', 'bigNum', 'smallNum', 'ignoreUp', 'ckRemainder', 'sourceDep', 'productRange', 'ckChk', 'productPicking',
            'bilNoMatching', 'boxExistence', 'prdtNotFormula', 'multireceive','poId']
          // 添加 条码验收启用储位 qycw ysbs xiugai
          // ysbs
          //    const fidArr = ['exceed', 'exceedProportion', 'source', 'inputQty', 'dz', 'add', 'scanBatNo', 'separator', 'force', 'storage',
          //   'examine', 'installedWh', 'appointWh', 'trunc', 'batOrder', 'batch', 'scanning', 'abnormal', 'abnormalWh', 'DbdAllocateWhName',
          //   'DbdIncomingWhName', 'DbdAllocateDeptName', 'DbdIncomingDeptName', 'DbdApplyDeptName', 'DbdIncomingUserName', 'mvZcNo', 'DsmcInStorage',
          //   'SIdeptName', 'SNdeptName'
          // ]
        },
        defaultProps: {
          children: 'children',
          label: 'label'
        }
      }
    },
    computed: {
      // 滚动区高度
      // (业务需求：屏幕高度减去头部标题和底部tabbar的高度，当然这2个高度也是可以动态获取的)
      heights: function () {
        return (window.innerHeight - 250) + 'px';
      }
    },
    methods: {
      handleChange() {
      },
      open(model, row) {
        this.row = row
        this.visible = true
        this.title = model.title
      },
      handleNodeClick(e) {
        if (isNaN(e.id)) {

          switch (e.id) {
            case 'TY_YK':
              this.cid = 'TY_YK'
              this.obj.subname = e.label
              break
            case 'PC_PB':
              this.cid = 'PC_PB'
              this.obj.subname = e.label
              break
            case 'XX_PB':
              this.cid = 'XX_PB'
              this.obj.subname = e.label
              break
            case 'YX_SB':
              this.cid = 'YX_SB'
              this.obj.subname = e.label
              break  
            case 'TI_PC':
              this.cid = 'TI_PC'
              this.obj.subname = e.label
              break   
            case 'Q7_TC':
              this.cid = 'Q7_TC'
              this.obj.subname = e.label
              break  
            case 'SH':
              this.cid = 'SH'
              this.obj.subname = e.label
              break
            case 'SRMSH':
              this.cid = 'SRMSH'
              this.obj.subname = e.label
              break
            case 'TY_YB':
              this.cid = 'TY_YB'
              this.obj.subname = e.label
              break
            case 'QM_MM':
              this.cid = 'QM_MM'
              this.obj.subname = e.label
              break
            case 'FC_IM':
              this.cid = 'FC_IM'
              this.obj.subname = e.label
              break
            case 'PO_TI':
              this.cid = 'PO_TI'
              this.obj.subname = e.label
              break
            case 'SO_SA':
              this.cid = 'SO_SA'
              this.obj.subname = e.label
              break
            case 'TA_SB':
              this.cid = 'TA_SB'
              this.obj.subname = e.label
              break
            case 'PO_PC':
              this.cid = 'PO_PC'
              this.obj.subname = e.label
              break
            case 'PC_PS':
              this.cid = 'PC_PS'
              this.obj.subname = e.label
              break
            case 'PC_ZG':
              this.cid = 'PC_ZG'
              this.obj.subname = e.label
              break
            case 'ML_ZP':
              this.cid = 'ML_ZP'
              this.obj.subname = e.label
              break
            case 'TI_TG':
              this.cid = 'TI_TG'
              this.obj.subname = e.label
              break
            case 'MF_PS':
              this.cid = 'MF_PS'
              this.obj.subname = e.label
              break
            case 'MF_ZG':
              this.cid = 'MF_ZG'
              this.obj.subname = e.label
              break
            case 'TI_TY':
              this.cid = 'TI_TY'
              this.obj.subname = e.label
              break
            case 'TI_T7':
              this.cid = 'TI_T7'
              this.obj.subname = e.label
              break
            case 'TI_T6':
              this.cid = 'TI_T6'
              this.obj.subname = e.label
              break
            case 'YS_XJ':
              this.cid = 'YS_XJ'
              this.obj.subname = e.label
              break
            case 'TB_TG':
              this.cid = 'TB_TG'
              this.obj.subname = e.label
              break
            case 'TGs':
              this.cid = 'TGs'
              this.obj.subname = e.label
              break
            case 'TB_YS':
              this.cid = 'TB_YS'
              this.obj.subname = e.label
              break
            case 'TB_TI':
              this.cid = 'TB_TI'
              this.obj.subname = e.label
              break 
            case 'TB_CZ':
              this.cid = 'TB_CZ'
              this.obj.subname = e.label
              break
            case 'QT_TB':
              this.cid = 'QT_TB'
              this.obj.subname = e.label
              break
            case 'BOX_TB':
              this.cid = 'BOX_TB'
              this.obj.subname = e.label
              break
            case 'CPD':
              this.cid = 'CPD'
              this.obj.subname = e.label
              break
            case 'DBD':
              this.cid = 'DBD'
              this.obj.subname = e.label
              break
            case 'DJD':
              this.cid = 'DJD'
              this.obj.subname = e.label
              break
            case 'DS_IC':
              this.cid = 'DS_IC'
              this.obj.subname = e.label
              break
            case 'DS_MC':
              this.cid = 'DS_MC'
              this.obj.subname = e.label
              break
            case 'DZD':
              this.cid = 'DZD'
              this.obj.subname = e.label
              break
            // 增加
            case 'CWSJ':
              this.cid = 'CWSJ'
              this.obj.subname = e.label
              break
            case 'CWDB':
              this.cid = 'CWDB'
              this.obj.subname = e.label
              break
            // 增加
            case 'MO_LD':
              this.cid = 'MO_LD'
              this.obj.subname = e.label
              break
            case 'TZ_LD':
              this.cid = 'TZ_LD'
              this.obj.subname = e.label
              break
            case 'LD_MO':
              this.cid = 'LD_MO'
              this.obj.subname = e.label
              break
            case 'CK_SA':
              this.cid = 'CK_SA'
              this.obj.subname = e.label
              break
            case 'QC_PS':
              this.cid = 'QC_PS'
              this.obj.subname = e.label
              break

            case 'CKs_SA':
              this.cid = 'CKs_SA'
              this.obj.subname = e.label
              break
            case 'ML_MO':
              this.cid = 'ML_MO'
              this.obj.subname = e.label
              break
            case 'ML_TL':
              this.cid = 'ML_TL'
              this.obj.subname = e.label
              break
            case 'ML_TZ':
              this.cid = 'ML_TZ'
              this.obj.subname = e.label
              break
            case 'ML_M2':
              this.cid = 'ML_M2'
              this.obj.subname = e.label
              break
            case 'ML_TG':
              this.cid = 'ML_TG'
              this.obj.subname = e.label
              break
            case 'TG_M2':
              this.cid = 'TG_M2'
              this.obj.subname = e.label
              break
            case 'QL_ML':
              this.cid = 'QL_ML'
              this.obj.subname = e.label
              break
            case 'QL_M3':
              this.cid = 'QL_M3'
              this.obj.subname = e.label
              break
            case 'QL_M2':
              this.cid = 'QL_M2'
              this.obj.subname = e.label
              break
            case 'QL_M4':
              this.cid = 'QL_M4'
              this.obj.subname = e.label
              break
            case 'QL_M5':
              this.cid = 'QL_M5'
              this.obj.subname = e.label
              break
            case 'QL_M6':
              this.cid = 'QL_M6'
              this.obj.subname = e.label
              break
            case 'SQ_WX':
              this.cid = 'SQ_WX'
              this.obj.subname = e.label
              break
            case 'SQ_SW':
              this.cid = 'SQ_SW'
              this.obj.subname = e.label
              break
            case 'MO_MM':
              this.cid = 'MO_MM'
              this.obj.subname = e.label
              break
            case 'YP_MM':
              this.cid = 'YP_MM'
              this.obj.subname = e.label
              break
            case 'TP_MM':
              this.cid = 'TP_MM'
              this.obj.subname = e.label
              break
            case 'T6_M0':
              this.cid = 'T6_M0'
              this.obj.subname = e.label
              break
            case 'SC':
              this.cid = 'SC'
              this.obj.subname = e.label
              break
            case 'TZ':
              this.cid = 'TZ'
              this.obj.subname = e.label
              break
            case 'WZ':
              this.cid = 'WZ'
              this.obj.subname = e.label
              break
            case 'ZC':
              this.cid = 'ZC'
              this.obj.subname = e.label
              break
            case 'MV':
              this.cid = 'MV'
              this.obj.subname = e.label
              break
            case 'S0_SY':
              this.cid = 'S0_SY'
              this.obj.subname = e.label
              break
            case 'SN_SI':
              this.cid = 'SN_SI'
              this.obj.subname = e.label
              break
            case 'TZDBG':
              this.cid = 'TZDBG'
              this.obj.subname = e.label
              break
            case 'LY_DY':
              this.cid = 'LY_DY'
              this.obj.subname = e.label
              break
            case 'TM_CX':
              this.cid = 'TM_CX'
              this.obj.subname = e.label
              break
            case 'TM_XG':
              this.cid = 'TM_XG'
              this.obj.subname = e.label
              break
            case 'QC_TM':
              this.cid = 'QC_TM'
              this.obj.subname = e.label
              break
            case 'CW_KC':
              this.cid = 'CW_KC'
              this.obj.subname = e.label
              break
            case 'CC_KC':
              this.cid = 'CC_KC'
              this.obj.subname = e.label
              break
            case 'CX_ZY':
              this.cid = 'CX_ZY'
              this.obj.subname = e.label
              break
            case 'ZX_ZY':
              this.cid = 'ZX_ZY'
              this.obj.subname = e.label
              break
            case 'QM_GZ':
              this.cid = 'QM_GZ'
              this.obj.subname = e.label
              break
            case 'MO_KG':
              this.cid = 'MO_KG'
              this.obj.subname = e.label
              break
            case 'QM_ZP':
              this.cid = 'QM_ZP'
              this.obj.subname = e.label
              break
            case 'PG_WG':
              this.cid = 'PG_WG'
              this.obj.subname = e.label
              break
            case 'SL_YZ':
              this.cid = 'SL_YZ'
              this.obj.subname = e.label
              break
            case 'MJ_WG':
              this.cid = 'MJ_WG'
              this.obj.subname = e.label
              break
            case 'MJ_BX':
              this.cid = 'MJ_BX'
              this.obj.subname = e.label
              break
          }
        }
      },
      Cancel() {
        this.visible = false
        this.cid = ''
      }
    }
  }
</script>