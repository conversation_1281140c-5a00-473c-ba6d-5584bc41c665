<template>
  <div>
    <a-modal :title="title" destroyOnClose width="40%" :visible.sync="visible" :confirmLoading="confirmLoading"
      @cancel="handleCancel">
      <a-row :gutter="16">
        <a-col>
          <a-form-item :label="$t('propertySettings.setPP')">
            <div>
              <template v-for="(tag, index) in tags">
                <a-tooltip v-if="tag.length > 10" :key="tag" :title="tag">
                  <a-tag :key="tag" :closable="index !== -1" @close="() => handleClose(tag)">
                    {{ `${tag.slice(0, 10)}...` }}
                  </a-tag>
                </a-tooltip>
                <a-tag v-else :key="tag" :closable="index !== -1" @close="() => handleClose(tag)">
                  {{ tag }}
                </a-tag>
              </template>
            </div>
          </a-form-item>
        </a-col>
      </a-row>
      <template slot="footer">
        <a-button key="choose" @click="choose">
          <!-- {{ $t('propertySettings.setPP') }}  -->
          选择人员
        </a-button>
        <a-button key="cancel" @click="handleCancel">{{ $t('public.cancel') }} </a-button>
        <a-button key="forward" :loading="loading" type="primary" @click="onOk()">{{ $t('public.sure') }}</a-button>
      </template>
      <!-- 选择人员模态框 -->
      <set-List ref="setList" @getTags="getTags" :newarr="newarr" />
    </a-modal>
  </div>
</template>
<script>
  import { chkUsers, addPswdRole, getUsers } from '@/api/barcode/propertySettings'
  import setList from './setList'
  import qs from 'qs'
  export default {
    name: 'SetMenu',
    components: {
      setList
    },
    data() {
      return {
        tags: [],
        tableData: [],
        title: '',
        newarr: [],
        row: {},
        loading: false,
        visible: false,
        visible1: false,
        confirmLoading: false,
        treeData: [],
        form: this.$form.createForm(this),
        tablePage: {
          currentPage: 1,
          pageSize: 10,
          total: 0
        }
      }
    },
    created() {
    },
    methods: {
      getUsers(row) {
        let newObj = JSON.parse(JSON.stringify(row))
        newObj.users = qs.parse(newObj.users)
        delete newObj.dutOtD
        //人员设置查询接口
        getUsers(newObj).then(res => {
          this.tags = res.data.uNames
          const arr = this.tags
          this.newarr = arr.map(i => { return i.split(':')[0] })
        }).catch(err => this.requestFailed(err))
      },
      // 获取人员列表
      getTags(arr) {
        if (arr.length > 0) {
          let set = new Set([...this.tags, ...arr])
          this.tags = [...set]
        }
      },
      handleClose(removedTag) {
        const tags = this.tags.filter(tag => tag !== removedTag)
        this.tags = tags
      },
      open(model, row) {
        this.row = row
        this.getUsers(row)
        this.visible = true
        this.title = model.title
      },
      // 已选择人员按钮
      choose() {
        this.$refs.setList.open(this.row)
      },
      // 保存
      onOk() {
        this.row.users = this.tags
        chkUsers(
          Object.assign(
            this.row
          )).then(response => {
            if (response.data === '') {
              this.addOk()
              // 没有重复
            } else {
              this.visible = true
              this.$message.error('已选择重复！')
            }
          }).catch(err => this.requestFailed(err))
      },
      addOk() {
        addPswdRole(
          Object.assign(
            this.row
          )).then(response => {
            this.$store.commit("clear_PROPER");
            this.visible = false
            this.$message.success(this.$t('public.success'))
          }).catch(err => this.requestFailed(err))
      },
      handleCancel() {
        this.tags = []
        this.visible = false
      }

    }
  }
</script>