<template>
	<div>
		<!-- 新增弹窗 -->
<!--		<el-drawer :title="$t(title)" :visible.sync="drawer" size="40%" ref="drawerRef" class="JustMake-drawer">-->
		<a-drawer :title="$t(title)" destroyOnClose :visible.sync="drawer" width="40%" @close="handleCancel"  ref="drawerRef" class="JustMake-a-drawer" >
      <div class="drawer-content">
        <el-form ref="drawerFormRef" :model="formData" label-position="right" label-width="100px" class="drawer-form">
          <el-row>
            <el-col v-for="(item, index) in formDataRaw" :key="index" :span="item.span || 12">
              <el-form-item :label="$t(item.field)" :title="$t(item.field)" :prop="getPropName(item.field)"
                :rules="item.rules">
                <Forms :data="item" v-model="formData[getPropName(item.field)]" @selectListEvent="handleSelectListEvent"></Forms>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="drawer-footer">
        <el-dropdown v-show="status!=='0'">
          <el-button style='margin-right: 10px'>
            {{ $t('public.action') }}
            <i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item @click.native="set_salm()">
              {{ $t('propertySettings.set') }}
            </el-dropdown-item>
            <el-dropdown-item @click.native="attribute()">
              {{ $t('propertySettings.attribute') }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-button @click="handleCancel">{{ $t('public.cancel') }}</el-button>
        <el-button type="primary" @click="handleCreate" :loading="drawerLoading">{{ drawerLoading ? 'Loading ...' :
          $t('public.sure')
          }}</el-button>
      </div>
    </a-drawer>
    <set-modal ref="set" />
    <propertySet ref="pro" />
	</div>
</template>

<script>
import Forms from '@/components/Forms/index.vue'
import { addObj, putObj, delObj, getListAll } from '@/api/barcode/propertySettings'
import setModal from './setModal'
import propertySet from './propertySet'

export default {
	components: {
		Forms,setModal, propertySet
	},
	data() {
		return {
			api_save: addObj,
			drawer: false,
			drawerLoading: false,
			title: 'public.add',
      status: '0', // 0 新增 1 编辑
			formData: {},
			currentRow: {},
			formDataRaw: [
				{
					field: 'propertySettings.compno', type: 'input', disabled: true,
					rules: [{ required: false, message: this.$t('propertySettings.placeholder.compno') }]
				},
				{
					field: 'propertySettings.roleno', type: 'input',
					rules: [{ required: true, message: this.$t('propertySettings.placeholder.roleno') }]
				},
				{
					field: 'propertySettings.name', type: 'input',
					rules: [{ required: true, message: this.$t('propertySettings.placeholder.name') }]
				},
				{
					field: 'propertySettings.dep', type: 'selectList',
					props: {
						url: "/admin/dept/page",
						tableColumn: this.$Column.fmDep,
						form: this.$Form.fmDep,
					},
					rules: [{ required: true, message: this.$t('salm.placeholder.depName') }]
				},
				{
					field: 'propertySettings.publicId', type: 'radio',
          values:[
           { label: 'public.T', value: 'T'},
           { label: 'public.F', value: 'F'},
          ],
					rules: [{ required: true, message: this.$t('propertySettings.placeholder.publicId') }]
				},
				{
					field: 'propertySettings.rem', type: 'input', 
					rules: [{ required: true, message: this.$t('propertySettings.placeholder.rem') }]
				},
			],
		}
	},
	watch: {
    status: function (newVal) {
      if (newVal === '0') {
        this.title = 'public.add'
        this.api_save = addObj
      } else {
        this.title = 'public.edit'
        this.api_save = putObj
      }
    }
  },
	methods: {
		handleVisible() {
			this.status = '0'
      this.formData = {}
			this.$nextTick(() => {
        this.$refs.drawerFormRef.clearValidate()
      })
      this.drawer = !this.drawer
		},
		getPropName(field) {
			return field.includes('.') ? field.split('.').pop() : field;
		},
    handleSelectListEvent(param){
      switch (param.field){
        case "propertySettings.dep":
          this.formData['dep'] = param.obj.data.deptCode
      }
    },
		handleCancel() {
			this.formData = {}
			this.$nextTick(() => {
				this.$refs.drawerFormRef.clearValidate()
			})
			this.drawer = false
		},
		async handleCreate() {
			if (this.drawerLoading) {
				return;
			}
			if (!this.api_save || typeof this.api_save !== 'function') {
				return this.$message.error('请配置 api_save 参数');
			}
			// 表单校验
			try {
				await this.$refs.drawerFormRef.validate();
			} catch (error) {
				return;
			}
			let result = null;
			try {
				this.drawerLoading = true;
				result = await this.api_save(this.formData)
				if (result.code == 0) {
					this.$message.success(this.$t('public.success'))
					this.$emit('refresh')
					this.drawer = false;
				}
			} catch (err) {
				console.error(err)
				this.$message.error(err || this.$t('public.error'));
			} finally {
				this.drawerLoading = false;
				// this.$emit('toolbarClick', { code: 'create', result });
			}
		},
		handleEdit(row) {
      this.currentRow = row
      this.status = '1'
      this.drawer = true
      this.$nextTick(() => {
        this.formData = Object.assign({}, row) // 拷贝数据
      })
    },
    set_salm () {
      const row = this.currentRow
      this.$refs.set.open({ title: this.$t('propertySettings.set') }, row)
    },
    // 设置属性
    attribute () {
      const row = this.currentRow
      this.$refs.pro.open({ title: this.$t('propertySettings.attribute') }, row)
    },
	},
}
</script>
