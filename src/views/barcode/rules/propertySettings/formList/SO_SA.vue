<template>
  <div>
    <a-form
      :form="form"
      ref="form"
    >
      <span style="font-size:1rem">{{ $t('propertySettings.ware') }}</span>
      <a-row>
        <a-col
          :xs="24"
          :sm="24"
          :md="12"
          :lg="12"
          :xl="12"
        >
          <a-form-item
            :label="$t('propertySettings.ware')"
            v-bind="formItemLayout"
          >
            <a-select
              style="width:100%"
              allowClear
              v-decorator="['installedWh', { rules: [{ message:$t('propertySettings.ware') }] }]"
              :getPopupContainer=" triggerNode => {
                return triggerNode.parentNode || document.body;
              }"
            >
              <a-select-option value="1">来源单仓库</a-select-option>
              <a-select-option value="2">扫描仓库</a-select-option>
              <a-select-option value="3">条码仓库</a-select-option>
              <a-select-option value="4">预设仓库</a-select-option>
              <a-select-option value="5">指定仓库</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col
          :xs="24"
          :sm="24"
          :md="12"
          :lg="12"
          :xl="12"
        >
          <a-form-item
            :label="$t('propertySettings.waret')"
            v-bind="formItemLayout"
          >
            <a-select
              show-search
              allowClear
              @search="searchWh"
              @focus="getWhs()"
              :filter-option="false"
              v-decorator="['appointWh',{rules: []}]"
            >
              <a-spin
                v-if="fetching"
                slot="notFoundContent"
                size="small"
              />
              <a-select-option
                v-for="(i, index) in whlist"
                :key="index"
                :value="i.wh"
              >{{ i.name }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">{{ $t('propertySettings.sheet') }}:</span>
      <a-row>
        <a-col
          :xs="24"
          :sm="24"
          :md="12"
          :lg="12"
          :xl="12"
        >
          <a-form-item
            :label="$t('propertySettings.Separator')"
            v-bind="formItemLayout"
          >
            <a-input v-decorator="['separator', { rules: [{ message:$t('propertySetting.Separator') }] }]"></a-input>
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">{{ $t('propertySettings.Quantity') }}:</span>
      <a-row>
        <a-col
          :xs="24"
          :sm="24"
          :md="12"
          :lg="12"
          :xl="12"
        >
          <a-form-item
            :label="$t('propertySettings.Quantitys')"
            v-bind="formItemLayout"
          >
            <a-switch v-model="inputQty" />
          </a-form-item>
        </a-col>
        <a-col
        :xs="24"
        :sm="24"
        :md="12"
        :lg="12"
        :xl="12"
      >
        <a-form-item
          label="是否可拆码"
          v-bind="formItemLayout"
        >
          <a-switch v-model="boxExistence" />
        </a-form-item>
      </a-col>
      </a-row>
      <span style="font-size:1rem">扫描设置:</span>
      <a-row>
        <a-col
          :xs="24"
          :sm="24"
          :md="12"
          :lg="12"
          :xl="12"
        >
          <a-form-item
            label="批号匹配"
            v-bind="formItemLayout"
          >
            <a-switch v-model="scanBatNo" />
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">{{ $t('propertySettings.detection') }}:</span>
      <a-row>
        <a-col
          :xs="24"
          :sm="24"
          :md="12"
          :lg="12"
          :xl="12"
        >
          <a-form-item
            :label="$t('propertySettings.detection')"
            v-bind="formItemLayout"
          >
            <a-select
              style="width:100%"
              allowClear
              v-decorator="['exceed', { rules: [{ message:$t('propertySettings.detection') }] }]"
              :getPopupContainer=" triggerNode => {
                return triggerNode.parentNode || document.body;
              }"
            >
              <a-select-option value="1">管制</a-select-option>
              <a-select-option value="2">提示</a-select-option>
              <a-select-option value="3">不管制</a-select-option>
              <a-select-option value="4">允许在超交比例内</a-select-option>
              <a-select-option value="5">自动扣除多余数量</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col
          :xs="24"
          :sm="24"
          :md="12"
          :lg="12"
          :xl="12"
        >
          <a-form-item
            :label="$t('propertySettings.Proportion')"
            v-bind="formItemLayout"
          >
            <a-input-number
              :min="0"
              v-decorator="['exceedProportion']"
            />
          </a-form-item>
        </a-col>
        <a-col
          :xs="24"
          :sm="24"
          :md="12"
          :lg="12"
          :xl="12"
        >
          <a-form-item
            label="拆分弹出"
            v-bind="formItemLayout"
          >
            <a-switch v-model="apart" />
          </a-form-item>
        </a-col>
        <a-col
          :xs="24"
          :sm="24"
          :md="12"
          :lg="12"
          :xl="12"
        >
          <a-form-item
            :label="$t('propertySettings.basic')"
            v-bind="formItemLayout"
          >
            <a-switch v-model="source" />
          </a-form-item>
        </a-col>
      </a-row>

    <span style="font-size:1rem">入库日大于来源单预交日:</span>
      <a-row>
        <a-col
          :xs="24"
          :sm="24"
          :md="12"
          :lg="12"
          :xl="12"
        >
          <a-form-item
            label="入库日大于来源单预交日"
            v-bind="formItemLayout"
          >
            <a-select
              style="width:100%"
              allowClear
              v-decorator="['bigDate', { rules: [{ message:$t('propertySettings.Batch') }] }]"
              :getPopupContainer=" triggerNode => {
                return triggerNode.parentNode || document.body;
              }"
            >
              <a-select-option value="1">不限制</a-select-option>
              <a-select-option value="2">不允许</a-select-option>
              <a-select-option value="3">允许在超交天数内送检</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col
          :xs="24"
          :sm="24"
          :md="12"
          :lg="12"
          :xl="12"
        >
          <a-form-item
            label="超交天数设定"
            v-bind="formItemLayout"
          >
            <a-input-number
              :min="1"
              v-decorator="['bigNum']"
            />
          </a-form-item>
        </a-col>
      </a-row>

    <span style="font-size:1rem">入库日小于来源单预交日:</span>
      <a-row>
        <a-col
          :xs="24"
          :sm="24"
          :md="12"
          :lg="12"
          :xl="12"
        >
          <a-form-item
            label="入库日小于来源单预交日"
            v-bind="formItemLayout"
          >
            <a-select
              style="width:100%"
              allowClear
              v-decorator="['smallDate', { rules: [{ message:$t('propertySettings.Batch') }] }]"
              :getPopupContainer=" triggerNode => {
                return triggerNode.parentNode || document.body;
              }"
            >
              <a-select-option value="1">不限制</a-select-option>
              <a-select-option value="2">不允许</a-select-option>
              <a-select-option value="3">允许在超交天数内送检</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col
          :xs="24"
          :sm="24"
          :md="12"
          :lg="12"
          :xl="12"
        >
          <a-form-item
            label="提前天数设定"
            v-bind="formItemLayout"
          >
            <a-input-number
              :min="1"
              v-decorator="['smallNum']"
            />
          </a-form-item>
        </a-col>
      </a-row>


      <span style="font-size:1rem">{{ $t('propertySettings.Batch') }}:</span>
      <a-row>
        <a-col
          :xs="24"
          :sm="24"
          :md="12"
          :lg="12"
          :xl="12"
        >
          <a-form-item
            :label="$t('propertySettings.serialNumber')"
            v-bind="formItemLayout"
          >
            <a-select
              style="width:100%"
              allowClear
              v-decorator="['batch', { rules: [{ message:$t('propertySettings.Batch') }] }]"
              :getPopupContainer=" triggerNode => {
                return triggerNode.parentNode || document.body;
              }"
            >
              <a-select-option value="1">按货品基础资料</a-select-option>
              <a-select-option value="2">不管制</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">{{ $t('propertySettings.Bill') }}:</span>
      <a-row>
        <a-col
          :xs="24"
          :sm="24"
          :md="12"
          :lg="12"
          :xl="12"
        >
          <a-form-item
            :label="$t('propertySettings.Bill')"
            v-bind="formItemLayout"
          >
            <a-select
              style="width:100%"
              allowClear
              v-decorator="['examine', { rules: [{ message:$t('propertySettings.Bill') }] }]"
              :getPopupContainer=" triggerNode => {
                return triggerNode.parentNode || document.body;
              }"
            >
              <a-select-option value="1">不审核</a-select-option>
              <a-select-option value="2">审核</a-select-option>
              <a-select-option value="3">自动判断</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">{{ $t('propertySettings.scanning') }}:</span>
      <a-row>
        <a-col
          :xs="24"
          :sm="24"
          :md="12"
          :lg="12"
          :xl="12"
        >
          <a-form-item
            :label="$t('propertySettings.scanning')"
            v-bind="formItemLayout"
          >
            <a-radio-group
              buttonStyle="solid"
              v-decorator="['scanning']"
            >
              <a-radio-button value="1">扫描</a-radio-button>
              <a-radio-button value="2">无码输入</a-radio-button>
            </a-radio-group>
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">主副数量推算:</span>
      <a-row>
        <a-col
          :xs="24"
          :sm="24"
          :md="12"
          :lg="12"
          :xl="12"
        >
          <a-form-item
            label="按单重推算"
            v-bind="formItemLayout"
          >
            <a-switch v-model="dz" />
          </a-form-item>
        </a-col>
      </a-row>
      <!-- <span style="font-size:1rem">FCIM同步:</span>
      <a-row>
        <a-col
          :xs="24"
          :sm="24"
          :md="12"
          :lg="12"
          :xl="12"
        >
          <a-form-item
            label="FCIM同步"
            v-bind="formItemLayout"
          >
            <a-switch v-model="fcim" />
          </a-form-item>
        </a-col>
      </a-row> -->
      <span style="font-size:1rem">{{ $t('propertySettings.trunc') }}:</span>
      <a-row>
        <a-col
          :xs="24"
          :sm="24"
          :md="12"
          :lg="12"
          :xl="12"
        >
          <a-form-item
            :label="$t('propertySettings.trunc')"
            v-bind="formItemLayout"
          >
            <a-select
              style="width:100%"
              allowClear
              v-decorator="['trunc', { rules: [{ message:$t('propertySettings.trunc') }] }]"
              :getPopupContainer=" triggerNode => {
                return triggerNode.parentNode || document.body;
              }"
            >
              <a-select-option value="1">向上取整</a-select-option>
              <a-select-option value="2">向下取取整</a-select-option>
              <a-select-option value="3">不取整</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
      <!-- 添加 -->
      <a-row>
        <a-col
          :xs="24"
          :sm="24"
          :md="12"
          :lg="12"
          :xl="20"
        >
          <a-form-item
            label="免检是否生成进货单"
            v-bind="formItemLayout"
          >
            <a-switch v-model="automaticPc" />
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">流水码:</span>
      <a-row>
        <a-col
          :xs="24"
          :sm="24"
          :md="12"
          :lg="12"
          :xl="12"
        >
          <a-form-item
            label="流水码允许拆分"
            v-bind="formItemLayout"
          >
            <a-switch v-model="isSplit" />
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">单号显示:</span>
      <a-row>
        <a-col
          :xs="24"
          :sm="24"
          :md="12"
          :lg="12"
          :xl="12"
        >
          <a-form-item
            label="单号显示"
            v-bind="formItemLayout"
          >
            <a-switch v-model="stime" />
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">应税内含->应税外加:</span>
      <a-row>
        <a-col
          :xs="24"
          :sm="24"
          :md="12"
          :lg="12"
          :xl="12"
        >
          <a-form-item
            label="应税内含->应税外加"
            v-bind="formItemLayout"
          >
            <a-switch v-model="amtChk" />
          </a-form-item>
        </a-col>
      </a-row>
      <!-- 添加 -->
    </a-form>
    <a-row :gutter="16">
      <a-col
        class="gutter-row"
        :span="12"
        style="text-align:right"
      >
        <a-button
          id="ok"
          type="primary"
          @click="handleOK"
        >{{ $t('public.save') }}{{ obj.subname }}</a-button>
      </a-col>
      <a-col
        class="gutter-row"
        :span="12"
        style="text-align:left"
      >
        <a-button
          id="cancel"
          @click="handleCancel"
        >{{ $t('public.cancel') }}</a-button>
      </a-col>
    </a-row>
  </div>
</template>
<script>
import { getWh, addBarPswdProp, getBarPswdProps } from '@/api/barcode/propertySettings'
import debounce from 'lodash.debounce'
export default {
  props: {
    obj: {
      required: true,
      type: Object
    },
    cid: {
      required: true,
      type: String
    },
    row: {
      required: true,
      type: Object
    }
  },
  data () {
    this.getWhs = debounce(this.getWhs, 300)
    return {
      title: '',
      visible: true,
      confirmLoading: true,
      form: this.$form.createForm(this),
      source: false,
      apart: false,
      inputQty: false,
      boxExistence: false,
      scanBatNo: false,
      dz: false,
      fcim: false,
      fetching: false,
      automaticPc: true,
      isSplit: false,
      stime: false,
      whlist: [],
      userList: [],
      subData: [],
       amtChk: false,
      onSubmitData: {
        // 保存属性对象
        compno: '',
        roleno: '',
        typeId: '6',
        pgm: '',
        fldName: '',
        fldValue: ''
      },
      formItemLayout: {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 7 },
          md: { span: 10 },
          lg: { span: 10 }
        },
        wrapperCol: {
          xs: { span: 5 },
          sm: { span: 16 },
          md: { span: 14 },
          lg: { span: 14 }
        }
      }

    }
  },
  created () {
    if (this.obj.subname) {
      this.getpop()
    }
    // this.whlist = JSON.parse(sessionStorage.getItem('allWh'))
  },
  methods: {
    getpop () {
      const obj = {
        compno: this.row.compno,
        roleno: this.row.roleno,
        pgm: this.cid
      }
      getBarPswdProps(obj).then(res => {
        this.subData = res.data
        const arr = this.subData
        if (arr.length > 0) {
          setTimeout(() => {
            function tran (name) {
              let ind
              arr.forEach((e, index) => {
                if (e.fldName === name) {
                  return ind = index
                }
              })
              return ind
            }
            // 添加
            this.automaticPc = JSON.parse(tran('automaticPc') === undefined ? this.automaticPc : arr[tran('automaticPc')].fldValue)
            this.amtChk = JSON.parse(tran('amtChk') === undefined || arr[tran('amtChk')].fldValue === '' || arr[tran('amtChk')].fldValue === null ? this.amtChk : arr[tran('amtChk')].fldValue)
            // 添加
            // this.fcim = JSON.parse(tran('fcim') === undefined || arr[tran('fcim')].fldValue === '' ? this.fcim : arr[tran('fcim')].fldValue)
            this.dz = JSON.parse(tran('dz') === undefined || arr[tran('dz')].fldValue === '' ? this.dz : arr[tran('dz')].fldValue)
            this.source = JSON.parse(tran('source') === undefined ? this.source : arr[tran('source')].fldValue)
            this.apart = JSON.parse(tran('apart') === undefined ? this.apart : arr[tran('apart')].fldValue)
            
            this.scanBatNo = JSON.parse(tran('scanBatNo') === undefined ? this.scanBatNo : arr[tran('scanBatNo')].fldValue)
            this.inputQty = JSON.parse(tran('inputQty') === undefined ? this.inputQty : arr[tran('inputQty')].fldValue)
            this.boxExistence = JSON.parse(tran('boxExistence') === undefined ? this.boxExistence : arr[tran('boxExistence')].fldValue)
            
            this.isSplit = JSON.parse(tran('isSplit') === undefined || arr[tran('isSplit')].fldValue === '' || arr[tran('isSplit')].fldValue === null ? this.isSplit : arr[tran('isSplit')].fldValue)
            
             this.stime = JSON.parse(tran('stime') === undefined ? this.stime : arr[tran('stime')].fldValue)
            // this.stime = JSON.parse(tran('stime') === undefined || arr[tran('stime')].fldValue === '' || arr[tran('stime')].fldValue === null ? this.stime : arr[tran('stime')].fldValue)
          
           
           this.form.setFieldsValue({
              exceed: tran('exceed') === undefined ? '' : arr[tran('exceed')].fldValue,
              installedWh: tran('installedWh') === undefined ? '' : arr[tran('installedWh')].fldValue,
              separator: tran('separator') === undefined ? '' : arr[tran('separator')].fldValue,
              exceedProportion: tran('exceedProportion') === undefined ? '' : arr[tran('exceedProportion')].fldValue,
              batch: tran('batch') === undefined ? '' : arr[tran('batch')].fldValue,
              examine: tran('examine') === undefined ? '' : arr[tran('examine')].fldValue,
              scanning: tran('scanning') === undefined ? '' : arr[tran('scanning')].fldValue,
              trunc: tran('trunc') === undefined ? '' : arr[tran('trunc')].fldValue,
              appointWh: tran('appointWh') === undefined ? '' : arr[tran('appointWh')].fldValue,
              bigDate: tran('bigDate') === undefined ? '' : arr[tran('bigDate')].fldValue,
              bigNum: tran('bigNum') === undefined ? '' : arr[tran('bigNum')].fldValue,
              smallDate: tran('smallDate') === undefined ? '' : arr[tran('smallDate')].fldValue,
              smallNum: tran('smallNum') === undefined ? '' : arr[tran('smallNum')].fldValue,
            })
            if (tran('appointWh') !== undefined && tran('appointWh') !== null && tran('appointWh') !== '') this.getWhs(1, arr[tran('appointWh')].fldValue)
            else
              this.getWhs()
          }, 1)
        }
      })
    },
    getWhs (page = 1, queryWhs = '') {
      this.fetching = true
      this.whlist = []
      getWh(
        Object.assign({
          current: page,
          size: 10,
          wh: queryWhs,
          name: queryWhs,
          rank: '2'
        })
      ).then(response => {
        this.whlist = response.data.records
        this.fetching = false
      })
        .catch(() => {
          this.fetching = false
        })
    },
    searchWh (value) {
      this.getWhs(1, value)
    },
    getData () {
      this.subData = []
      const fidArr = this.obj.fidArr
      fidArr.forEach(i => {
        this.subData.push(this.onSubmitData = {
          compno: this.row.compno,
          roleno: this.row.roleno,
          typeId: '6',
          pgm: this.cid,
          fldName: i,
          fldValue: ''
        })
      })
    },
    handleOK () {
      this.getData()
      this.form.validateFields((err, values) => {
        const arr = this.subData
        arr.forEach(i => {
          if (i.fldName === 'exceed') {
            i.fldValue = values.exceed
          }
          if (i.fldName === 'installedWh') {
            i.fldValue = values.installedWh
          }
          if (i.fldName === 'appointWh') {
            i.fldValue = values.appointWh
          }
          if (i.fldName === 'bigDate') {
            i.fldValue = values.bigDate
          }
          if (i.fldName === 'bigNum') {
            i.fldValue = values.bigNum
          }
          if (i.fldName === 'smallDate') {
            i.fldValue = values.smallDate
          }
          if (i.fldName === 'smallNum') {
            i.fldValue = values.smallNum
          }
          if (i.fldName === 'separator') {
            i.fldValue = values.separator
          }
          if (i.fldName === 'inputQty') {
            i.fldValue = this.inputQty + ''
          }
          if (i.fldName === 'boxExistence') {
            i.fldValue = this.boxExistence + ''
          }
          
          if (i.fldName === 'exceedProportion') {
            i.fldValue = values.exceedProportion
          }
          if (i.fldName === 'source') {
            i.fldValue = this.source + ''
          }
          if (i.fldName === 'apart') {
            i.fldValue = this.apart + ''
          }
          
          if (i.fldName === 'batch') {
            i.fldValue = values.batch
          }
          if (i.fldName === 'examine') {
            i.fldValue = values.examine
          }
          if (i.fldName === 'dz') {
            i.fldValue = this.dz
          }
          // if (i.fldName === 'fcim') {
          //   i.fldValue = this.fcim
          // }
          if (i.fldName === 'scanning') {
            i.fldValue = values.scanning
          }
          if (i.fldName === 'trunc') {
            i.fldValue = values.trunc
          }
          if (i.fldName === 'scanBatNo') {
            i.fldValue = this.scanBatNo
          }
          // 添加
          if (i.fldName === 'automaticPc') {
            i.fldValue = this.automaticPc
          }
          if (i.fldName === 'isSplit') {
            i.fldValue = this.isSplit
          }
          if (i.fldName === 'stime') {
            i.fldValue = this.stime
          }
          if (i.fldName === 'amtChk') {
            i.fldValue = this.amtChk
          }
          // 添加

        })
        if (!err) {
          addBarPswdProp(this.subData)
            .then(() => {
              this.$message.success(this.$t('public.success'))
            })
            .catch(() => {
              this.$message.error(this.$t('public.error'))
            })
        }
      })
    },
    handleCancel () {
      this.$emit('Cancel')
      this.subData = []
    }
  }
}
</script>
