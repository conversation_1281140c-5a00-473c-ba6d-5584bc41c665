<template>
  <div>
    <a-form :form="form" ref="form">
      <span style="font-size:1rem">{{ $t('propertySettings.ware') }}:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item :label="$t('propertySettings.ware')" v-bind="formItemLayout">
            <a-select style="width:100%" allowClear
              v-decorator="['installedWh', { rules: [{ message:$t('propertySettings.ware') }] }]" :getPopupContainer=" triggerNode => {
                return triggerNode.parentNode || document.body;
              }">
              <a-select-option value="1">来源单仓库</a-select-option>
              <a-select-option value="2">扫描仓库</a-select-option>
              <a-select-option value="3">条码仓库</a-select-option>
              <a-select-option value="4">预设仓库</a-select-option>
              <a-select-option value="5">指定仓库</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item :label="$t('propertySettings.waret')" v-bind="formItemLayout">
            <a-select show-search allowClear @search="searchWh" @focus="getWhs()" :filter-option="false"
              v-decorator="['appointWh',{rules: [{ message: $t('propertySettings.waret') }],}]">
              <a-spin v-if="fetching" slot="notFoundContent" size="small" />
              <a-select-option v-for="(i, index) in whlist" :key="index" :value="i.wh">{{ i.name }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <!-- <span style="font-size:1rem">缴库限制方式:</span>
      <a-row>
        <a-col
          :xs="24"
          :sm="24"
          :md="12"
          :lg="12"
          :xl="12"
        >
          <a-form-item
            label="缴库限制方式"
            v-bind="formItemLayout"
          >
          <a-select
              style="width:100%"
              v-decorator="['limit', { rules: [{ message:$t('propertySettings.ware') }] }]"
              :getPopupContainer=" triggerNode => {
                return triggerNode.parentNode || document.body;
              }"
            >
              <a-select-option
              v-for="(i, index) in limitData"
              :key="index"
              :value="i.value"
              >
                {{ i.name }}
              </a-select-option>
             
            </a-select>
          </a-form-item>
        </a-col>
      </a-row> -->

      <span style="font-size:1rem">缴库日期是否可修改:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="是否" v-bind="formItemLayout">
            <a-switch v-model="change" />
          </a-form-item>
        </a-col>
      </a-row>

      <!-- <span style="font-size:1rem">非批号管制的货品，批号插入批号汇总栏位否：</span>
      <a-row>
        <a-col
          :xs="24"
          :sm="24"
          :md="12"
          :lg="12"
          :xl="12"
        >
          <a-form-item
            label="是否"
            v-bind="formItemLayout"
          >
            <a-switch v-model="remark" />
          </a-form-item>
        </a-col>
      </a-row>

      <span style="font-size:1rem">{{ $t('propertySettings.sheet') }}:</span>
      <a-row>
        <a-col
          :xs="24"
          :sm="24"
          :md="12"
          :lg="12"
          :xl="12"
        >
          <a-form-item
            :label="$t('propertySettings.Separator')"
            v-bind="formItemLayout"
          >
            <a-input v-decorator="['separator', { rules: [{ message:$t('propertySetting.Separator') }] }]"></a-input>
          </a-form-item>
        </a-col>
      </a-row> -->

      <span style="font-size:1rem">扫描含员工/机台的条码是否记录作业人员完工缴库数量:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="是否" v-bind="formItemLayout">
            <a-switch v-model="completionRecord" />
          </a-form-item>
        </a-col>
      </a-row>

      <!-- <span style="font-size:1rem">倒冲库存不足是否弹窗:</span>
      <a-row>
        <a-col
          :xs="24"
          :sm="24"
          :md="12"
          :lg="12"
          :xl="12"
        >
          <a-form-item
            label="是否"
            v-bind="formItemLayout"
          >
            <a-switch v-model="popup" />
          </a-form-item>
        </a-col>
      </a-row> -->
      <span style="font-size:1rem">{{ $t('propertySettings.Quantity') }}:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item :label="$t('propertySettings.Quantitys')" v-bind="formItemLayout">
            <a-switch v-model="inputQty" />
          </a-form-item>
        </a-col>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="是否可拆码" v-bind="formItemLayout">
            <a-switch v-model="boxExistence" />
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">扫描设置:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="批号匹配" v-bind="formItemLayout">
            <a-switch v-model="scanBatNo" />
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">{{ $t('propertySettings.detection') }}:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item :label="$t('propertySettings.detection')" v-bind="formItemLayout">
            <a-select style="width:100%" allowClear
              v-decorator="['exceed', { rules: [{ message:$t('propertySettings.detection') }] }]" :getPopupContainer=" triggerNode => {
                return triggerNode.parentNode || document.body;
              }">
              <a-select-option value="1">管制</a-select-option>
              <a-select-option value="2">提示</a-select-option>
              <a-select-option value="3">不管制</a-select-option>
              <a-select-option value="4">允许在超交比例内</a-select-option>
              <a-select-option value="5">自动扣除多余数量</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item :label="$t('propertySettings.Proportion')" v-bind="formItemLayout">
            <a-input-number :min="0" v-decorator="['exceedProportion']" />
          </a-form-item>
        </a-col>
        <!-- <span style="font-size:1rem">{{ $t('propertySettings.sheet') }}111:</span> -->
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="拆分弹出" v-bind="formItemLayout">
            <a-switch v-model="apart" />
          </a-form-item>
        </a-col>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item :label="$t('propertySettings.basic')" v-bind="formItemLayout">
            <a-switch v-model="source" />
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">{{ $t('propertySettings.Batch') }}:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item :label="$t('propertySettings.serialNumber')" v-bind="formItemLayout">
            <a-select style="width:100%" allowClear
              v-decorator="['batch', { rules: [{ message:$t('propertySettings.Batch') }] }]" :getPopupContainer=" triggerNode => {
                return triggerNode.parentNode || document.body;
              }">
              <a-select-option value="1">按货品基础资料</a-select-option>
              <a-select-option value="2">不管制</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">{{ $t('propertySettings.Bill') }}:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item :label="$t('propertySettings.Bill')" v-bind="formItemLayout">
            <a-select style="width:100%" allowClear
              v-decorator="['examine', { rules: [{ message:$t('propertySettings.Bill') }] }]" :getPopupContainer=" triggerNode => {
                return triggerNode.parentNode || document.body;
              }">
              <a-select-option value="1">不审核</a-select-option>
              <a-select-option value="2">审核</a-select-option>
              <a-select-option value="3">自动判断</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">主副数量推算:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="按单重推算" v-bind="formItemLayout">
            <a-switch v-model="dz" />
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">FCIM同步:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="FCIM同步" v-bind="formItemLayout">
            <a-switch v-model="fcim" />
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">流水码:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="流水码允许拆分" v-bind="formItemLayout">
            <a-switch v-model="isSplit" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row :gutter="16">
        <a-col class="gutter-row" :span="12" style="text-align:right">
          <a-button id="ok" type="primary" @click="handleOK">{{ $t('public.save') }}{{ obj.subname }}</a-button>
        </a-col>
        <a-col class="gutter-row" :span="12" style="text-align:left">
          <a-button id="cancel" @click="handleCancel">{{ $t('public.cancel') }}</a-button>
        </a-col>
      </a-row>
    </a-form>
  </div>
</template>
<script>
  import { getWh, addBarPswdProp, getBarPswdProps } from '@/api/barcode/propertySettings'

  export default {
    props: {
      obj: {
        required: true,
        type: Object
      },
      cid: {
        required: true,
        type: String
      },
      row: {
        required: true,
        type: Object
      }
    },
    data() {
      return {
        isSplit: false,
        title: '',
        visible: true,
        confirmLoading: true,
        form: this.$form.createForm(this),
        whlist: [],
        userList: [],
        subData: [],
        limitData: [
          {
            name: '应生产量',
            value: '1'
          },
          {
            name: '材料领料套数(<=应生产量)',
            value: '5'
          }
        ],
        source: false,
        apart: false,
        inputQty: false,
        boxExistence: false,
        fcim: false,
        remark: false,
        completionRecord: false,
        popup: false,
        dz: false,
        scanBatNo: false,
        change: false,
        fetching: false,
        onSubmitData: {
          // 保存属性对象
          compno: '',
          roleno: '',
          typeId: '6',
          pgm: '',
          fldName: '',
          fldValue: ''
        },
        formItemLayout: {
          labelCol: {
            xs: { span: 24 },
            sm: { span: 7 },
            md: { span: 8 },
            lg: { span: 8 }
          },
          wrapperCol: {
            xs: { span: 5 },
            sm: { span: 16 },
            md: { span: 17 },
            lg: { span: 16 }
          }
        }

      }
    },
    created() {

      if (this.obj.subname) {
        this.getpop()
      }
    },
    methods: {
      getpop() {
        const obj = {
          compno: this.row.compno,
          roleno: this.row.roleno,
          pgm: this.cid
        }
        getBarPswdProps(obj).then(res => {
          this.subData = res.data
          const arr = this.subData
          if (arr.length > 0) {
            setTimeout(() => {
              function tran(name) {
                let ind
                arr.forEach((e, index) => {
                  if (e.fldName === name) {
                    return ind = index
                  }
                })
                return ind
              }
              this.isSplit = JSON.parse(tran('isSplit') === undefined || arr[tran('isSplit')].fldValue === '' || arr[tran('isSplit')].fldValue === null ? this.isSplit : arr[tran('isSplit')].fldValue)

              this.change = JSON.parse(tran('change') === undefined || arr[tran('change')].fldValue === undefined || arr[tran('change')].fldValue === '' || arr[tran('change')].fldValue === null ? this.change : arr[tran('change')].fldValue)
              this.remark = JSON.parse(tran('remark') === undefined || arr[tran('remark')].fldValue === undefined || arr[tran('remark')].fldValue === '' || arr[tran('remark')].fldValue === null ? this.remark : arr[tran('remark')].fldValue)
              this.completionRecord = JSON.parse(tran('completionRecord') === undefined || arr[tran('completionRecord')].fldValue === undefined || arr[tran('completionRecord')].fldValue === '' || arr[tran('completionRecord')].fldValue === null ? this.completionRecord : arr[tran('completionRecord')].fldValue)
              this.popup = JSON.parse(tran('popup') === undefined || arr[tran('popup')].fldValue === undefined || arr[tran('popup')].fldValue === '' || arr[tran('popup')].fldValue === null ? this.popup : arr[tran('popup')].fldValue)
              this.source = JSON.parse(tran('source') === undefined || arr[tran('source')].fldValue === undefined || arr[tran('source')].fldValue === '' || arr[tran('source')].fldValue === null ? this.source : arr[tran('source')].fldValue)
              this.apart = JSON.parse(tran('apart') === undefined || arr[tran('apart')].fldValue === undefined || arr[tran('apart')].fldValue === '' || arr[tran('apart')].fldValue === null ? this.apart : arr[tran('apart')].fldValue)
              this.boxExistence = JSON.parse(tran('boxExistence') === undefined ? this.boxExistence : arr[tran('boxExistence')].fldValue)
              this.inputQty = JSON.parse(tran('inputQty') === undefined || arr[tran('inputQty')].fldValue === undefined || arr[tran('inputQty')].fldValue === '' || arr[tran('inputQty')].fldValue === null ? this.inputQty : arr[tran('inputQty')].fldValue)
              this.scanBatNo = JSON.parse(tran('scanBatNo') === undefined || arr[tran('scanBatNo')].fldValue === undefined || arr[tran('scanBatNo')].fldValue === '' || arr[tran('scanBatNo')].fldValue === null ? this.scanBatNo : arr[tran('scanBatNo')].fldValue)
              this.dz = JSON.parse(tran('dz') === undefined || arr[tran('dz')].fldValue === undefined || arr[tran('dz')].fldValue === '' || arr[tran('dz')].fldValue === null ? this.dz : arr[tran('dz')].fldValue)
              this.fcim = JSON.parse(tran('fcim') === undefined || arr[tran('fcim')].fldValue === undefined || arr[tran('fcim')].fldValue === '' || arr[tran('fcim')].fldValue === null ? this.fcim : arr[tran('fcim')].fldValue)
              this.form.setFieldsValue({
                separator: tran('separator') === undefined ? '' : arr[tran('separator')].fldValue,
                exceed: tran('exceed') === undefined ? '' : arr[tran('exceed')].fldValue,
                installedWh: tran('installedWh') === undefined ? '' : arr[tran('installedWh')].fldValue,
                appointWh: tran('appointWh') === undefined ? '' : arr[tran('appointWh')].fldValue,
                exceedProportion: tran('exceedProportion') === undefined ? '' : arr[tran('exceedProportion')].fldValue,
                batch: tran('batch') === undefined ? '' : arr[tran('batch')].fldValue,
                examine: tran('examine') === undefined ? '' : arr[tran('examine')].fldValue,
                limit: tran('limit') === undefined ? '1' : arr[tran('limit')].fldValue
              })
              if (tran('appointWh') !== undefined && tran('appointWh') !== null && tran('appointWh') !== '') this.getWhs(1, arr[tran('appointWh')].fldValue)
              else
                this.getWhs()
            }, 1)
          }
        })
      },
      getWhs(page = 1, queryWhs = '') {
        this.fetching = true
        this.whlist = []
        getWh(
          Object.assign({
            current: page,
            size: 10,
            wh: queryWhs,
            name: queryWhs,
            rank: '2'
          })
        ).then(response => {
          this.whlist = response.data.records
          this.fetching = false
        })
          .catch(() => {
            this.fetching = false
          })
      },
      searchWh(value) {
        this.getWhs(1, value)
      },
      getData() {
        this.subData = []
        const fidArr = this.obj.fidArr
        fidArr.forEach(i => {
          this.subData.push(this.onSubmitData = {
            compno: this.row.compno,
            roleno: this.row.roleno,
            typeId: '6',
            pgm: this.cid,
            fldName: i,
            fldValue: ''
          })
        })
      },
      handleOK() {
        this.getData()

        this.form.validateFields((err, values) => {
          const arr = this.subData
          arr.forEach(i => {
            if (i.fldName === 'exceed') {
              i.fldValue = values.exceed
            }
            if (i.fldName === 'installedWh') {
              i.fldValue = values.installedWh
            }
            if (i.fldName === 'appointWh') {
              i.fldValue = values.appointWh
            }
            if (i.fldName === 'limit') {
              i.fldValue = values.limit
            }
            if (i.fldName === 'inputQty') {
              i.fldValue = this.inputQty
            }
            if (i.fldName === 'boxExistence') {
              i.fldValue = this.boxExistence
            }

            if (i.fldName === 'exceedProportion') {
              i.fldValue = values.exceedProportion
            }
            if (i.fldName === 'source') {
              i.fldValue = this.source
            }
            if (i.fldName === 'apart') {
              i.fldValue = this.apart
            }

            if (i.fldName === 'batch') {
              i.fldValue = values.batch
            }
            if (i.fldName === 'examine') {
              i.fldValue = values.examine
            }
            if (i.fldName === 'completionRecord') {
              i.fldValue = this.completionRecord
            }
            if (i.fldName === 'fcim') {
              i.fldValue = this.fcim
            }
            if (i.fldName === 'dz') {
              i.fldValue = this.dz
            }
            if (i.fldName === 'scanBatNo') {
              i.fldValue = this.scanBatNo
            }
            if (i.fldName === 'separator') {
              i.fldValue = values.separator
            }
            if (i.fldName === 'popup') {
              i.fldValue = this.popup
            }
            if (i.fldName === 'remark') {
              i.fldValue = this.remark
            }
            if (i.fldName === 'change') {
              i.fldValue = this.change
            }
            if (i.fldName === 'isSplit') {
              i.fldValue = this.isSplit
            }
          })

          if (!err) {
            addBarPswdProp(this.subData)
              .then(() => {
                this.$message.success(this.$t('public.success'))
              })
              .catch(() => {
                this.$message.error(this.$t('public.error'))
              })
          }
        })
      },
      handleCancel() {

        this.$emit('Cancel')
        this.subData = []
      }
    }
  }
</script>