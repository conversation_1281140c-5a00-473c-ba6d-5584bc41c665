<template>
  <div>
    <a-form :form="form" ref="form">
      <span style="font-size:1rem">模具是否可重复领用:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="是否" v-bind="formItemLayout">
            <a-switch v-model="mj1" />
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">领用模具不能超出生产单据范围:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="是否" v-bind="formItemLayout">
            <a-switch v-model="mjly" />
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">依生产单据领用模具:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="是否" v-bind="formItemLayout">
            <a-switch v-model="mj3" />
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">{{ $t('propertySettings.Bill') }}:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item :label="$t('propertySettings.Bill')" v-bind="formItemLayout">
            <a-select style="width:100%" allowClear
              v-decorator="['examine', { rules: [{ message:$t('propertySettings.Bill') }] }]" :getPopupContainer=" triggerNode => {
                return triggerNode.parentNode || document.body;
              }">
              <a-select-option value="1">不审核</a-select-option>
              <a-select-option value="2">审核</a-select-option>
              <a-select-option value="3">自动判断</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">条码设定</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="是否可拆码" v-bind="formItemLayout">
            <a-switch v-model="boxExistence" />
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">流水码:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="流水码允许拆分" v-bind="formItemLayout">
            <a-switch v-model="isSplit" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <a-row :gutter="16">
      <a-col class="gutter-row" :span="12" style="text-align:right">
        <a-button id="ok" type="primary" @click="handleOK">{{ $t('public.save') }}{{ obj.subname }}</a-button>
      </a-col>
      <a-col class="gutter-row" :span="12" style="text-align:left">
        <a-button id="cancel" @click="handleCancel">{{ $t('public.cancel') }}</a-button>
      </a-col>
    </a-row>
  </div>
</template>
<script>
  import { addBarPswdProp, getBarPswdProps } from '@/api/barcode/propertySettings'
  export default {
    props: {
      obj: {
        required: true,
        type: Object
      },
      cid: {
        required: true,
        type: String
      },
      row: {
        required: true,
        type: Object
      }
    },
    data() {
      return {
        isSplit: false,
        title: '',
        visible: true,
        confirmLoading: true,
        boxExistence: false,
        mj1: false,
        mjly: false,
        mj3: false,
        form: this.$form.createForm(this),
        whlist: [],
        userList: [],
        deptsList: [],
        whs: [],
        subData: [],
        onSubmitData: {
          // 保存属性对象
          compno: '',
          roleno: '',
          typeId: '6',
          pgm: '',
          fldName: '',
          fldValue: ''
        },
        formItemLayout: {
          labelCol: {
            xs: { span: 24 },
            sm: { span: 7 },
            md: { span: 8 },
            lg: { span: 8 }
          },
          wrapperCol: {
            xs: { span: 5 },
            sm: { span: 16 },
            md: { span: 17 },
            lg: { span: 16 }
          }
        }
      }
    },
    created() {
      if (this.obj.subname) {
        this.getpop()
      }
    },
    methods: {
      getpop() {
        const obj = {
          compno: this.row.compno,
          roleno: this.row.roleno,
          pgm: this.cid
        }
        getBarPswdProps(obj).then(res => {
          this.subData = res.data
          const arr = this.subData
          if (arr.length > 0) {
            setTimeout(() => {
              function tran(name) {
                let ind
                arr.forEach((e, index) => {
                  if (e.fldName === name) {
                    return ind = index
                  }
                })
                return ind
              }
              this.isSplit = JSON.parse(tran('isSplit') === undefined || arr[tran('isSplit')].fldValue === '' || arr[tran('isSplit')].fldValue === null ? this.isSplit : arr[tran('isSplit')].fldValue)

              this.boxExistence = JSON.parse(tran('boxExistence') === undefined ? this.boxExistence : arr[tran('boxExistence')].fldValue)
              this.mj1 = JSON.parse(tran('mj1') === undefined ? this.mj1 : arr[tran('mj1')].fldValue)
              this.mjly = JSON.parse(tran('mjly') === undefined ? this.mj1 : arr[tran('mjly')].fldValue)
              this.mj3 = JSON.parse(tran('mj3') === undefined ? this.mj1 : arr[tran('mj3')].fldValue)
              this.form.setFieldsValue({
                examine: tran('examine') === undefined ? '' : arr[tran('examine')].fldValue
              })
            }, 1)
          }
        })
      },
      getData() {
        this.subData = []
        const fidArr = this.obj.fidArr
        fidArr.forEach(i => {
          this.subData.push(this.onSubmitData = {
            compno: this.row.compno,
            roleno: this.row.roleno,
            typeId: '6',
            pgm: this.cid,
            fldName: i,
            fldValue: ''
          })
        })
      },
      handleOK() {
        this.getData()
        this.form.validateFields((err, values) => {
          const arr = this.subData
          arr.forEach(i => {
            if (i.fldName === 'mj1') {
              i.fldValue = this.mj1
            }
            if (i.fldName === 'mjly') {
              i.fldValue = this.mjly
            }
            if (i.fldName === 'mj3') {
              i.fldValue = this.mj3
            }
            if (i.fldName === 'examine') {
              i.fldValue = values.examine
            }
            if (i.fldName === 'boxExistence') {
              i.fldValue = this.boxExistence + ''
            }
            if (i.fldName === 'isSplit') {
              i.fldValue = this.isSplit
            }
          })
          if (!err) {
            addBarPswdProp(this.subData)
              .then(() => {
                this.$message.success(this.$t('public.success'))
              })
              .catch(() => {
                this.$message.error(this.$t('public.error'))
              })
          }
        })
      },
      handleCancel() {
        this.$emit('Cancel')
        this.subData = []
      }
    }
  }
</script>