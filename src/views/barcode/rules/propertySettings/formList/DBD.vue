<template>
  <div>
    <a-form :form="form" ref="form">
      <span style="font-size: 1rem">{{ $t('propertySettings.Quantity') }}:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item :label="$t('propertySettings.Quantitys')" v-bind="formItemLayout">
            <a-switch v-model="inputQty" />
          </a-form-item>
        </a-col>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="是否可拆码" v-bind="formItemLayout">
            <a-switch v-model="boxExistence" />
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size: 1rem">{{ $t('propertySettings.Bill') }}:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item :label="$t('propertySettings.Bill')" v-bind="formItemLayout">
            <a-select style="width: 100%" allowClear
              v-decorator="['examine', { rules: [{ message: $t('propertySettings.Bill') }] }]" :getPopupContainer="
                (triggerNode) => {
                  return triggerNode.parentNode || document.body
                }
              ">
              <a-select-option value="1">不审核</a-select-option>
              <a-select-option value="2">审核</a-select-option>
              <a-select-option value="3">自动判断</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
      <!-- <span style="font-size: 1rem">FCIM同步:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="FCIM同步" v-bind="formItemLayout">
            <a-switch v-model="fcim" />
          </a-form-item>
        </a-col>
      </a-row> -->
      <span style="font-size: 1rem">{{ $t('propertySettings.Default') }}:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item :label="$t('propertySettings.Warehousing')" v-bind="formItemLayout">
            <a-select show-search allowClear @search="getUser" @focus="user" @change="userChange" :filter-option="false"
              v-decorator="['DbdIncomingUser', { rules: [{ message: $t('propertySettings.Warehousing') }] }]">
              <a-select-option v-for="item in userList" :key="item.salNo" :label="item.name" :value="item.salNo">{{
                item.name
                }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="申请部门" v-bind="formItemLayout">
            <a-select show-search allowClear @search="getDep" @focus="dep" @change="depChange" :filter-option="false"
              v-decorator="['DbdApplyDept', { rules: [{ message: $t('propertySettings.storage') }] }]">
              <a-select-option v-for="item in deptsList" :key="item.dep" :label="item.name" :value="item.dep">{{
                item.name
                }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item :label="$t('propertySettings.Appropriated')" v-bind="formItemLayout">
            <a-select show-search allowClear @search="getDep" @focus="dep" @change="DbdAllocateDeptChange"
              :filter-option="false"
              v-decorator="['DbdAllocateDept', { rules: [{ message: $t('propertySettings.Appropriated') }] }]">
              <a-select-option v-for="item in deptsList" :key="item.dep" :label="item.name" :value="item.dep">{{
                item.name
                }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item :label="$t('propertySettings.Dial')" v-bind="formItemLayout">
            <a-select show-search allowClear @search="getDep" @focus="dep" @change="DbdInDeptChange"
              :filter-option="false"
              v-decorator="['DbdIncomingDept', { rules: [{ message: $t('propertySettings.Dial') }] }]">
              <a-select-option v-for="item in deptsList" :key="item.dep" :label="item.name" :value="item.dep">{{
                item.name
                }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item :label="$t('propertySettings.Outgoing')" v-bind="formItemLayout">
            <a-select show-search allowClear @search="getWhs" @focus="wh" @change="whChange" :filter-option="false"
              v-decorator="['DbdAllocateWh', { rules: [{ message: $t('propertySettings.Outgoing') }] }]">
              <a-select-option v-for="(item, index) in whs" :key="index" :label="item.name" :value="item.wh">{{
                item.name
                }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item :label="$t('propertySettings.going')" v-bind="formItemLayout">
            <a-select show-search allowClear @search="getWhs" @focus="wh" @change="ckWhChange" :filter-option="false"
              v-decorator="['DbdIncomingWh', { rules: [{ message: $t('propertySettings.going') }] }]">
              <a-select-option v-for="(item, index) in whs" :key="index" :label="item.name" :value="item.wh">{{
                item.name
                }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <a-row :gutter="16">
      <a-col class="gutter-row" :span="12" style="text-align: right">
        <a-button id="ok" type="primary" @click="handleOK">{{ $t('public.save') }}{{ obj.subname }}</a-button>
      </a-col>
      <a-col class="gutter-row" :span="12" style="text-align: left">
        <a-button id="cancel" @click="handleCancel">{{ $t('public.cancel') }}</a-button>
      </a-col>
    </a-row>
  </div>
</template>
<script>
  import { querySalm, deptListPage, getWh, addBarPswdProp, getBarPswdProps } from '@/api/barcode/propertySettings'

  export default {
    props: {
      obj: {
        required: true,
        type: Object,
      },
      cid: {
        required: true,
        type: String,
      },
      row: {
        required: true,
        type: Object,
      },
    },
    data() {
      return {
        title: '',
        userName: '',
        depName: '',
        DbDepName: '',
        DbdInName: '',
        whName: '',
        ckWhName: '',
        visible: true,
        confirmLoading: true,
        // source: true,
        inputQty: false,
        boxExistence: false,
        fcim: false,
        form: this.$form.createForm(this),
        whlist: [],
        subData: [],
        onSubmitData: {
          // 保存属性对象
          compno: '',
          roleno: '',
          typeId: '6',
          pgm: '',
          fldName: '',
          fldValue: '',
        },
        userList: [],
        deptsList: [],
        whs: [],
        formItemLayout: {
          labelCol: {
            xs: { span: 24 },
            sm: { span: 7 },
            md: { span: 8 },
            lg: { span: 8 },
          },
          wrapperCol: {
            xs: { span: 5 },
            sm: { span: 16 },
            md: { span: 17 },
            lg: { span: 16 },
          },
        },
      }
    },
    created() {
      if (this.obj.subname) {
        this.getpop()
        this.user()
        this.dep()
        this.wh()
      }
    },
    methods: {
      getpop() {
        const obj = {
          compno: this.row.compno,
          roleno: this.row.roleno,
          pgm: this.cid,
        }
        getBarPswdProps(obj).then((res) => {
          this.subData = res.data
          const arr = this.subData
          if (arr.length > 0) {
            setTimeout(() => {
              function tran(name) {
                let ind
                arr.forEach((e, index) => {
                  if (e.fldName === name) {
                    return ind = index
                  }
                })
                return ind
              }
              // this.fcim = JSON.parse(tran('fcim') === undefined || arr[tran('fcim')].fldValue === '' ? this.fcim : arr[tran('fcim')].fldValue)
              this.inputQty = JSON.parse(tran('inputQty') === undefined ? this.inputQty : arr[tran('inputQty')].fldValue)
              this.boxExistence = JSON.parse(tran('boxExistence') === undefined ? this.boxExistence : arr[tran('boxExistence')].fldValue)

              this.form.setFieldsValue({
                DbdIncomingUser: tran('DbdIncomingUserName') === undefined || arr[tran('DbdIncomingUserName')].fldValue === '' || arr[tran('DbdIncomingUserName')].fldValue === null ? '' : arr[tran('DbdIncomingUserName')].fldValue.split(',')[0],
                DbdApplyDept: tran('DbdApplyDeptName') === undefined || arr[tran('DbdApplyDeptName')].fldValue === '' || arr[tran('DbdApplyDeptName')].fldValue === null ? '' : arr[tran('DbdApplyDeptName')].fldValue.split(',')[0],
                DbdAllocateDept: tran('DbdAllocateDeptName') === undefined || arr[tran('DbdAllocateDeptName')].fldValue === '' || arr[tran('DbdAllocateDeptName')].fldValue === null ? '' : arr[tran('DbdAllocateDeptName')].fldValue.split(',')[0],
                DbdIncomingDept: tran('DbdIncomingDeptName') === undefined || arr[tran('DbdIncomingDeptName')].fldValue === '' || arr[tran('DbdIncomingDeptName')].fldValue === null ? '' : arr[tran('DbdIncomingDeptName')].fldValue.split(',')[0],
                DbdAllocateWh: tran('DbdAllocateWhName') === undefined || arr[tran('DbdAllocateWhName')].fldValue === '' || arr[tran('DbdAllocateWhName')].fldValue === null ? '' : arr[tran('DbdAllocateWhName')].fldValue.split(',')[0],
                DbdIncomingWh: tran('DbdIncomingWhName') === undefined || arr[tran('DbdIncomingWhName')].fldValue === '' || arr[tran('DbdIncomingWhName')].fldValue === null ? '' : arr[tran('DbdIncomingWhName')].fldValue.split(',')[0],
                examine: tran('examine') === undefined ? '' : arr[tran('examine')].fldValue,
              })
              if (arr[tran('DbdIncomingUserName')].fldValue !== undefined && arr[tran('DbdIncomingUserName')].fldValue !== '' && arr[tran('DbdIncomingUserName')].fldValue !== null) {
                this.user(1, arr[tran('DbdIncomingUserName')].fldValue.split(',')[0])
                this.userName = arr[tran('DbdIncomingUserName')].fldValue.split(',')[1]
              }
              if (arr[tran('DbdApplyDeptName')].fldValue !== undefined && arr[tran('DbdApplyDeptName')].fldValue !== '' && arr[tran('DbdApplyDeptName')].fldValue !== null) {
                this.dep(1, arr[tran('DbdApplyDeptName')].fldValue.split(',')[0])
                this.depName = arr[tran('DbdApplyDeptName')].fldValue.split(',')[1]
              }
              if (arr[tran('DbdAllocateDeptName')].fldValue !== undefined && arr[tran('DbdAllocateDeptName')].fldValue !== '' && arr[tran('DbdAllocateDeptName')].fldValue !== null) {
                this.dep(1, arr[tran('DbdAllocateDeptName')].fldValue.split(',')[0])
                this.DbDepName = arr[tran('DbdAllocateDeptName')].fldValue.split(',')[1]
              }
              if (arr[tran('DbdIncomingDeptName')].fldValue !== undefined && arr[tran('DbdIncomingDeptName')].fldValue !== '' && arr[tran('DbdIncomingDeptName')].fldValue !== null) {
                this.dep(1, arr[tran('DbdIncomingDeptName')].fldValue.split(',')[0])
                this.DbdInName = arr[tran('DbdIncomingDeptName')].fldValue.split(',')[1]
              }
              if (arr[tran('DbdAllocateWhName')].fldValue !== undefined && arr[tran('DbdAllocateWhName')].fldValue !== '' && arr[tran('DbdAllocateWhName')].fldValue !== null) {
                this.wh(1, arr[tran('DbdAllocateWhName')].fldValue.split(',')[0])
                this.whName = arr[tran('DbdAllocateWhName')].fldValue.split(',')[1]
              }
              if (arr[tran('DbdIncomingWhName')].fldValue !== undefined && arr[tran('DbdIncomingWhName')].fldValue !== '' && arr[tran('DbdIncomingWhName')].fldValue !== null) {
                this.wh(1, arr[tran('DbdIncomingWhName')].fldValue.split(',')[0])
                this.ckWhName = arr[tran('DbdIncomingWhName')].fldValue.split(',')[1]
              }
            }, 1)
          }
        })
      },
      getData() {
        this.subData = []
        const fidArr = this.obj.fidArr
        fidArr.forEach((i) => {
          this.subData.push(
            (this.onSubmitData = {
              compno: this.row.compno,
              roleno: this.row.roleno,
              typeId: '6',
              pgm: this.cid,
              fldName: i,
              fldValue: '',
            })
          )
        })
      },
      handleOK() {
        this.getData()
        this.form.validateFields((err, values) => {
          const arr = this.subData
          arr.forEach((i) => {
            if (i.fldName === 'inputQty') {
              i.fldValue = this.inputQty
            }
            if (i.fldName === 'boxExistence') {
              i.fldValue = this.boxExistence
            }
            if (i.fldName === 'DbdIncomingUserName') {
              if (values.DbdIncomingUser !== '' && values.DbdIncomingUser !== undefined && values.DbdIncomingUser !== null) {
                i.fldValue = values.DbdIncomingUser + ',' + this.userName
              } else {
                i.fldValue = ''
              }
            }
            if (i.fldName === 'DbdApplyDeptName') {
              if (values.DbdApplyDept !== '' && values.DbdApplyDept !== undefined && values.DbdApplyDept !== null) {
                i.fldValue = values.DbdApplyDept + ',' + this.depName
              } else {
                i.fldValue = ''
              }
            }
            if (i.fldName === 'DbdAllocateDeptName') {
              if (values.DbdAllocateDept !== '' && values.DbdAllocateDept !== undefined && values.DbdAllocateDept !== null) {
                i.fldValue = values.DbdAllocateDept + ',' + this.DbDepName
              } else {
                i.fldValue = ''
              }
            }
            if (i.fldName === 'DbdIncomingDeptName') {
              if (values.DbdIncomingDept !== '' && values.DbdIncomingDept !== undefined && values.DbdIncomingDept !== null) {
                i.fldValue = values.DbdIncomingDept + ',' + this.DbdInName
              } else {
                i.fldValue = ''
              }
            }
            if (i.fldName === 'DbdAllocateWhName') {
              if (values.DbdAllocateWh !== '' && values.DbdAllocateWh !== undefined && values.DbdAllocateWh !== null) {
                i.fldValue = values.DbdAllocateWh + ',' + this.whName
              } else {
                i.fldValue = ''
              }
            }
            if (i.fldName === 'DbdIncomingWhName') {
              if (values.DbdIncomingWh !== '' && values.DbdIncomingWh !== undefined && values.DbdIncomingWh !== null) {
                i.fldValue = values.DbdIncomingWh + ',' + this.ckWhName
              } else {
                i.fldValue = ''
              }
            }
            if (i.fldName === 'examine') {
              i.fldValue = values.examine
            }
            // if (i.fldName === 'fcim') {
            //   i.fldValue = this.fcim
            // }
          })
          if (!err) {
            addBarPswdProp(this.subData)
              .then(() => {
                this.$message.success(this.$t('public.success'))
              })
              .catch(() => {
                this.$message.error(this.$t('public.error'))
              })
          }
        })
      },
      user(page = 1, val) {
        querySalm(
          Object.assign({
            current: page,
            size: 10,
            name: val,
            salNo: val
          })
        ).then((res) => {
          this.userList = res.data.records
        })
      },
      getUser(val) {
        this.user(1, val)
      },
      // 获取申请部门
      dep(page = 1, val) {
        deptListPage(
          Object.assign({
            current: page,
            size: 10,
            dep: val,
            name: val
          })
        ).then((res) => {
          this.deptsList = res.data.records
        })
      },
      getDep(val) {
        this.dep(1, val)
      },
      userChange(val) {
        if (val) {
          const userName = this.userList.find(j => j.salNo === val)
          if (userName === undefined) return this.userName = ''
          this.userName = userName.name
          return
        }
        return this.userName = ''
      },
      // 申请部门change
      depChange(val) {
        if (val) {
          const depName = this.deptsList.find(j => j.dep === val)
          if (depName === undefined) return this.depName = ''
          this.depName = depName.name
          return
        }
        return this.depName = ''
      },
      DbdAllocateDeptChange(val) {
        if (val) {
          const DbDepName = this.deptsList.find(j => j.dep === val)
          if (DbDepName === undefined) return this.DbDepName = ''
          this.DbDepName = DbDepName.name
          return
        }
        return this.DbDepName = ''
      },
      DbdInDeptChange(val) {
        if (val) {
          const DbdInName = this.deptsList.find(j => j.dep === val)
          if (DbdInName === undefined) return this.DbdInName = ''
          this.DbdInName = DbdInName.name
          return
        }
        return this.DbdInName = ''
      },
      whChange(val) {
        if (val) {
          const whName = this.whs.find(j => j.wh === val)
          if (whName === undefined) return this.whName = ''
          this.whName = whName.name
          return
        }
        return this.whName = ''
      },
      ckWhChange(val) {
        if (val) {
          const ckWhName = this.whs.find(j => j.wh === val)
          if (ckWhName === undefined) return this.ckWhName = ''
          this.ckWhName = ckWhName.name
          return
        }
        return this.ckWhName = ''
      },

      wh(page = 1, val) {
        getWh(
          Object.assign({
            current: page,
            size: 10,
            wh: val,
            name: val,
            rank: '2'
          })
        ).then((res) => {
          this.whs = res.data.records
        })
      },
      getWhs(val) {
        this.wh(1, val)
      },

      handleCancel() {
        this.$emit('Cancel')
        this.subData = []
      },
    },
  }
</script>