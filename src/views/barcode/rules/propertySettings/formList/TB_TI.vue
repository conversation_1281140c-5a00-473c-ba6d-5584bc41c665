<template>
  <div>
    <a-form :form="form" ref="form">
      <span style="font-size:1rem">{{ $t('propertySettings.ware') }}</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item :label="$t('propertySettings.ware')" v-bind="formItemLayout">
            <a-select allowClear style="width:100%"
              v-decorator="['installedWh', { rules: [{ message:$t('propertySettings.ware') }] }]" :getPopupContainer=" triggerNode => {
                return triggerNode.parentNode || document.body;
              }">
              <a-select-option value="1">来源单仓库</a-select-option>
              <a-select-option value="2">扫描仓库</a-select-option>
              <a-select-option value="3">条码仓库</a-select-option>
              <a-select-option value="4">预设仓库</a-select-option>
              <a-select-option value="5">指定仓库</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item :label="$t('propertySettings.waret')" v-bind="formItemLayout">
            <a-select show-search allowClear @search="searchWh" @focus="getWhs()" :filter-option="false"
              v-decorator="['appointWh',{rules: [{ message: $t('propertySettings.waret') }],}]">
              <a-spin v-if="fetching" slot="notFoundContent" size="small" />
              <a-select-option v-for="(i, index) in whlist" :key="index" :value="i.wh">{{ i.name }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">{{ $t('propertySettings.sheet') }}:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item :label="$t('propertySettings.Separator')" v-bind="formItemLayout">
            <a-input v-decorator="['separator', { rules: [{ message:$t('propertySetting.Separator') }] }]"></a-input>
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">{{ $t('propertySettings.Quantity') }}:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item :label="$t('propertySettings.Quantitys')" v-bind="formItemLayout">
            <a-switch v-model="inputQty" />
          </a-form-item>
        </a-col>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="是否可拆码" v-bind="formItemLayout">
            <a-switch v-model="boxExistence" />
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">扫描设置:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="批号匹配" v-bind="formItemLayout">
            <a-switch v-model="scanBatNo" />
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">来源单部门:</span>
      <a-row>
        <a-col
          :xs="24"
          :sm="24"
          :md="12"
          :lg="12"
          :xl="12"
        >
          <a-form-item
            label="来源单部门"
            v-bind="formItemLayout"
          >
            <a-switch v-model="sourceDep" />
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">{{ $t('propertySettings.detection') }}:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item :label="$t('propertySettings.detection')" v-bind="formItemLayout">
            <a-select style="width:100%" allowClear
              v-decorator="['exceed', { rules: [{ message:$t('propertySettings.detection') }] }]" :getPopupContainer=" triggerNode => {
                return triggerNode.parentNode || document.body;
              }">
              <a-select-option value="1">管制</a-select-option>
              <a-select-option value="2">提示</a-select-option>
              <a-select-option value="3">不管制</a-select-option>
              <a-select-option value="4">允许在超交比例内</a-select-option>
              <a-select-option value="5">自动扣除多余数量</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item :label="$t('propertySettings.Proportion')" v-bind="formItemLayout">
            <a-input-number :min="0" v-decorator="['exceedProportion', { rules: [] }]" />
          </a-form-item>
        </a-col>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="拆分弹出" v-bind="formItemLayout">
            <a-switch v-model="apart" />
          </a-form-item>
        </a-col>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item :label="$t('propertySettings.basic')" v-bind="formItemLayout">
            <a-switch v-model="source" />
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">{{ $t('propertySettings.Batch') }}:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item :label="$t('propertySettings.serialNumber')" v-bind="formItemLayout">
            <a-select style="width:100%" allowClear
              v-decorator="['batch', { rules: [{ message:$t('propertySettings.Batch') }] }]" :getPopupContainer=" triggerNode => {
                return triggerNode.parentNode || document.body;
              }">
              <a-select-option value="1">按货品基础资料</a-select-option>
              <a-select-option value="2">不管制</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">{{ $t('propertySettings.Bill') }}:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item :label="$t('propertySettings.Bill')" v-bind="formItemLayout">
            <a-select style="width:100%" allowClear
              v-decorator="['examine', { rules: [{ message:$t('propertySettings.Bill') }] }]" :getPopupContainer=" triggerNode => {
                return triggerNode.parentNode || document.body;
              }">
              <a-select-option value="1">不审核</a-select-option>
              <a-select-option value="2">审核并自动提交</a-select-option>
              <a-select-option value="3">审核不自动提交</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">{{ $t('propertySettings.scanning') }}:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item :label="$t('propertySettings.scanning')" v-bind="formItemLayout">
            <a-radio-group buttonStyle="solid" v-decorator="['scanning', { rules: [{ }] }]">
              <a-radio-button value="1">扫描</a-radio-button>
              <a-radio-button value="2">无码输入</a-radio-button>
            </a-radio-group>
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">主副数量推算:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="按单重推算" v-bind="formItemLayout">
            <a-switch v-model="dz" />
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">FCIM同步:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="FCIM同步" v-bind="formItemLayout">
            <a-switch v-model="fcim" />
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">{{ $t('propertySettings.trunc') }}:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item :label="$t('propertySettings.trunc')" v-bind="formItemLayout">
            <a-select style="width:100%" allowClear
              v-decorator="['trunc', { rules: [{ message:$t('propertySettings.trunc') }] }]" :getPopupContainer=" triggerNode => {
                return triggerNode.parentNode || document.body;
              }">
              <a-select-option value="1">向上取整</a-select-option>
              <a-select-option value="2">向下取取整</a-select-option>
              <a-select-option value="3">不取整</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">周转箱数量缴回:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="周转箱数量缴回" v-bind="formItemLayout">
            <a-switch v-model="box" />
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">忽略单价计算:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="忽略单价计算" v-bind="formItemLayout">
            <a-switch v-model="ignoreUp" />
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">让步货品存储设置:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="让步货品存储设置" v-bind="formItemLayout">
            <a-select style="width:100%" allowClear v-decorator="['ckChk', { rules: [{ message:'让步货品存储设置' }] }]"
              :getPopupContainer=" triggerNode => {
                return triggerNode.parentNode || document.body;
              }">
              <a-select-option value="1">正常入库</a-select-option>
              <a-select-option value="3">客户废品仓</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">流水码:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="流水码允许拆分" v-bind="formItemLayout">
            <a-switch v-model="isSplit" />
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">批号库存出库顺序:</span>
      <a-row>
        <a-col
          :xs="24"
          :sm="24"
          :md="12"
          :lg="12"
          :xl="12"
        >
          <a-form-item
            label="先进先出"
            v-bind="formItemLayout"
          >
            <a-select
              style="width:100%"
              allowClear
              v-decorator="['batOrder', { rules: [{ message:$t('propertySettings.Bill') }] }]"
              :getPopupContainer=" triggerNode => {
                return triggerNode.parentNode || document.body;
              }"
            >
              <a-select-option value="1">不管控</a-select-option>
              <a-select-option value="3">管控（依入库日）</a-select-option>
              <a-select-option value="4">管控（依批号）</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <a-row :gutter="16">
      <a-col class="gutter-row" :span="12" style="text-align:right">
        <a-button id="ok" type="primary" @click="handleOK">{{ $t('public.save') }}{{ obj.subname }}</a-button>
      </a-col>
      <a-col class="gutter-row" :span="12" style="text-align:left">
        <a-button id="cancel" @click="handleCancel">{{ $t('public.cancel') }}</a-button>
      </a-col>
    </a-row>
  </div>
</template>
<script>

  import { getWh, addBarPswdProp, getBarPswdProps } from '@/api/barcode/propertySettings'

  export default {
    props: {
      obj: {
        required: true,
        type: Object
      },
      cid: {
        required: true,
        type: String
      },
      row: {
        required: true,
        type: Object
      }
    },
    data() {
      return {
        title: '',
        visible: true,
        sourceDep: false,
        confirmLoading: true,
        source: false,
        apart: false,
        inputQty: false,
        boxExistence: false,
        scanBatNo: false,
        dz: false,
        fcim: false,
        ignoreUp: false,
        box: false,
        fetching: false,
        form: this.$form.createForm(this),
        whlist: [],
        userList: [],
        subData: [],
        onSubmitData: {
          // 保存属性对象
          compno: '',
          roleno: '',
          typeId: '6',
          pgm: '',
          fldName: '',
          fldValue: ''
        },
        formItemLayout: {
          labelCol: {
            xs: { span: 24 },
            sm: { span: 7 },
            md: { span: 8 },
            lg: { span: 8 }
          },
          wrapperCol: {
            xs: { span: 5 },
            sm: { span: 16 },
            md: { span: 17 },
            lg: { span: 16 }
          }
        },
        isSplit: false,
      }
    },
    created() {
      if (this.obj.subname) {
        this.getpop()
      }
    },
    methods: {
      getpop() {
        const obj = {
          compno: this.row.compno,
          roleno: this.row.roleno,
          pgm: this.cid
        }
        getBarPswdProps(obj).then(res => {
          this.subData = res.data
          const arr = this.subData
          if (arr.length > 0) {
            setTimeout(() => {
              function tran(name) {
                let ind
                arr.forEach((e, index) => {
                  if (e.fldName === name) {
                    return ind = index
                  }
                })
                return ind
              }
              this.source = JSON.parse(tran('source') === undefined ? this.source : arr[tran('source')].fldValue)
              this.apart = JSON.parse(tran('apart') === undefined ? this.apart : arr[tran('apart')].fldValue)

              this.fcim = JSON.parse(tran('fcim') === undefined ? this.fcim : arr[tran('fcim')].fldValue)
              this.ignoreUp = JSON.parse(tran('ignoreUp') === undefined ? this.ignoreUp : arr[tran('ignoreUp')].fldValue)

              this.box = JSON.parse(tran('box') === undefined ? this.box : arr[tran('box')].fldValue)
              this.scanBatNo = JSON.parse(tran('scanBatNo') === undefined ? this.scanBatNo : arr[tran('scanBatNo')].fldValue)
              this.inputQty = JSON.parse(tran('inputQty') === undefined ? this.inputQty : arr[tran('inputQty')].fldValue)
              this.boxExistence = JSON.parse(tran('boxExistence') === undefined ? this.boxExistence : arr[tran('boxExistence')].fldValue)
              this.isSplit = JSON.parse(tran('isSplit') === undefined || arr[tran('isSplit')].fldValue === '' || arr[tran('isSplit')].fldValue === null ? this.isSplit : arr[tran('isSplit')].fldValue)
              this.sourceDep = JSON.parse(tran('sourceDep') === undefined ? this.sourceDep : arr[tran('sourceDep')].fldValue === "" ? this.sourceDep : arr[tran('sourceDep')].fldValue)
              this.dz = JSON.parse(tran('dz') === undefined ? this.dz : arr[tran('dz')].fldValue)
              this.form.setFieldsValue({
                exceed: tran('exceed') === undefined ? '' : arr[tran('exceed')].fldValue,
                installedWh: tran('installedWh') === undefined ? '' : arr[tran('installedWh')].fldValue,
                appointWh: tran('appointWh') === undefined ? '' : arr[tran('appointWh')].fldValue,
                separator: tran('separator') === undefined ? '' : arr[tran('separator')].fldValue,
                exceedProportion: tran('exceedProportion') === undefined ? '' : arr[tran('exceedProportion')].fldValue,
                batch: tran('batch') === undefined ? '' : arr[tran('batch')].fldValue,
                examine: tran('examine') === undefined ? '' : arr[tran('examine')].fldValue,
                scanning: tran('scanning') === undefined ? '' : arr[tran('scanning')].fldValue,
                trunc: tran('trunc') === undefined ? '' : arr[tran('trunc')].fldValue,
                ckChk: tran('ckChk') === undefined ? '' : arr[tran('ckChk')].fldValue,
              })
              if (tran('appointWh') !== undefined && tran('appointWh') !== null && tran('appointWh') !== '') this.getWhs(1, arr[tran('appointWh')].fldValue)
              else
                this.getWhs()
            }, 1)
          }
        })
      },
      getWhs(page = 1, queryWhs = '') {
        this.fetching = true
        this.whlist = []
        getWh(
          Object.assign({
            current: page,
            size: 10,
            wh: queryWhs,
            name: queryWhs,
            rank: '2'
          })
        ).then(response => {
          this.whlist = response.data.records
          this.fetching = false
        })
          .catch(() => {
            this.fetching = false
          })
      },
      searchWh(value) {
        this.getWhs(1, value)
      },
      getData() {
        this.subData = []
        const fidArr = this.obj.fidArr
        fidArr.forEach(i => {
          this.subData.push(this.onSubmitData = {
            compno: this.row.compno,
            roleno: this.row.roleno,
            typeId: '6',
            pgm: this.cid,
            fldName: i,
            fldValue: ''
          })
        })
      },
      handleOK() {
        this.getData()
        this.form.validateFields((err, values) => {
          const arr = this.subData
          arr.forEach(i => {
            if (i.fldName === 'exceed') {
              i.fldValue = values.exceed
            }
            if (i.fldName === 'installedWh') {
              i.fldValue = values.installedWh
            }
            if (i.fldName === 'appointWh') {
              i.fldValue = values.appointWh
            }
            if (i.fldName === 'separator') {
              i.fldValue = values.separator
            }
            if (i.fldName === 'inputQty') {
              i.fldValue = this.inputQty
            }
            if (i.fldName === 'boxExistence') {
              i.fldValue = this.boxExistence
            }

            if (i.fldName === 'exceedProportion') {
              i.fldValue = values.exceedProportion
            }
            if (i.fldName === 'source') {
              i.fldValue = this.source
            }
            if (i.fldName === 'apart') {
              i.fldValue = this.apart
            }

            if (i.fldName === 'batch') {
              i.fldValue = values.batch
            }
            if (i.fldName === 'examine') {
              i.fldValue = values.examine
            }
            if (i.fldName === 'dz') {
              i.fldValue = this.dz
            }
            if (i.fldName === 'fcim') {
              i.fldValue = this.fcim
            }
            if (i.fldName === 'ignoreUp') {
              i.fldValue = this.ignoreUp
            }
            if (i.fldName === 'box') {
              i.fldValue = this.box
            }
            if (i.fldName === 'scanBatNo') {
              i.fldValue = this.scanBatNo
            }
            if (i.fldName === 'trunc') {
              i.fldValue = values.trunc
            }
            if (i.fldName === 'scanning') {
              i.fldValue = values.scanning
            }
            if (i.fldName === 'ckChk') {
              i.fldValue = values.ckChk
            }
            if (i.fldName === 'isSplit') {
              i.fldValue = this.isSplit
            }
            if (i.fldName === 'sourceDep') {
              i.fldValue = this.sourceDep
            }
          })

          if (!err) {
            addBarPswdProp(this.subData)
              .then(() => {
                this.$message.success(this.$t('public.success'))
              })
              .catch(() => {
                this.$message.error(this.$t('public.error'))
              })
          }
        })
      },
      handleCancel() {
        this.$emit('Cancel')
        this.subData = []
      }
    }
  }
</script>