<template>
  <div>
    <a-form
      :form="form"
      ref="form"
    >

      <span style="font-size:1rem">末道工序:</span>
      <a-row>
        <a-col
          :xs="24"
          :sm="24"
          :md="12"
          :lg="12"
          :xl="12"
        >
          <a-form-item
            label="末道工序自动缴存"
            v-bind="formItemLayout"
          >
            <a-switch
              defaultChecked
              v-model="parma.storage"
            />
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">报工管控:</span>
      <a-row>
        <a-col
          :xs="24"
          :sm="24"
          :md="12"
          :lg="12"
          :xl="12"
        >
          <a-form-item
            label="报工时间限制(天)"
            v-bind="formItemLayout"
          >
            <a-input-number
              v-model="parma.controlyDay"
              style="width:100%"
              size="small"
              id="inputNumber"
              :min="0"
            />
          </a-form-item>

        </a-col>
      </a-row>
    </a-form>
    <a-row :gutter="16">
      <a-col
        class="gutter-row"
        :span="12"
        style="text-align:right"
      >
        <a-button
          id="ok"
          type="primary"
          @click="handleOK"
        >{{ $t('public.save') }}{{ obj.subname }}</a-button>
      </a-col>
      <a-col
        class="gutter-row"
        :span="12"
        style="text-align:left"
      >
        <a-button
          id="cancel"
          @click="handleCancel"
        >{{ $t('public.cancel') }}</a-button>
      </a-col>
    </a-row>
  </div>
</template>
<script>

import { getWh, addBarPswdProp, getBarPswdProps } from '@/api/barcode/propertySettings'

export default {
  props: {
    obj: {
      required: true,
      type: Object
    },
    cid: {
      required: true,
      type: String
    },
    row: {
      required: true,
      type: Object
    }
  },
  data () {
    return {
      title: '',
      visible: true,
      confirmLoading: true,
      form: this.$form.createForm(this),
      whlist: [],
      userList: [],
      whs: [],
      inputQty: true,
      parma: {
        storage: false,
        controlyDay: '',
      },
      subData: [],
      onSubmitData: {
        // 保存属性对象
        compno: '',
        roleno: '',
        typeId: '6',
        pgm: '',
        fldName: '',
        fldValue: ''
      },
      formItemLayout: {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 7 },
          md: { span: 8 },
          lg: { span: 14 }
        },
        wrapperCol: {
          xs: { span: 5 },
          sm: { span: 16 },
          md: { span: 17 },
          lg: { span: 10 }
        }
      }

    }
  },
  created () {
    if (this.obj.subname) {
      this.getpop()
      getWh(
        Object.assign({
          current: 1,
          size: 10
        })
      ).then(response => {
        this.whlist = response.data.records
      })
    }
  },
  methods: {
    getpop () {
      const obj = {
        compno: this.row.compno,
        roleno: this.row.roleno,
        pgm: this.cid
      }
      getBarPswdProps(obj).then(res => {
  
        this.subData = res.data
        const arr = this.subData
        if (arr.length > 0) {
          setTimeout(() => {
            this.parma.storage = JSON.parse(arr[23].fldValue)
          }, 1)
        }
      })
    },
    getData () {
      this.subData = []
      const fidArr = this.obj.fidArr
      fidArr.forEach(i => {
        this.subData.push(this.onSubmitData = {
          compno: this.row.compno,
          roleno: this.row.roleno,
          typeId: '6',
          pgm: this.cid,
          fldName: i,
          fldValue: ''
        })
      })
    },
    handleOK () {
      this.getData()
      this.form.validateFields((err, values) => {
        const arr = this.subData
        arr.forEach(i => {
          if (i.fldName === 'storage') {
            i.fldValue = this.parma.storage
          }
          if (i.fldName === 'controlyDay') {
            i.fldValue = this.parma.controlyDay
          }
        })
        if (!err) {
          addBarPswdProp(this.subData)
            .then(() => {
              this.$message.success(this.$t('public.success'))
            })
            .catch(() => {
              this.$message.error(this.$t('public.error'))
            })
        }
      })
    },
    handleCancel () {
      this.$emit('Cancel')
      this.subData = []
    }
  }
}
</script>
