<template>
  <div>
    <a-form :form="form" ref="form">
      <span style="font-size:1rem">{{ $t('propertySettings.ware') }}:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item :label="$t('propertySettings.ware')" v-bind="formItemLayout">
            <a-select style="width:100%"
              v-decorator="['installedWh', { rules: [{ message:$t('propertySettings.ware') }] }]" :getPopupContainer=" triggerNode => {
                return triggerNode.parentNode || document.body;
              }">
              <a-select-option value="1">来源单仓库</a-select-option>
              <a-select-option value="2">扫描仓库</a-select-option>
              <a-select-option value="3">条码仓库</a-select-option>
              <a-select-option value="4">预设仓库</a-select-option>
              <a-select-option value="5">指定仓库</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item :label="$t('propertySettings.waret')" v-bind="formItemLayout">
            <a-select show-search allowClear @search="searchWh" @focus="getWhs()" :filter-option="false"
              v-decorator="['appointWh',{rules: [{ message: $t('propertySettings.waret') }],}]">
              <a-spin v-if="fetching" slot="notFoundContent" size="small" />
              <a-select-option v-for="(i, index) in whlist" :key="index" :value="i.wh">{{ i.name }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="自动获取明细" v-bind="formItemLayout">
            <a-switch v-model="automaticList" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="合格量生成进货单" v-bind="formItemLayout">
            <a-switch v-model="automaticPc" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="自选单据" v-bind="formItemLayout">
            <a-switch v-model="selectData" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="连续扫描" v-bind="formItemLayout">
            <a-switch v-model="continuousScanning" />
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">{{ $t('propertySettings.Quantity') }}:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item :label="$t('propertySettings.Quantitys')" v-bind="formItemLayout">
            <a-switch v-model="inputQty" />
          </a-form-item>
        </a-col>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="是否可拆码" v-bind="formItemLayout">
            <a-switch v-model="boxExistence" />
          </a-form-item>
        </a-col>

      </a-row>
      <span style="font-size:1rem">{{ $t('propertySettings.detection') }}:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item :label="$t('propertySettings.detection')" v-bind="formItemLayout">
            <a-select style="width:100%" allowClear
              v-decorator="['exceed', { rules: [{ message:$t('propertySettings.detection') }] }]" :getPopupContainer=" triggerNode => {
                return triggerNode.parentNode || document.body;
              }">
              <a-select-option value="1">管制</a-select-option>
              <a-select-option value="2">提示</a-select-option>
              <a-select-option value="3">不管制</a-select-option>
              <a-select-option value="4">允许在超交比例内</a-select-option>
              <a-select-option value="5">自动扣除多余数量</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item :label="$t('propertySettings.Proportion')" v-bind="formItemLayout">
            <a-input-number :min="0" v-decorator="['exceedProportion', { rules: [] }]" />
          </a-form-item>
        </a-col>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="拆分弹出" v-bind="formItemLayout">
            <a-switch v-model="apart" />
          </a-form-item>
        </a-col>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item :label="$t('propertySettings.basic')" v-bind="formItemLayout">
            <a-switch v-model="source" />
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">{{ $t('propertySettings.Batch') }}:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item :label="$t('propertySettings.serialNumber')" v-bind="formItemLayout">
            <a-select style="width:100%" allowClear
              v-decorator="['batch', { rules: [{ message:$t('propertySettings.Batch') }] }]" :getPopupContainer=" triggerNode => {
                return triggerNode.parentNode || document.body;
              }">
              <a-select-option value="1">按货品基础资料</a-select-option>
              <a-select-option value="2">不管制</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">扫描设置:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="批号匹配" v-bind="formItemLayout">
            <a-switch v-model="scanBatNo" />
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">{{ $t('propertySettings.Bill') }}:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item :label="$t('propertySettings.Bill')" v-bind="formItemLayout">
            <a-select style="width:100%" allowClear
              v-decorator="['examine', { rules: [{ message:$t('propertySettings.Bill') }] }]" :getPopupContainer=" triggerNode => {
                return triggerNode.parentNode || document.body;
              }">
              <a-select-option value="1">不审核</a-select-option>
              <a-select-option value="2">审核并自动提交</a-select-option>
              <a-select-option value="3">审核不自动提交</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">FCIM同步:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="FCIM同步" v-bind="formItemLayout">
            <a-switch v-model="fcim" />
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">流水码:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="流水码允许拆分" v-bind="formItemLayout">
            <a-switch v-model="isSplit" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <a-row :gutter="16">
      <a-col class="gutter-row" :span="12" style="text-align:right">
        <a-button id="ok" type="primary" @click="handleOK">{{ $t('public.save') }}{{ obj.subname }}</a-button>
      </a-col>
      <a-col class="gutter-row" :span="12" style="text-align:left">
        <a-button id="cancel" @click="handleCancel">{{ $t('public.cancel') }}</a-button>
      </a-col>
    </a-row>
  </div>
</template>
<script>

  import { getWh, addBarPswdProp, getBarPswdProps } from '@/api/barcode/propertySettings'

  export default {
    props: {
      obj: {
        required: true,
        type: Object
      },
      cid: {
        required: true,
        type: String
      },
      row: {
        required: true,
        type: Object
      }
    },
    data() {
      return {
        title: '',
        visible: true,
        confirmLoading: true,
        source: false,
        apart: false,
        inputQty: false,
        boxExistence: false,
        fcim: false,
        fetching: false,
        scanBatNo: false,
        automaticList: false,
        automaticPc: false,
        selectData: false,
        continuousScanning: false,
        form: this.$form.createForm(this),
        whlist: [],
        userList: [],
        subData: [],
        onSubmitData: {
          // 保存属性对象
          compno: '',
          roleno: '',
          typeId: '6',
          pgm: '',
          fldName: '',
          fldValue: ''
        },
        formItemLayout: {
          labelCol: {
            xs: { span: 24 },
            sm: { span: 7 },
            md: { span: 8 },
            lg: { span: 8 }
          },
          wrapperCol: {
            xs: { span: 5 },
            sm: { span: 16 },
            md: { span: 17 },
            lg: { span: 16 }
          }
        },
        isSplit: false,
      }
    },
    created() {
      if (this.obj.subname) {
        this.getpop()
      }
    },
    methods: {
      getpop() {
        const obj = {
          compno: this.row.compno,
          roleno: this.row.roleno,
          pgm: this.cid
        }
        getBarPswdProps(obj).then(res => {
          this.subData = res.data
          const arr = this.subData
          if (arr.length > 0) {
            setTimeout(() => {
              function tran(name) {
                let ind
                arr.forEach((e, index) => {
                  if (e.fldName === name) {
                    return ind = index
                  }
                })
                return ind
              }
              this.source = JSON.parse(tran('source') === undefined || arr[tran('source')].fldValue === '' || arr[tran('source')].fldValue === null ? this.source : arr[tran('source')].fldValue)
              this.apart = JSON.parse(tran('apart') === undefined || arr[tran('apart')].fldValue === '' || arr[tran('apart')].fldValue === null ? this.apart : arr[tran('apart')].fldValue)

              this.inputQty = JSON.parse(tran('inputQty') === undefined ? this.inputQty : arr[tran('inputQty')].fldValue)
              this.boxExistence = JSON.parse(tran('boxExistence') === undefined ? this.boxExistence : arr[tran('boxExistence')].fldValue)
              this.isSplit = JSON.parse(tran('isSplit') === undefined || arr[tran('isSplit')].fldValue === '' || arr[tran('isSplit')].fldValue === null ? this.isSplit : arr[tran('isSplit')].fldValue)

              this.fcim = JSON.parse(tran('fcim') === undefined || arr[tran('fcim')].fldValue === '' ? this.fcim : arr[tran('fcim')].fldValue)
              this.scanBatNo = JSON.parse(tran('scanBatNo') === undefined || arr[tran('scanBatNo')].fldValue === '' || arr[tran('scanBatNo')].fldValue === null ? this.scanBatNo : arr[tran('scanBatNo')].fldValue)
              this.automaticList = JSON.parse(tran('automaticList') === undefined || arr[tran('automaticList')].fldValue === '' || arr[tran('automaticList')].fldValue === null ? this.automaticList : arr[tran('automaticList')].fldValue)
              this.automaticPc = JSON.parse(tran('automaticPc') === undefined || arr[tran('automaticPc')].fldValue === '' || arr[tran('automaticPc')].fldValue === null ? this.automaticPc : arr[tran('automaticPc')].fldValue)
              this.selectData = JSON.parse(tran('selectData') === undefined || arr[tran('selectData')].fldValue === '' || arr[tran('selectData')].fldValue === null ? this.selectData : arr[tran('selectData')].fldValue)
              this.continuousScanning = JSON.parse(tran('continuousScanning') === undefined || arr[tran('continuousScanning')].fldValue === '' || arr[tran('continuousScanning')].fldValue === null ? this.continuousScanning : arr[tran('continuousScanning')].fldValue)
              this.form.setFieldsValue({
                exceed: tran('exceed') === undefined ? '' : arr[tran('exceed')].fldValue,
                installedWh: tran('installedWh') === undefined ? '' : arr[tran('installedWh')].fldValue,
                appointWh: tran('appointWh') === undefined ? '' : arr[tran('appointWh')].fldValue,
                exceedProportion: tran('exceedProportion') === undefined ? '' : arr[tran('exceedProportion')].fldValue,
                batch: tran('batch') === undefined ? '' : arr[tran('batch')].fldValue,
                examine: tran('examine') === undefined ? '' : arr[tran('examine')].fldValue
              })
              if (tran('appointWh') !== undefined && tran('appointWh') !== null && tran('appointWh') !== '') this.getWhs(1, arr[tran('appointWh')].fldValue)
              else
                this.getWhs()
            }, 1)
          }
        })
      },
      getWhs(page = 1, queryWhs = '') {
        this.fetching = true
        this.whlist = []
        getWh(
          Object.assign({
            current: page,
            size: 10,
            wh: queryWhs,
            name: queryWhs,
            rank: '2'
          })
        ).then(response => {
          this.whlist = response.data.records
          this.fetching = false
        })
          .catch(() => {
            this.fetching = false
          })
      },
      searchWh(value) {
        this.getWhs(1, value)
      },
      getData() {
        this.subData = []
        const fidArr = this.obj.fidArr
        fidArr.forEach(i => {
          this.subData.push(this.onSubmitData = {
            compno: this.row.compno,
            roleno: this.row.roleno,
            typeId: '6',
            pgm: this.cid,
            fldName: i,
            fldValue: ''
          })
        })
      },
      handleOK() {
        this.getData()
        this.form.validateFields((err, values) => {
          const arr = this.subData
          arr.forEach(i => {
            if (i.fldName === 'exceed') {
              i.fldValue = values.exceed
            }
            if (i.fldName === 'installedWh') {
              i.fldValue = values.installedWh
            }
            if (i.fldName === 'appointWh') {
              i.fldValue = values.appointWh
            }
            if (i.fldName === 'inputQty') {
              i.fldValue = this.inputQty
            }
            if (i.fldName === 'boxExistence') {
              i.fldValue = this.boxExistence
            }
            if (i.fldName === 'exceedProportion') {
              i.fldValue = values.exceedProportion
            }
            if (i.fldName === 'source') {
              i.fldValue = this.source
            }
            if (i.fldName === 'apart') {
              i.fldValue = this.apart
            }
            if (i.fldName === 'batch') {
              i.fldValue = values.batch
            }
            if (i.fldName === 'examine') {
              i.fldValue = values.examine
            }
            if (i.fldName === 'fcim') {
              i.fldValue = this.fcim
            }
            if (i.fldName === 'scanBatNo') {
              i.fldValue = this.scanBatNo
            }
            if (i.fldName === 'automaticList') {
              i.fldValue = this.automaticList
            }
            if (i.fldName === 'automaticPc') {
              i.fldValue = this.automaticPc
            }
            if (i.fldName === 'selectData') {
              i.fldValue = this.selectData
            }
            if (i.fldName === 'continuousScanning') {
              i.fldValue = this.continuousScanning
            }
            if (i.fldName === 'isSplit') {
              i.fldValue = this.isSplit
            }
          })

          if (!err) {
            addBarPswdProp(this.subData)
              .then(() => {
                this.$message.success(this.$t('public.success'))
              })
              .catch(() => {
                this.$message.error(this.$t('public.error'))
              })
          }
        })
      },
      handleCancel() {
        this.$emit('Cancel')
        this.subData = []
      }
    }
  }
</script>