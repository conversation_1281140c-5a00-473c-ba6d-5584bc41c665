<template>
  <div>
    <a-form
      :form="form"
      ref="form"
    >
      <span style="font-size:1rem">FCIM同步:</span>
      <a-row>
        <a-col
          :xs="24"
          :sm="24"
          :md="12"
          :lg="12"
          :xl="12"
        >
          <a-form-item
            label="FCIM同步"
            v-bind="formItemLayout"
          >
            <a-switch v-model="fcim" />
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">FCIM间隔时间:</span>
      <a-row>
        <a-col
          :xs="24"
          :sm="24"
          :md="12"
          :lg="12"
          :xl="12"
        >
          <a-form-item
            label="间隔时间(分钟)"
            v-bind="formItemLayout"
          >
            <a-input-number
              :min="0"
              v-decorator="['fcimTime', { rules: [] }]"
              style="width:100%"
            />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <a-row :gutter="16">
      <a-col
        class="gutter-row"
        :span="12"
        style="text-align:right"
      >
        <a-button
          id="ok"
          type="primary"
          @click="handleOK"
        >{{ $t('public.save') }}{{ obj.subname }}</a-button>
      </a-col>
      <a-col
        class="gutter-row"
        :span="12"
        style="text-align:left"
      >
        <a-button
          id="cancel"
          @click="handleCancel"
        >{{ $t('public.cancel') }}</a-button>
      </a-col>
    </a-row>
  </div>
</template>
<script>

import { querySalm, deptListPage, getWh, addBarPswdProp, getBarPswdProps } from '@/api/barcode/propertySettings'

export default {
  props: {
    obj: {
      required: true,
      type: Object
    },
    cid: {
      required: true,
      type: String
    },
    row: {
      required: true,
      type: Object
    }
  },
  data () {
    return {
      title: '',
      visible: true,
      confirmLoading: true,
      // source: true,
      fcim: false,
      fcimTime: '',
      form: this.$form.createForm(this),
      whlist: [],
      subData: [],
      onSubmitData: {
        // 保存属性对象
        compno: '',
        roleno: '',
        typeId: '6',
        pgm: '',
        fldName: '',
        fldValue: ''
      },
      userList: [],
      deptsList: [],
      whs: [],
      formItemLayout: {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 7 },
          md: { span: 8 },
          lg: { span: 8 }
        },
        wrapperCol: {
          xs: { span: 5 },
          sm: { span: 16 },
          md: { span: 17 },
          lg: { span: 16 }
        }
      }
    }
  },
  created () {
    if (this.obj.subname) {
      this.getpop()
      this.user()
      this.dep()
      this.wh()
    }
  },
  methods: {
    getpop () {
      const obj = {
        compno: this.row.compno,
        roleno: this.row.roleno,
        pgm: this.cid
      }
      getBarPswdProps(obj).then(res => {
        this.subData = res.data
        const arr = this.subData
        if (arr.length > 0) {
          setTimeout(() => {
            function tran (name) {
              let ind
              arr.forEach((e, index) => {
                if (e.fldName === name) {
                  return ind = index
                }
              })
              return ind
            }
            this.fcim = JSON.parse(tran('fcim') === undefined ? this.fcim : arr[tran('fcim')].fldValue)
            this.form.setFieldsValue({
              fcimTime: tran('fcimTime') === undefined ? '' : Number(arr[tran('fcimTime')].fldValue),
            })
          }, 1)
        }
      })
    },
    getData () {
      this.subData = []
      const fidArr = this.obj.fidArr
      fidArr.forEach(i => {
        this.subData.push(this.onSubmitData = {
          compno: this.row.compno,
          roleno: this.row.roleno,
          typeId: '6',
          pgm: this.cid,
          fldName: i,
          fldValue: ''
        })
      })
    },
    handleOK () {
      this.getData()
      this.form.validateFields((err, values) => {
        const arr = this.subData
        arr.forEach(i => {
          if (i.fldName === 'fcim') {
            i.fldValue = this.fcim
          }
          if (i.fldName === 'fcimTime') {
            i.fldValue = values.fcimTime
          }
        })
        if (!err) {
          addBarPswdProp(this.subData)
            .then(() => {
              this.$message.success(this.$t('public.success'))
            })
            .catch(() => {
              this.$message.error(this.$t('public.error'))
            })
        }
      })
    },
    user () {
      querySalm(Object.assign({
        current: 1,
        size: 10
      })).then(res => {
        this.userList = res.data.records
      })
    },
    dep () {
      deptListPage(
        Object.assign({
          current: 1,
          size: 10
        })
      ).then(response => {
        this.deptsList = response.data.records
      })
    },
    wh () {
      getWh(
        Object.assign({
          current: 1,
          size: 10
        })
      ).then(response => {
        this.whs = response.data.records
      })
    },
    handleCancel () {
      this.$emit('Cancel')
      this.subData = []
    }
  }
}
</script>
