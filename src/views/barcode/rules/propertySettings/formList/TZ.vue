<template>
  <div>
    <a-form :form="form" ref="form">
      <span style="font-size:1rem">{{ $t('propertySettings.Batch') }}:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item :label="$t('propertySettings.serialNumber')" v-bind="formItemLayout">
            <a-select style="width:100%" allowClear
              v-decorator="['batch', { rules: [{ message:$t('propertySettings.Batch') }] }]" :getPopupContainer=" triggerNode => {
                return triggerNode.parentNode || document.body;
              }">
              <a-select-option value="1">按货品基础资料</a-select-option>
              <a-select-option value="2">不管制</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">条码设定:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="是否可拆码" v-bind="formItemLayout">
            <a-switch v-model="boxExistence" />
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">扫描设置:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="批号匹配" v-bind="formItemLayout">
            <a-switch v-model="scanBatNo" />
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">{{ $t('propertySettings.detection') }}:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item :label="$t('propertySettings.detection')" v-bind="formItemLayout">
            <a-select style="width:100%" allowClear
              v-decorator="['exceed', { rules: [{ message:$t('propertySettings.detection') }] }]" :getPopupContainer=" triggerNode => {
                return triggerNode.parentNode || document.body;
              }">
              <a-select-option value="1">管制</a-select-option>
              <a-select-option value="2">提示</a-select-option>
              <a-select-option value="3">不管制</a-select-option>
              <a-select-option value="4">允许在超交比例内</a-select-option>
              <a-select-option value="5">自动扣除多余数量</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item :label="$t('propertySettings.Proportion')" v-bind="formItemLayout">
            <a-input-number :min="0" v-decorator="['exceedProportion', { rules: [] }]" />
          </a-form-item>
        </a-col>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="拆分弹出" v-bind="formItemLayout">
            <a-switch v-model="apart" />
          </a-form-item>
        </a-col>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item :label="$t('propertySettings.basic')" v-bind="formItemLayout">
            <a-switch v-model="source" />
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">主副数量推算:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="按单重推算" v-bind="formItemLayout">
            <a-switch v-model="dz" />
          </a-form-item>
          <a-form-item label="主数量推算副数量" v-bind="formItemLayout">
            <a-switch v-model="qtyOrqty1" />
          </a-form-item>
          <a-form-item label="副数量推算主数量" v-bind="formItemLayout">
            <a-switch v-model="qty1Orqty" />
          </a-form-item>
          <a-form-item label="作业人员默认取登录用户" v-bind="formItemLayout">
            <a-switch v-model="ygNo" />
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">{{ $t('propertySettings.trunc') }}:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item :label="$t('propertySettings.trunc')" v-bind="formItemLayout">
            <a-select style="width:100%" allowClear
              v-decorator="['trunc', { rules: [{ message:$t('propertySettings.trunc') }] }]" :getPopupContainer=" triggerNode => {
                return triggerNode.parentNode || document.body;
              }">
              <a-select-option value="1">向上取整</a-select-option>
              <a-select-option value="2">向下取取整</a-select-option>
              <a-select-option value="3">不取整</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">{{ $t('propertySettings.Exception') }}:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="产生异常通知单处理方式" v-bind="formItemLayout">
            <a-select style="width:100%" allowClear
              v-decorator="['abnormal', { rules: [{ message:$t('propertySettings.way') }] }]" :getPopupContainer=" triggerNode => {
                return triggerNode.parentNode || document.body;
              }">
              <a-select-option value="1">强制缴库</a-select-option>
              <a-select-option value="2">报废处理</a-select-option>
              <a-select-option value="3">重开通知单</a-select-option>
              <a-select-option value="4">重开制令单</a-select-option>
              <a-select-option value="5">PASS</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item :label="$t('propertySettings.payment')" v-bind="formItemLayout">
            <a-switch v-model="force" />
          </a-form-item>
        </a-col>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item :label="$t('propertySettings.abnormal')" v-bind="formItemLayout">
            <a-select show-search allowClear @search="searchWh" @focus="getWhs()" :filter-option="false"
              v-decorator="['abnormalWh',{rules: [{ message: $t('propertySettings.abnormal') }],}]">
              <a-spin v-if="fetching" slot="notFoundContent" size="small" />
              <a-select-option v-for="(i, index) in whlist" :key="index" :value="i.wh">{{ i.name }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">末道工序:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="末道工序自动缴存" v-bind="formItemLayout">
            <a-switch @change="switchinput" v-model="storage" />
          </a-form-item>
        </a-col>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="末道工序自动送检" v-bind="formItemLayout">
            <a-switch @change="switchinputtwo" v-model="saveT5" />
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">报工管控:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="报工时间限制(天)" v-bind="formItemLayout">
            <a-input-number style="width: 100%" v-decorator="['controlyDay', { rules: [] }]" id="inputNumber"
              :min="0" />
          </a-form-item>

        </a-col>
      </a-row>
      <span style="font-size:1rem">流水码:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="流水码允许拆分" v-bind="formItemLayout">
            <a-switch v-model="isSplit" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <a-row :gutter="16">
      <a-col class="gutter-row" :span="12" style="text-align:right">
        <a-button id="ok" type="primary" @click="handleOK">{{ $t('public.save') }}{{ obj.subname }}</a-button>
      </a-col>
      <a-col class="gutter-row" :span="12" style="text-align:left">
        <a-button id="cancel" @click="handleCancel">{{ $t('public.cancel') }}</a-button>
      </a-col>
    </a-row>
  </div>
</template>
<script>

  import { getWh, addBarPswdProp, getBarPswdProps } from '@/api/barcode/propertySettings'

  export default {
    props: {
      obj: {
        required: true,
        type: Object
      },
      cid: {
        required: true,
        type: String
      },
      row: {
        required: true,
        type: Object
      }
    },
    data() {
      return {
        isSplit: false,
        title: '',
        visible: true,
        confirmLoading: true,
        form: this.$form.createForm(this),
        whlist: [],
        userList: [],
        force: false,
        source: false,
        inputQty: false,
        apart: false,
        scanBatNo: false,
        boxExistence: false,
        dz: false,
        qtyOrqty1: false,
        qty1Orqty: false,
        ygNo: false,
        storage: false,
        saveT5: false,
        fetching: false,
        subData: [],
        onSubmitData: {
          // 保存属性对象
          compno: '',
          roleno: '',
          typeId: '6',
          pgm: '',
          fldName: '',
          fldValue: ''
        },
        formItemLayout: {
          labelCol: {
            xs: { span: 24 },
            sm: { span: 7 },
            md: { span: 8 },
            lg: { span: 8 }
          },
          wrapperCol: {
            xs: { span: 5 },
            sm: { span: 16 },
            md: { span: 17 },
            lg: { span: 16 }
          }
        }

      }
    },
    created() {
      if (this.obj.subname) {
        this.getpop()
      }
    },
    methods: {
      switchinputtwo(val) {
        if (val) {
          this.storage = false
        } else {
          this.storage = true
        }
      },
      switchinput(val) {
        if (val) {
          this.saveT5 = false
        } else {
          this.saveT5 = true
        }
      },
      getpop() {
        const obj = {
          compno: this.row.compno,
          roleno: this.row.roleno,
          pgm: this.cid
        }
        getBarPswdProps(obj).then(res => {
          this.subData = res.data
          const arr = this.subData
          if (arr.length > 0) {
            setTimeout(() => {
              function tran(name) {
                let ind
                arr.forEach((e, index) => {
                  if (e.fldName === name) {
                    return ind = index
                  }
                })
                return ind
              }
              this.scanBatNo = JSON.parse(tran('scanBatNo') === undefined ? this.scanBatNo : arr[tran('scanBatNo')].fldValue)
              this.source = JSON.parse(tran('source') === undefined ? this.source : arr[tran('source')].fldValue)
              this.apart = JSON.parse(tran('apart') === undefined ? this.apart : arr[tran('apart')].fldValue)
              this.boxExistence = JSON.parse(tran('boxExistence') === undefined ? this.boxExistence : arr[tran('boxExistence')].fldValue)
              this.isSplit = JSON.parse(tran('isSplit') === undefined || arr[tran('isSplit')].fldValue === '' || arr[tran('isSplit')].fldValue === null ? this.isSplit : arr[tran('isSplit')].fldValue)

              this.inputQty = JSON.parse(tran('inputQty') === undefined ? this.inputQty : arr[tran('inputQty')].fldValue)
              this.force = JSON.parse(tran('force') === undefined ? this.force : arr[tran('force')].fldValue)
              this.dz = JSON.parse(tran('dz') === undefined ? this.dz : arr[tran('dz')].fldValue)
              this.qtyOrqty1 = JSON.parse(tran('qtyOrqty1') === undefined ? this.qtyOrqty1 : arr[tran('qtyOrqty1')].fldValue)
              this.qty1Orqty = JSON.parse(tran('qty1Orqty') === undefined ? this.qty1Orqty : arr[tran('qty1Orqty')].fldValue)
              this.ygNo = JSON.parse(tran('ygNo') === undefined ? this.ygNo : arr[tran('ygNo')].fldValue)
              this.storage = JSON.parse(tran('storage') === undefined ? this.storage : arr[tran('storage')].fldValue)
              this.saveT5 = JSON.parse(tran('saveT5') === undefined ? this.saveT5 : arr[tran('saveT5')].fldValue)

              this.form.setFieldsValue({
                abnormal: tran('abnormal') === undefined ? '' : arr[tran('abnormal')].fldValue,
                abnormalWh: tran('abnormalWh') === undefined ? '' : arr[tran('abnormalWh')].fldValue,
                exceed: tran('exceed') === undefined ? '' : arr[tran('exceed')].fldValue,
                exceedProportion: tran('exceedProportion') === undefined ? '' : arr[tran('exceedProportion')].fldValue,
                trunc: tran('trunc') === undefined ? '' : arr[tran('trunc')].fldValue,
                controlyDay: tran('controlyDay') === undefined ? '' : arr[tran('controlyDay')].fldValue,
                batch: tran('batch') === undefined ? '' : arr[tran('batch')].fldValue,
              })
              if (tran('abnormalWh') !== undefined && tran('abnormalWh') !== null && tran('abnormalWh') !== '') this.getWhs(1, arr[tran('abnormalWh')].fldValue)
              else
                this.getWhs()
            }, 1)
          }
        })
      },
      getWhs(page = 1, queryWhs = '') {
        this.fetching = true
        this.whlist = []
        getWh(
          Object.assign({
            current: page,
            size: 10,
            wh: queryWhs,
            name: queryWhs,
            rank: '2'
          })
        ).then(response => {
          this.whlist = response.data.records
          this.fetching = false
        })
          .catch(() => {
            this.fetching = false
          })
      },
      searchWh(value) {
        this.getWhs(1, value)
      },
      getData() {
        this.subData = []
        const fidArr = this.obj.fidArr
        fidArr.forEach(i => {
          this.subData.push(this.onSubmitData = {
            compno: this.row.compno,
            roleno: this.row.roleno,
            typeId: '6',
            pgm: this.cid,
            fldName: i,
            fldValue: ''
          })
        })
      },
      handleOK() {
        this.getData()
        this.form.validateFields((err, values) => {
          const arr = this.subData
          arr.forEach(i => {
            if (i.fldName === 'scanBatNo') {
              i.fldValue = this.scanBatNo
            }
            if (i.fldName === 'boxExistence') {
              i.fldValue = this.boxExistence
            }

            if (i.fldName === 'exceed') {
              i.fldValue = values.exceed
            }
            if (i.fldName === 'abnormal') {
              i.fldValue = values.abnormal
            }
            if (i.fldName === 'abnormalWh') {
              i.fldValue = values.abnormalWh
            }
            if (i.fldName === 'inputQty') {
              i.fldValue = this.inputQty
            }
            if (i.fldName === 'exceedProportion') {
              i.fldValue = values.exceedProportion
            }
            if (i.fldName === 'force') {
              i.fldValue = this.force
            }
            if (i.fldName === 'source') {
              i.fldValue = this.source
            }
            if (i.fldName === 'apart') {
              i.fldValue = this.apart
            }

            if (i.fldName === 'dz') {
              i.fldValue = this.dz
            }
            if (i.fldName === 'qtyOrqty1') {
              i.fldValue = this.qtyOrqty1
            }
            if (i.fldName === 'qty1Orqty') {
              i.fldValue = this.qty1Orqty
            }
            if (i.fldName === 'ygNo') {
              i.fldValue = this.ygNo
            }
            if (i.fldName === 'storage') {
              i.fldValue = this.storage
            }
            if (i.fldName === 'saveT5') {
              i.fldValue = this.saveT5
            }
            if (i.fldName === 'trunc') {
              i.fldValue = values.trunc
            }
            if (i.fldName === 'controlyDay') {
              i.fldValue = values.controlyDay
            }
            if (i.fldName === 'batch') {
              i.fldValue = values.batch
            }
            if (i.fldName === 'isSplit') {
              i.fldValue = this.isSplit
            }
          })

          if (!err) {
            addBarPswdProp(this.subData)
              .then(() => {
                this.$message.success(this.$t('public.success'))
              })
              .catch(() => {
                this.$message.error(this.$t('public.error'))
              })
          }
        })
      },
      handleCancel() {
        this.$emit('Cancel')
        this.subData = []
      }
    }
  }
</script>