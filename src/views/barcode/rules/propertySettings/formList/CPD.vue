<template>
  <div>
    <a-form :form="form" ref="form">
      <!-- <span style="font-size:1rem">{{ $t('propertySettings.ware') }}</span> -->
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item :label="$t('propertySettings.ware')" v-bind="formItemLayout">
            <a-select style="width:100%" allowClear
              v-decorator="['installedWh', { rules: [{ message:$t('propertySettings.ware') }] }]" :getPopupContainer=" triggerNode => {
                return triggerNode.parentNode || document.body;
              }">
              <!-- <a-select-option value="1">来源单仓库</a-select-option> -->
              <a-select-option value="2">扫描仓库</a-select-option>
              <a-select-option value="3">条码仓库</a-select-option>
              <a-select-option value="4">预设仓库</a-select-option>
              <a-select-option value="5">指定仓库</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <!-- <span style="font-size: 1rem">{{ $t('propertySettings.Quantity') }}:</span> -->
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="不合并" v-bind="formItemLayout">
            <a-switch v-model="add" />
          </a-form-item>
          <a-form-item label="确认数量" v-bind="formItemLayout">
            <a-switch v-model="confirmQty" @click.native="switchUpdate2()" />
          </a-form-item>
          <a-form-item label="批量盘点" v-bind="formItemLayout">
            <a-switch v-model="batchInventory" @click.native="switchUpdate1()" />
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">条码设定</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="是否可拆码" v-bind="formItemLayout">
            <a-switch v-model="boxExistence" />
          </a-form-item>
        </a-col>
      </a-row>

    </a-form>
    <a-row :gutter="16">
      <a-col class="gutter-row" :span="12" style="text-align: right">
        <a-button id="ok" type="primary" @click="handleOK">{{ $t('public.save') }}{{ obj.subname }}</a-button>
      </a-col>
      <a-col class="gutter-row" :span="12" style="text-align: left">
        <a-button id="cancel" @click="handleCancel">{{ $t('public.cancel') }}</a-button>
      </a-col>
    </a-row>
  </div>
</template>
<script>
  import { querySalm, deptListPage, getWh, addBarPswdProp, getBarPswdProps } from '@/api/barcode/propertySettings'

  export default {
    props: {
      obj: {
        required: true,
        type: Object,
      },
      cid: {
        required: true,
        type: String,
      },
      row: {
        required: true,
        type: Object,
      },
    },
    data() {
      return {
        title: '',
        boxExistence: false,
        visible: true,
        add: false,
        confirmQty: false,
        batchInventory: false,
        form: this.$form.createForm(this),
        confirmLoading: true,
        whlist: [],
        subData: [],
        onSubmitData: {
          // 保存属性对象
          compno: '',
          roleno: '',
          typeId: '6',
          pgm: '',
          fldName: '',
          fldValue: '',
        },
        userList: [],
        deptsList: [],
        whs: [],
        formItemLayout: {
          labelCol: {
            xs: { span: 24 },
            sm: { span: 7 },
            md: { span: 8 },
            lg: { span: 8 },
          },
          wrapperCol: {
            xs: { span: 5 },
            sm: { span: 16 },
            md: { span: 17 },
            lg: { span: 16 },
          },
        },
      }
    },
    created() {
      if (this.obj.subname) {
        this.getpop()
        this.user()
        this.dep()
        this.wh()
      }
    },
    methods: {
      switchUpdate1() {
        if (this.batchInventory) {
          this.confirmQty = false
        }
      },
      switchUpdate2() {
        if (this.confirmQty) {
          this.batchInventory = false
        }
      },
      getpop() {
        const obj = {
          compno: this.row.compno,
          roleno: this.row.roleno,
          pgm: this.cid,
        }
        getBarPswdProps(obj).then((res) => {
          this.subData = res.data
          const arr = this.subData
          if (arr.length > 0) {
            setTimeout(() => {
              function tran(name) {
                let ind
                arr.forEach((e, index) => {
                  if (e.fldName === name) {
                    return ind = index
                  }
                })
                return ind
              }

              this.boxExistence = JSON.parse(tran('boxExistence') === undefined ? this.boxExistence : arr[tran('boxExistence')].fldValue)
              this.add = JSON.parse(tran('add') === undefined ? this.add : arr[tran('add')].fldValue)
              this.confirmQty = JSON.parse(tran('confirmQty') === undefined ? this.confirmQty : arr[tran('confirmQty')].fldValue)
              this.batchInventory = JSON.parse(tran('batchInventory') === undefined ? this.batchInventory : arr[tran('batchInventory')].fldValue)
              this.form.setFieldsValue({
                installedWh: tran('installedWh') === undefined ? '' : arr[tran('installedWh')].fldValue
              })
            }, 1)
          }
        })
      },
      getData() {
        this.subData = []
        const fidArr = this.obj.fidArr
        fidArr.forEach((i) => {
          this.subData.push(
            (this.onSubmitData = {
              compno: this.row.compno,
              roleno: this.row.roleno,
              typeId: '6',
              pgm: this.cid,
              fldName: i,
              fldValue: '',
            })
          )
        })
      },
      handleOK() {
        this.getData()
        this.form.validateFields((err, values) => {
          const arr = this.subData
          arr.forEach((i) => {
            if (i.fldName === 'installedWh') {
              i.fldValue = values.installedWh
            }
            if (i.fldName === 'add') {
              i.fldValue = this.add
            }
            if (i.fldName === 'confirmQty') {
              i.fldValue = this.confirmQty
            }
            if (i.fldName === 'batchInventory') {
              i.fldValue = this.batchInventory
            }
            if (i.fldName === 'boxExistence') {
              i.fldValue = this.boxExistence + ''
            }
          })
          if (!err) {
            addBarPswdProp(this.subData)
              .then(() => {
                this.$message.success(this.$t('public.success'))
              })
              .catch(() => {
                this.$message.error(this.$t('public.error'))
              })
          }
        })
      },
      user() {
        querySalm(
          Object.assign({
            current: 1,
            size: 10,
          })
        ).then((res) => {
          this.userList = res.data.records
        })
      },
      dep() {
        deptListPage(
          Object.assign({
            current: 1,
            size: 10,
          })
        ).then((response) => {
          this.deptsList = response.data.records
        })
      },
      wh() {
        getWh(
          Object.assign({
            current: 1,
            size: 10,
          })
        ).then((response) => {
          this.whs = response.data.records
        })
      },
      handleCancel() {
        this.$emit('Cancel')
        this.subData = []
      },
    },
  }
</script>