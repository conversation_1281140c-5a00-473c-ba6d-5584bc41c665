<template>
  <div>
    <a-form :form="form" ref="form">
      <span style="font-size:1rem">{{ $t('propertySettings.Batch') }}:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item :label="$t('propertySettings.serialNumber')" v-bind="formItemLayout">
            <a-select style="width:100%" allowClear
              v-decorator="['batch', { rules: [{ message:$t('propertySettings.Batch') }] }]" :getPopupContainer=" triggerNode => {
              return triggerNode.parentNode || document.body;
            }">
              <a-select-option value="1">按货品基础资料</a-select-option>
              <a-select-option value="2">不管制</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
      <!-- <span style="font-size:1rem">批号库存出库顺序:</span>
      <a-row>
        <a-col
          :xs="24"
          :sm="24"
          :md="12"
          :lg="12"
          :xl="12"
        >
          <a-form-item
            label="先进先出"
            v-bind="formItemLayout"
          >
            <a-select
              style="width:100%"
              allowClear
              v-decorator="['batOrder', { rules: [{ message:$t('propertySettings.Bill') }] }]"
              :getPopupContainer=" triggerNode => {
                return triggerNode.parentNode || document.body;
              }"
            >
              <a-select-option value="1">不管控</a-select-option>
              <a-select-option value="3">管控</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row> -->
      <span style="font-size:1rem">条码设定:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="是否可拆码" v-bind="formItemLayout">
            <a-switch v-model="boxExistence" />
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">扫描设置:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="批号匹配" v-bind="formItemLayout">
            <a-switch v-model="scanBatNo" />
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">主副数量推算:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="按单重推算" v-bind="formItemLayout">
            <a-switch v-model="dz" />
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">{{ $t('propertySettings.Default') }}:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item :label="$t('propertySettings.SNdept')" v-bind="formItemLayout">
            <a-select allowClear :notFoundContent="null"
              :getPopupContainer=" triggerNode => {return triggerNode.parentNode || document.body;}"
              v-decorator="['SNdept',{rules: [{ message: $t('propertySettings.storage') }],}]">
              <a-select-option v-for="item in deptsList" :key="item.dep" :label="item.name" :value="item.dep">{{
                item.name }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item :label="$t('propertySettings.SIdept')" v-bind="formItemLayout">
            <a-select allowClear :notFoundContent="null"
              :getPopupContainer=" triggerNode => {return triggerNode.parentNode || document.body;}"
              v-decorator="['SIdept',{rules: [{ message: $t('propertySettings.SIdept') }],}]">
              <a-select-option v-for="item in cussList" :key="item.dep" :label="item.name" :value="item.dep">{{
                item.name }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">流水码:</span>
      <a-row>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="流水码允许拆分" v-bind="formItemLayout">
            <a-switch v-model="isSplit" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <a-row :gutter="16">
      <a-col class="gutter-row" :span="12" style="text-align:right">
        <a-button id="ok" type="primary" @click="handleOK">{{ $t('public.save') }}{{ obj.subname }}</a-button>
      </a-col>
      <a-col class="gutter-row" :span="12" style="text-align:left">
        <a-button id="cancel" @click="handleCancel">{{ $t('public.cancel') }}</a-button>
      </a-col>
    </a-row>
  </div>
</template>
<script>

  import { deptListPage, getCusList1, addBarPswdProp, getBarPswdProps } from '@/api/barcode/propertySettings'

  export default {
    props: {
      obj: {
        required: true,
        type: Object
      },
      cid: {
        required: true,
        type: String
      },
      row: {
        required: true,
        type: Object
      }
    },
    data() {
      return {
        title: '',
        visible: true,
        confirmLoading: true,
        scanBatNo: false,
        boxExistence: false,
        dz: false,
        form: this.$form.createForm(this),
        cussList: [],
        deptsList: [],
        whs: [],
        subData: [],
        onSubmitData: {
          // 保存属性对象
          compno: '',
          roleno: '',
          typeId: '6',
          pgm: '',
          fldName: '',
          fldValue: ''
        },
        formItemLayout: {
          labelCol: {
            xs: { span: 24 },
            sm: { span: 7 },
            md: { span: 8 },
            lg: { span: 8 }
          },
          wrapperCol: {
            xs: { span: 5 },
            sm: { span: 16 },
            md: { span: 17 },
            lg: { span: 16 }
          }
        },
        isSplit: false,
      }
    },
    created() {
      if (this.obj.subname) {
        this.getpop()
        this.user()
        this.dep()
      }
    },
    methods: {
      user() {
        getCusList1(
          Object.assign({
            current: 1,
            size: 999
          })
        ).then(response => {
          this.cussList = response.data.records
        })
      },
      dep() {
        deptListPage(
          Object.assign({
            current: 1,
            size: 10
          })
        ).then(response => {
          this.deptsList = response.data.records
        })
      },
      getpop() {
        const obj = {
          compno: this.row.compno,
          roleno: this.row.roleno,
          pgm: this.cid
        }
        getBarPswdProps(obj).then(res => {
          this.subData = res.data
          const arr = this.subData
          if (arr.length > 0) {
            setTimeout(() => {
              function tran(name) {
                let ind
                arr.forEach((e, index) => {
                  if (e.fldName === name) {
                    return ind = index
                  }
                })
                return ind
              }
              this.boxExistence = JSON.parse(tran('boxExistence') === undefined ? this.boxExistence : arr[tran('boxExistence')].fldValue)
              this.isSplit = JSON.parse(tran('isSplit') === undefined || arr[tran('isSplit')].fldValue === '' || arr[tran('isSplit')].fldValue === null ? this.isSplit : arr[tran('isSplit')].fldValue)

              this.scanBatNo = JSON.parse(tran('scanBatNo') === undefined ? this.scanBatNo : arr[tran('scanBatNo')].fldValue)
              this.dz = JSON.parse(tran('dz') === undefined ? this.dz : arr[tran('dz')].fldValue)
              this.form.setFieldsValue({
                // batOrder: tran('batOrder') === undefined ? '' : arr[tran('batOrder')].fldValue,
                batch: tran('batch') === undefined ? '' : arr[tran('batch')].fldValue,
                SNdept: tran('SNdeptName') === undefined || arr[tran('SNdeptName')].fldValue === '' || arr[tran('SNdeptName')].fldValue === null ? '' : arr[tran('SNdeptName')].fldValue.split(',')[0],
                SIdept: tran('SIdeptName') === undefined || arr[tran('SIdeptName')].fldValue === '' || arr[tran('SIdeptName')].fldValue === null ? '' : arr[tran('SIdeptName')].fldValue.split(',')[0],
              })
            }, 1)
          }
        })
      },
      getData() {
        this.subData = []
        const fidArr = this.obj.fidArr
        fidArr.forEach(i => {
          this.subData.push(this.onSubmitData = {
            compno: this.row.compno,
            roleno: this.row.roleno,
            typeId: '6',
            pgm: this.cid,
            fldName: i,
            fldValue: ''
          })
        })
      },
      handleOK() {
        this.getData()
        this.form.validateFields((err, values) => {
          const arr = this.subData
          arr.forEach(i => {
            if (i.fldName === 'scanBatNo') {
              i.fldValue = this.scanBatNo
            }
            if (i.fldName === 'boxExistence') {
              i.fldValue = this.boxExistence
            }

            if (i.fldName === 'dz') {
              i.fldValue = this.dz
            }
            // if (i.fldName === 'batOrder') {
            //   i.fldValue = values.batOrder
            // }
            if (i.fldName === 'SNdeptName') {
              i.fldValue = values.SNdept
            }
            if (i.fldName === 'SIdeptName') {
              i.fldValue = values.SIdept
            }
            if (i.fldName === 'batch') {
              i.fldValue = values.batch
            }
            if (i.fldName === 'isSplit') {
              i.fldValue = this.isSplit
            }
          })

          if (!err) {
            addBarPswdProp(this.subData)
              .then(() => {
                this.$message.success(this.$t('public.success'))
              })
              .catch(() => {
                this.$message.error(this.$t('public.error'))
              })
          }
        })
      },
      handleCancel() {
        this.$emit('Cancel')
        this.subData = []
      }
    }
  }
</script>