<template>
  <div>
    <!-- 选择人员模态框 -->
    <a-modal
      title="人员选择"
      destroyOnClose
      width="40%"
      :visible="visible"
      :confirmLoading="confirmLoading"
      @cancel="handleCancel"
    >
      <a-form
        layout="inline"
        :form="form"
      >
        <a-form-item :label="$t('salm.salNo')">
          <a-input
            v-model="queryParam.salNo"
            :placeholder="$t('salm.salNo')"
          />
        </a-form-item>
        <a-button
          type="primary"
          @click="getList"
        >{{ $t('public.query') }}</a-button>
        <a-button
          style="margin-left: 8px"
          @click="reset"
        >{{ $t('public.reset') }}</a-button>
      </a-form>
      <!-- row-id="salNo" -->
      <vxe-table
        size="small"
        border
        show-overflow
        highlight-hover-row
        ref="xTable"
        class="radio-table"
        @checkbox-change="selectChangeEvent"
        :checkbox-config="{trigger: 'row', range: true}"
        :radio-config="{labelField: '', trigger: 'row'}"
        :data="tableData"
      >
        <vxe-table-column
          type="checkbox"
          width="50"
        ></vxe-table-column>
        <vxe-table-column
          field="salNo"
          title="salm.salNo"
          align="center"
          width="100"
        ></vxe-table-column>
        <vxe-table-column
          field="name"
          title="salm.name"
          align="center"
        ></vxe-table-column>
      </vxe-table>
      <vxe-pager
        :current-page="tablePage.currentPage"
        :page-size="tablePage.pageSize"
        :total="tablePage.total"
        :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
        @page-change="handlePageChange"
      >
      </vxe-pager>
      <template slot="footer">
        <a-button
          key="ok"
          @click="save"
        >{{ $t('public.sure') }}</a-button>
        <a-button
          key="cancel"
          @click="handleCancel"
        >{{ $t('public.cancel') }}</a-button>
      </template>
    </a-modal>
  </div>
</template>
<script>
import { getUserList } from '@/api/barcode/propertySettings'
import {
  mapState
} from 'vuex'
export default {
  name: 'SetList',
  props: {
    newarr: {
      required: true,
      type: Array
    }
  },
  data () {
    return {
      title: '',
      tableData: [],
      row: {},
      queryParam: {},
      selectRecords: [],
      visible: false,
      visible1: false,
      confirmLoading: false,
      treeData: [],
      currentPage: 1,
      form: this.$form.createForm(this),
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      text: []
    }
  },
  watch: {
    // currentPage: {
    //   handler (val) {
    //     this.arr = this.newarr
    //   },
    //   // 监听到数据变化时立即调用
    //   immediate: true,
    //   deep: true
    // }
  },
  computed: {
    ...mapState({
      propertyList: state => state.approval.propertyList
    })
  },
  created () {

  },

  methods: {
    // 分页触发事件
    click () {
      let arr = this.propertyList.filter(i => {
        return i.page === this.tablePage.currentPage
      })
      let showList = []
      arr.forEach(e => {
        showList.push(this.tableData[e.rowIndex])
      });
      this.$refs.xTable.setCheckboxRow([...showList], true)
      const selectRecords = this.$refs.xTable.getCheckboxRecords()
      this.text.push(...selectRecords)
    },
    open (model) {
      this.row = model
      this.getList()
      this.visible = true
    },
    getList (data) {
      this.dep = data
      this.loading = true
      getUserList(
        Object.assign(
          {
            // dep: '',
            roleno: this.row.roleno,
            current: this.tablePage.currentPage,
            size: this.tablePage.pageSize
          }, this.queryParam
        )
      )
        .then(res => {
          this.tableData = res.data.records
          this.tablePage.total = res.data.total
          this.tablePage.currentPage = res.data.current
          this.loading = false
          // this.click()
        })
        .catch(e => {
          this.requestFailed(e)
          this.loading = false
        })
    },
    // 分页
    handlePageChange ({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getList()
    },
    // 重置搜索内容
    reset () {
      this.queryParam = {}
    },
    selectChangeEvent ({ checked, records, rowIndex }) {
      // let obj = {
      //   page: this.tablePage.currentPage,
      //   rowIndex: rowIndex
      // }
      // if (checked) {
      //   this.$store.commit("SET_PROPER", obj);
      // } else {
      //   this.$store.commit("DEL_PROPER", obj);
      // }
    },
    save () {
      const list = this.$refs.xTable.getCheckboxRecords()
      var selectRecords = []
      selectRecords = [...list, ...this.text]
      const arr = []
      for (let index = 0; index < selectRecords.length; index++) {
        const vart =
          selectRecords[index].salNo +
          ':' +
          selectRecords[index].name
        for (let index1 = 0; index1 < arr.length; index1++) {
          const vart1 = arr[index1]
          if (vart === vart1) {
            arr.splice(index1, 1)
          }
        }
        arr.push(vart)
      }
      this.$emit('getTags', arr)
      this.$nextTick(() => {
        selectRecords = []
        this.handleCancel()
      })
    },

    handleCancel () {
      this.visible = false
      // this.text = []
    }
  }
}
</script>
