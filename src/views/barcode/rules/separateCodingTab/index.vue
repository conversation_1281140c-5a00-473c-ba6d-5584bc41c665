<template>
  <div>
    <a-card :bordered="false">
      <a-row :gutter="18">
        <a-col :span="24">
          <div class="table-page-search-wrapper">
            <a-radio-group
              size="large"
              v-model="type"
            >
              <a-radio-button value="1">3.{{ $t('separateCoding.dept') }}</a-radio-button>
              <a-radio-button
                style="margin-left:20px"
                value="2"
              >2.{{ $t('separateCoding.cust') }}</a-radio-button>
              <a-radio-button
                style="margin-left:20px"
                value="3"
              >1.{{ $t('separateCoding.goods') }}</a-radio-button>
            </a-radio-group>
          </div>
        </a-col>
      </a-row>
      <a-row style="margin-top:30px">
        <a-col :span="6">
          <div>
            <a-select
              style="width: 100%"
              @focus="getDep"
              size="large"
              v-model="depDs"
              placeholder="请选择部门/客户厂商"
              @change="handleChange"
            >
              <a-select-option
                :key="item.dep"
                :label="item.name"
                :value="item.dep"
                v-for="item in depDsList"
              >{{ item.name }}</a-select-option>
            </a-select>
          </div>
        </a-col>
      </a-row>
      <br />
      <div style="margin-bottom:10px">
        <a-collapse v-model="activeKey">
          <a-collapse-panel
            :header="$t('separateCoding.copy')"
            key="1"
          >
            <div>
              <el-transfer
                style="width:100%"
                filterable
                :titles="[ $t('separateCoding.list') , $t('separateCoding.select')]"
                v-model="value"
                :data="data"
                disabled
              >
                <el-button
                  slot="left-footer"
                  type="success"
                  size="mini"
                  @click="clickInput2"
                >{{ $t('separateCoding.get') }}</el-button>
                <el-button
                  slot="right-footer"
                  type="success"
                  size="mini"
                  @click="copyRule"
                >{{ $t('separateCoding.copy') }}</el-button>
              </el-transfer>
            </div>
          </a-collapse-panel>
        </a-collapse>
      </div>
      <!-- 表格 -->
      <tableList
        :tfList="tfList"
        :depDs="depDs"
        :type="type"
        @handleChange="handleChange"
      />
    </a-card>
  </div>
</template>
<script>
import { deptListPage, getCusList, getList, copy } from '@/api/barcode/separateCodingTab'
import tableList from './tableList'
export default {
  name: 'SeparateCodingList',
  components: {
    tableList
  },
  data () {
    return {
      data: [],
      value: [],
      value1: [],
      type: '1',
      depDs: '',
      depDsList: [],
      depDsList2: [],
      queryDepDsForm: {
        makeId: ''
      },
      tfList: [],
      activeKey: '',
      mockData: [],
      targetKeys: [],
      fdIdList: [
        {
          label: '供应商',
          value: 1
        },
        {
          label: '货品代号',
          value: 2
        },
        {
          label: '货品特征',
          value: 3
        },
        {
          label: '批号',
          value: 4
        },
        {
          label: '数量',
          value: 5
        },
        {
          label: '数量副',
          value: 6
        },
        {
          label: '库位',
          value: 7
        },
        {
          label: '来源单',
          value: 8
        },
        {
          label: '对方货号',
          value: 10
        },
        {
          label: '流水号',
          value: 11
        }
      ],
      groupidList: [
        {
          label: '组合一',
          value: '1'
        },
        {
          label: '组合二',
          value: '2'
        },
        {
          label: '组合三',
          value: '3'
        }
      ],
      tableData: []
    }
  },
  watch: {
    type () {
      this.tfList = []
      this.handleChange()
      this.data = []
    },
    depDs () {
      this.tfList = []
      this.data = []
      this.handleChange()
    }
  },
  created () {

  },
  methods: {
    clickInput2 () {
      this.data = []
      if (this.type === '1' || this.type === '3') {
        // 查询部门
        deptListPage(
          Object.assign({
            current: 1,
            size: 9999
          })
        ).then(response => {
          // 查询供应商
          this.depDsList2 = response.data.records
          this.depDsList2.forEach(i => {
            this.data.push(
              {
                key: i.dep,
                label: i.name,
                disabled: this.depDs === i.dep
              }
            )
          })
        }).catch(err => this.requestFailed(err))
      } else if (this.type === '2') {
        getCusList(
          Object.assign({
            current: 1,
            size: 9999
          })
        ).then(response => {
          this.depDsList2 = response.data.records
          this.depDsList2.forEach(i => {
            this.data.push(
              {
                key: i.dep,
                label: i.name,
                disabled: this.depDs === i.dep
              }
            )
          })
        }).catch(err => this.requestFailed(err))
      }
    },
    change () {

    },
    // 复制
    copyRule () {
      const assign = {
        no: this.depDs,
        nos: this.value,
        type: this.type
      }
      if (this.type !== 3) {
        if (this.depDs == null || this.depDs === '') {
          this.$notification.info({
            message: '提示',
            description: '请选择部门/供应商'
          })
          return
        }
      }
      copy(assign)
        .then(response => {
          if (response.msg === 'success') {
            this.$message.success(this.$t('public.success'))
            this.value1 = this.value
            this.value = []
            for (let i = this.data.length - 1; i >= 0; i--) {
              for (let j = 0; j < this.value1.length; j++) {
                if (this.data[i]) {
                  if (this.data[i].key === this.value1[j]) {
                    this.data[i] = {
                      key: this.data[i].key,
                      label: this.data[i].label,
                      disabled: true
                    }
                    continue // 结束当前本轮循环，开始新的一轮循环
                  }
                }
              }
            }
          } else {
            this.$notification.info({
              message: '提示',
              description: response.msg
            })
          }
        }).catch(err => this.requestFailed(err))
    },
    getDep () {
      this.depDsloading = true
      if (this.type === '1' || this.type === '3') {
        // 查询部门
        deptListPage(
          Object.assign({
            current: 1,
            size: 9999
          })
        ).then(response => {
          // 查询供应商
          this.depDsList = response.data.records
        })
      } else {
        getCusList(
          Object.assign({
            current: 1,
            size: 9999
          })
        ).then(response => {
          this.depDsList = response.data.records
        }).catch(err => this.requestFailed(err))
      }
    },
    // 选择
    handleChange () {
      getList(
        Object.assign({
          no: this.depDs,
          type: this.type
        })
      ).then(response => {
       
        this.tfList = response.data
      }).catch(err => this.requestFailed(err))
    }

  }
}
</script>
<style lang="less" scoped>
.el-transfer-panel {
  width: 44%;
  height: 400px;
}
.el-transfer-panel__body {
  width: 97%;
}
.el-transfer__buttons {
  padding: 0;
}
</style>
