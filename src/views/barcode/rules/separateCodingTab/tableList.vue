<template>
  <div>
    <vxe-toolbar>
      <template v-slot:buttons>
        <a-dropdown :trigger="['click']" v-permission="barcode_separatecoding_del">
          <a-button size='small'>{{ $t('public.action') }}
            <a-icon type="down" />
          </a-button>
          <a-menu slot="overlay">
            <a-menu-item key="0">
              <a @click="dropdownMenuEvent('remove')">删除选中</a>
            </a-menu-item>
          </a-menu>
        </a-dropdown>
        <a-button
          size='small'
          style="margin-left:10px"
          type="primary"
          icon="plus"
          @click="insertEvent(-1)"
          v-permission="barcode_separatecoding_add"
        >{{ $t('public.add') }}</a-button>
        <a-button
          size='small'
          style="margin-left:10px"
          type="primary"
          @click="save()"
          v-permission="barcode_separatecoding_save"
        >{{ $t('public.save') }}</a-button>
      </template>
    </vxe-toolbar>
    <vxe-table
      style="width: 100%"
      ref="xTable"
      keep-source
      header-align="center"
      size="mini"
      border
      :data.sync="tfList"
      :header-cell-style="rowClass"
      :edit-config="{trigger: 'click', mode: 'cell', showStatus: true, isTabKey: true, isArrowKey: true, isCheckedEdit: true,activeMethod:activeMethod}"
      :edit-rules="validRules"
      border:inner
      :cell-style="{color:'#FF0000'}"
    >
      <vxe-table-column
        type="checkbox"
        fixed="left"
        align="center"
        width="50"
      ></vxe-table-column>
      <vxe-table-column
        field="itm"
        align="center"
        label="分段编码"
        :edit-render="{name: '$select', options: fdIdList}"
      ></vxe-table-column>
      <vxe-table-column
        field="groupid"
        align="center"
        label="组合"
        :edit-render="{name: '$select', options: groupidList}"
      ></vxe-table-column>
      <vxe-table-column
        title="组合一"
        align="center"
      >
        <vxe-table-column title="">
          <vxe-table-column
            title="S1"
            align="center"
          >
            <vxe-table-column
              field="s1n1"
              title="开始位置"
              align="center"
              :edit-render="{name: 'input'}"
            ></vxe-table-column>
          </vxe-table-column>
        </vxe-table-column>
        <vxe-table-column
          title="二选一"
          align="center"
        >
          <vxe-table-column
            title="S2"
            align="center"
          >
            <vxe-table-column
              field="s2n1"
              title="截至位置"
              align="center"
              :edit-render="{name: 'input'}"
            ></vxe-table-column>
          </vxe-table-column>
          <vxe-table-column
            title="S3"
            align="center"
          >
            <vxe-table-column
              field="s3n1"
              title="截至第几个字符"
              align="center"
              :edit-render="{name: 'input'}"
            ></vxe-table-column>
            <vxe-table-column
              field="s3n2"
              title="字符"
              align="center"
              :edit-render="{name: 'input'}"
            ></vxe-table-column>
            <vxe-table-column
              field="s3n3"
              title="截止字符倒数位置（含字符位）"
              align="center"
              :edit-render="{name: 'input'}"
            ></vxe-table-column>
          </vxe-table-column>
        </vxe-table-column>
      </vxe-table-column>
      <vxe-table-column
        title="组合二"
        align="center"
      >
        <vxe-table-column
          title=""
          align="center"
        >
          <vxe-table-column
            title="S4"
            align="center"
          >
            <vxe-table-column
              field="s4n1"
              title="倒数位置"
              align="center"
              :edit-render="{name: 'input'}"
            ></vxe-table-column>
          </vxe-table-column>
        </vxe-table-column>
        <vxe-table-column
          title=""
          align="center"
        >
          <vxe-table-column
            title="S5"
            align="center"
          >
            <vxe-table-column
              field="s5n1"
              title="截至倒数位置"
              align="center"
              :edit-render="{name: 'input'}"
            ></vxe-table-column>
          </vxe-table-column>
        </vxe-table-column>
      </vxe-table-column>
      <vxe-table-column
        title="组合三"
        align="center"
      >
        <vxe-table-column
          title=""
          align="center"
        >
          <vxe-table-column
            title="S6"
            align="center"
          >
            <vxe-table-column
              field="s6n1"
              title="第几个字符"
              align="center"
              :edit-render="{name: 'input'}"
            ></vxe-table-column>
            <vxe-table-column
              field="s6n2"
              title="字符"
              align="center"
              :edit-render="{name: 'input'}"
            ></vxe-table-column>
            <vxe-table-column
              field="s6n3"
              title="开始位置（含字符位）"
              align="center"
              :edit-render="{name: 'input'}"
            ></vxe-table-column>
          </vxe-table-column>
        </vxe-table-column>
        <vxe-table-column
          title="二选一"
          align="center"
        >
          <vxe-table-column
            title="S7"
            align="center"
          >
            <vxe-table-column
              field="s7n1"
              title="截至位置"
              align="center"
              :edit-render="{name: 'input'}"
            ></vxe-table-column>
          </vxe-table-column>
          <vxe-table-column
            title="S8"
            align="center"
          >
            <vxe-table-column
              field="s8n1"
              title="截至第几个字符"
              align="center"
              :edit-render="{name: 'input'}"
            ></vxe-table-column>
            <vxe-table-column
              field="s8n2"
              title="字符"
              align="center"
              :edit-render="{name: 'input'}"
            ></vxe-table-column>
            <vxe-table-column
              field="s8n3"
              title="截止字符倒数位置（含字符位）"
              align="center"
              :edit-render="{name: 'input'}"
            ></vxe-table-column>
          </vxe-table-column>
        </vxe-table-column>
      </vxe-table-column>
    </vxe-table>
  </div>
</template>
<script>
import validatenum from '@/util/validate'
import { delRule, insert } from '@/api/barcode/separateCodingTab'

export default {
  name: 'TableList',
  data () {
    return {
      barcode_separatecoding_del: 'barcode_separatecoding_del',
      barcode_separatecoding_add: 'barcode_separatecoding_add',
      barcode_separatecoding_save: 'barcode_separatecoding_save',
      obj: [],
      value: '',
      activeKey: '',
      fdIdList: [
        {
          label: '供应商',
          value: 1
        },
        {
          label: '货品代号',
          value: 2
        },
        {
          label: '货品特征',
          value: 3
        },
        {
          label: '批号',
          value: 4
        },
        {
          label: '数量',
          value: 5
        },
        {
          label: '数量副',
          value: 6
        },
        {
          label: '库位',
          value: 7
        },
        {
          label: '来源单号',
          value: 8
        },
        {
          label: '对方货号',
          value: 10
        },
        {
          label: '流水号',
          value: 11
        }
      ],
      groupidList: [
        {
          label: '组合一',
          value: '1'
        },
        {
          label: '组合二',
          value: '2'
        },
        {
          label: '组合三',
          value: '3'
        }
      ],
      tableData: [],
      validRules: {
        // 字符串方式
        groupid: [{ required: true, message: '必填字段', trigger: 'change' }],
        itm: [{ required: true, message: '必填字段', trigger: 'change' }],
        s3n2: [{ max: 1, message: '字符只能一位', trigger: 'change' }],
        s6n2: [{ max: 1, message: '字符只能一位', trigger: 'change' }],
        s8n2: [{ max: 1, message: '字符只能一位', trigger: 'change' }],
        s1n1: [
          { type: 'number', message: '填写数字', trigger: 'change' },
          { validator: validatenum, trigger: 'change' }
        ],
        s2n1: [
          { type: 'number', message: '填写数字', trigger: 'change' },
          { validator: validatenum, trigger: 'change' }
        ],
        s3n1: [
          { type: 'number', message: '填写数字', trigger: 'change' },
          { validator: validatenum, trigger: 'change' }
        ],
        s3n3: [
          { type: 'number', message: '填写数字', trigger: 'change' },
          { validator: validatenum, trigger: 'change' }
        ],
        s4n1: [
          { type: 'number', message: '填写数字', trigger: 'change' },
          { validator: validatenum, trigger: 'change' }
        ],
        s5n1: [
          { type: 'number', message: '填写数字', trigger: 'change' },
          { validator: validatenum, trigger: 'change' }
        ],
        s6n1: [
          { type: 'number', message: '填写数字', trigger: 'change' },
          { validator: validatenum, trigger: 'change' }
        ],
        s6n3: [
          { type: 'number', message: '填写数字', trigger: 'change' },
          { validator: validatenum, trigger: 'change' }
        ],
        s7n1: [
          { type: 'number', message: '填写数字', trigger: 'change' },
          { validator: validatenum, trigger: 'change' }
        ],
        s8n1: [
          { type: 'number', message: '填写数字', trigger: 'change' },
          { validator: validatenum, trigger: 'change' }
        ],
        s8n3: [
          { type: 'number', message: '填写数字', trigger: 'change' },
          { validator: validatenum, trigger: 'change' }
        ]
      }
    }
  },
  props: {
    tfList: {
      required: true,
      type: Array
    },
    depDs: {
      required: true,
      type: String
    },
    type: {
      required: true,
      type: String
    }
  },
  methods: {
    // 添加
    insertEvent (row) {
      const xTable = this.$refs.xTable
      const record = {
        itm: '',
        groupid: '',
        s1n1: '',
        s2n1: '',
        s3n1: '',
        s3n2: '',
        s3n3: '',
        s4n1: '',
        s5n1: '',
        s6n1: '',
        s6n2: '',
        s6n3: '',
        s7n1: '',
        s8n1: '',
        s8n2: '',
        s8n3: ''
      }
      xTable.insertAt(record, row).then(({ row }) => {
        xTable.setActiveRow(row)
      })
    },
    // 颜色
    rowClass ({ $rowIndex, column, columnIndex, $columnIndex }) {
      if ($rowIndex === 0 && columnIndex === 1) {
        return 'background:#EEC211'
      }
      if ($rowIndex === 0 && columnIndex === 2) {
        return 'background:#91D52B'
      }
      if ($rowIndex === 0 && columnIndex === 3) {
        return 'background:#F79709'
      }
      if ($rowIndex === 0 && columnIndex === 4) {
        return 'background:rgb(230, 230, 26)'
      }
      if ($rowIndex === 0 && columnIndex === 5) {
        return 'background:rgb(204, 255, 0)'
      }
      if ($rowIndex === 1 && columnIndex === 0) {
        return 'background:rgb(220, 203, 138)'
      }
      if ($rowIndex === 1 && columnIndex === 1) {
        return 'background:rgb(223, 196, 230)'
      }
      if ($rowIndex === 3 && columnIndex === 3) {
        return 'background:rgb(247, 151, 9)'
      }
      if ($rowIndex === 3 && columnIndex === 4) {
        return 'background:rgb(223, 196, 230)'
      }
      if ($rowIndex === 3 && columnIndex === 5) {
        return 'background:rgb(220, 203, 138)'
      }
      if ($rowIndex === 3 && columnIndex === 6) {
        return 'background:rgb(220, 203, 138)'
      }
      if ($rowIndex === 3 && columnIndex === 7) {
        return 'background:rgb(220, 203, 138)'
      }
      if ($rowIndex === 3 && columnIndex === 8) {
        return 'background:rgb(230, 230, 26)'
      }
      if ($rowIndex === 3 && columnIndex === 9) {
        return 'background:rgb(230, 230, 26)'
      }
      if ($rowIndex === 3 && columnIndex === 10) {
        return 'background:rgb(204, 255, 0)'
      }
      if ($rowIndex === 3 && columnIndex === 11) {
        return 'background:rgb(204, 255, 0)'
      }
      if ($rowIndex === 3 && columnIndex === 12) {
        return 'background:rgb(204, 255, 0)'
      }
      if ($rowIndex === 3 && columnIndex === 13) {
        return 'background:rgb(223, 196, 230)'
      }
      if ($rowIndex === 3 && columnIndex === 14) {
        return 'background:rgb(220, 203, 138)'
      }
      if ($rowIndex === 3 && columnIndex === 15) {
        return 'background:rgb(220, 203, 138)'
      }
      if ($rowIndex === 3 && columnIndex === 16) {
        return 'background:rgb(220, 203, 138)'
      }
      if ($rowIndex === 2 && $columnIndex === 0) {
        return 'background:#F79709'
      }
      if ($rowIndex === 2 && $columnIndex === 1) {
        return 'background:#F79709'
      }
      if ($rowIndex === 2 && $columnIndex === 2) {
        return 'background:#F79709'
      }
      if ($rowIndex === 2 && $columnIndex === 3) {
        return 'background:rgb(230, 230, 26)'
      }
      if ($rowIndex === 2 && $columnIndex === 4) {
        return 'background:rgb(230, 230, 26)'
      }
      if ($rowIndex === 2 && $columnIndex === 5) {
        return 'background:rgb(204, 255, 0)'
      }
      if ($rowIndex === 2 && $columnIndex === 6) {
        return 'background:rgb(204, 255, 0)'
      }
      if ($rowIndex === 2 && $columnIndex === 7) {
        return 'background:rgb(204, 255, 0)'
      }
      // if ($rowIndex === 1 && columnIndex === 0) {
      //   return 'background:#F79709'
      // }
      // if ($rowIndex === 1 && columnIndex === 1) {
      //   return 'background:#F79709'
      // }
      // if ($rowIndex === 2 && columnIndex === 0) {
      //   return 'background:#F79709'
      // }
      // if ($rowIndex === 2 && columnIndex === 1) {
      //   return 'background:#F79709'
      // }
      // if ($rowIndex === 2 && columnIndex === 2) {
      //   return 'background:#F79709'
      // }
      // if ($rowIndex === 3 && columnIndex === 0) {
      //   return 'background:#F79709'
      // }
      // if ($rowIndex === 3 && columnIndex === 1) {
      //   return 'background:#DFC4E6'
      // }
      // if ($rowIndex === 3 && columnIndex === 2) {
      //   return 'background:#DCCB8A'
      // }
      // if ($rowIndex === 3 && columnIndex === 3) {
      //   return 'background:#DCCB8A'
      // }
      // if ($rowIndex === 3 && columnIndex === 4) {
      //   return 'background:#DCCB8A'
      // }

      // if ($rowIndex === 0 && columnIndex === 4) {
      //   return 'background:#E6E61A'
      // }

      // if ($rowIndex === 1 && columnIndex === 2) {
      //   return 'background:#E6E61A'
      // }
      // if ($rowIndex === 1 && columnIndex === 3) {
      //   return 'background:#E6E61A'
      // }
      // if ($rowIndex === 2 && columnIndex === 3) {
      //   return 'background:#E6E61A'
      // }
      // if ($rowIndex === 2 && columnIndex === 4) {
      //   return 'background:#E6E61A'
      // }
      // if ($rowIndex === 3 && columnIndex === 5) {
      //   return 'background:#E6E61A'
      // }
      // if ($rowIndex === 3 && columnIndex === 6) {
      //   return 'background:#E6E61A'
      // }

      // if ($rowIndex === 0 && columnIndex === 5) {
      //   return 'background:#CCFF00'
      // }
      // if ($rowIndex === 1 && columnIndex === 4) {
      //   return 'background:#CCFF00'
      // }
      // if ($rowIndex === 1 && columnIndex === 5) {
      //   return 'background:#CCFF00'
      // }
      // if ($rowIndex === 2 && columnIndex === 5) {
      //   return 'background:#CCFF00'
      // }
      // if ($rowIndex === 2 && columnIndex === 6) {
      //   return 'background:#CCFF00'
      // }
      // if ($rowIndex === 2 && columnIndex === 7) {
      //   return 'background:#CCFF00'
      // }
      // if ($rowIndex === 3 && columnIndex === 7) {
      //   return 'background:#CCFF00'
      // }
      // if ($rowIndex === 3 && columnIndex === 8) {
      //   return 'background:#CCFF00'
      // }
      // if ($rowIndex === 3 && columnIndex === 9) {
      //   return 'background:#CCFF00'
      // }
      // if ($rowIndex === 3 && columnIndex === 10) {
      //   return 'background:#DFC4E6'
      // }
      // if ($rowIndex === 3 && columnIndex === 11) {
      //   return 'background:#E7DDB1'
      // }
      // if ($rowIndex === 3 && columnIndex === 12) {
      //   return 'background:#E7DDB1'
      // }
      // if ($rowIndex === 3 && columnIndex === 13) {
      //   return 'background:#E7DDB1'
      // }

      // 表头行标号为0
    },
    // 单元格开启判断
    activeMethod ({ row, rowIndex, column, columnIndex }) {
      

      if (['itm'].includes(column.property)) {
        // 默认可编辑
        return true
      }
      if (['groupid'].includes(column.property)) {
        // 默认可编辑
        return true
      }
      if (row.groupid === '1') {
        // 选择组合一
        if (['s1n1'].includes(column.property)) {
          return true
        }
        row.s4n1 = ''
        row.s5n1 = ''
        row.s6n1 = ''
        row.s6n2 = ''
        row.s6n3 = ''
        row.s7n1 = ''
        row.s8n1 = ''
        row.s8n2 = ''
        row.s8n3 = ''
        if (row.s2n1 === '' && row.s3n1 === '' && row.s3n2 === '' && row.s3n3 === '') {
          if (['s2n1', 's3n1', 's3n2', 's3n3'].includes(column.property)) {
            return true
          }
        }
        if (row.s2n1 !== '' && row.s3n1 === '' && row.s3n2 === '' && row.s3n3 === '') {
          if (['s3n1', 's3n2', 's3n3'].includes(column.property)) {
            return false
          }
          if (['s2n1'].includes(column.property)) {
            return true
          }
        }
        if (row.s2n1 === '' && (row.s3n1 !== '' || row.s3n2 !== '' || row.s3n3 !== '')) {
          if (['s3n1', 's3n2', 's3n3'].includes(column.property)) {
            return true
          }
          if (['s2n1'].includes(column.property)) {
            return false
          }
        }
      }
      if (row.groupid === '2') {
        // 选择组合二
        if (['s4n1'].includes(column.property)) {
          return true
        }
        if (['s5n1'].includes(column.property)) {
          return true
        }
        row.s1n1 = ''
        row.s2n1 = ''
        row.s3n1 = ''
        row.s3n2 = ''
        row.s3n3 = ''
        row.s6n1 = ''
        row.s6n2 = ''
        row.s6n3 = ''
        row.s7n1 = ''
        row.s8n1 = ''
        row.s8n2 = ''
        row.s8n3 = ''
      }
      if (row.groupid === '3') {
        // 选择组合三
        if (['s6n1'].includes(column.property)) {
          return true
        }
        if (['s6n2'].includes(column.property)) {
          return true
        }
        if (['s6n3'].includes(column.property)) {
          return true
        }
        row.s1n1 = ''
        row.s2n1 = ''
        row.s3n1 = ''
        row.s3n2 = ''
        row.s3n3 = ''
        row.s4n1 = ''
        row.s5n1 = ''
        if (
          // 组合一 二选一 S3 有内容 禁用 S2
          (row.s8n1 !== '' && row.s8n1 != null) ||
          (row.s8n2 !== '' && row.s8n2 != null) ||
          (row.s8n3 !== '' && row.s8n3 != null)
        ) {
          if (['s7n1'].includes(column.property)) {
            return false
          }
        } else {
          if (['s7n1'].includes(column.property)) {
            return true
          }
        }
        if (row.s7n1 !== '') {
          // 组合一 二选一 S7 有内容 禁用 S8
          if (['s8n1'].includes(column.property)) {
            return false
          }
          if (['s8n2'].includes(column.property)) {
            return false
          }
          if (['s8n3'].includes(column.property)) {
            return false
          }
        } else {
          if (['s8n1'].includes(column.property)) {
            return true
          }
          if (['s8n2'].includes(column.property)) {
            return true
          }
          if (['s8n3'].includes(column.property)) {
            return true
          }
        }
      }
      // return false
    },
    // 保存
    async save () {

      const errMap = await this.$refs.xTable.validate().catch(errMap => errMap)
      if (errMap) {
        this.$notification.error({
          message: '提示',
          description: '请输入正确格式！'
        })
      } else {
        // const rest = this.$refs.xTable.getCheckboxRecords()
        this.obj = this.$refs.xTable.getTableData().tableData
      
        // const uprest = this.$refs.xTable.getUpdateRecords()
       
        // if (rest.length > 0) {
        //   this.obj = rest
        // } else if (uprest.length > 0) {
        //   this.obj = uprest
        // }
        const assign = {
          no: this.depDs,
          type: this.type,
          rest: this.obj
        }
        if (this.type !== 3) {
          if (this.depDs == null || this.depDs === '') {
            this.$notification.info({
              message: '提示',
              description: '请选择部门/供应商'
            })
            return
          }
        }
        if (this.obj.length > 0) {
          insert(assign).then(response => {
            if (response.msg === 'success') {
              this.$message.success(this.$t('public.success'))
              // this.tfList = response.data
            } else {
              this.$message.error(response.data)
            }
          }).catch(err => this.requestFailed(err))
            .finally(() => {
              this.spinning = false
            })
        }

        // if (this.tfList.length === 0 || assign.rest.length === 0) {
        //   this.$notification.info({
        //     message: '提示',
        //     description: '请先添加数据或者修改'
        //   })
        // } else {
        //   if (this.obj.length > 0) {
        //     insert(assign).then(response => {
        //       if (response.msg === 'success') {
        //         this.$message.success(this.$t('public.success'))
        //         // this.tfList = response.data
        //       } else {
        //         this.$message.error(this.$t('public.error'))
        //       }
        //     })
        //   }
        // }

        //  this.$refs.xTable.validate(valid => {
     
        //   if (valid) {

        //   } else {
        //     this.$notification.error({
        //       message: '提示',
        //       description: '请输入正确格式！'
        //     })
        //   }
        // })
      }




    },
    // 删除按钮
    dropdownMenuEvent (name) {
      switch (name) {
        case 'remove': {
          const selectRecords = this.$refs.xTable.getCheckboxRecords()
          if (selectRecords.length) {
            const that = this
            this.$confirm({
              title: this.$t('public.del.title'),
              content: this.$t('public.del.content'),
              okText: this.$t('public.sure'),
              okType: 'danger',
              cancelText: this.$t('public.cancel'),
              onOk () {
                delRule(selectRecords)
                  .then(() => {
                    that.$emit('handleChange')
                    that.$message.success(that.$t('public.success'))
                  })
                  .catch(err => that.requestFailed(err))
              },
              onCancel () {
              }
            })
          } else {
            this.$message.warning(this.$t('public.list'))
          }
          break
        }
      }
    }
  }
}
</script>
