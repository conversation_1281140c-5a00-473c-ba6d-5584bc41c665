<template>
  <div>
    <a-drawer
      :title="title"
      placement="right"
      :closable="false"
      @close="onClose"
      :visible="visible"
      :destroyOnClose="true"
      width="70%"
    >
      <a-form
        :form="form"
        ref="form"
      >
      <a-row :gutter="16" v-if="!objNo">
        <a-col
            :xs="24"
            :sm="24"
            :md="12"
            :lg="8"
            :xl="8"
          >
       <a-form-item
          label="组号"
          v-bind="formItemLayout"
        >
      <el-input
        v-model="groupNo"
        style="width:200px;"
      />
       </a-form-item>
        </a-col>
       </a-row>
      <vxe-table
            size='small'
            border
            resizable
            stripe
            highlight-current-row
            show-overflow
            highlight-hover-row
            export-config
            ref="xTable"
            :loading="loading"
            :data="tableData"
            :keyboard-config="{ isArrow: true }"
            :edit-config="{ trigger: 'click', mode: 'row' }"
          >
          <!-- @cell-dblclick="cellDBLClickEvent" -->
            <vxe-table-column
              type="checkbox"
              fixed="left"
              align="center"
              :width="50"
            ></vxe-table-column>
            <vxe-table-column
              field="salNo"
              fixed="left"
              title="人员代号"
              align="center"
            ></vxe-table-column>
            <vxe-table-column
              field="name"
              title="姓名"
              align="center"
            ></vxe-table-column>
          </vxe-table>
          <vxe-pager
            :loading="loading"
            :current-page="tablePage.currentPage"
            :page-size="tablePage.pageSize"
            :total="tablePage.total"
            :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
            @page-change="handlePageChange"
          ></vxe-pager>
        <a-row :gutter="16">
          <!-- <a-col
            :xs="24"
            :sm="24"
            :md="12"
            :lg="8"
            :xl="8"
          >

            <a-form-item
              label="原因代号"
              v-bind="formItemLayout"
            >
              <a-input
                :disabled="formIndex"
                v-decorator="['spcNo', { rules: [{ required: true, message:'原因代号' }
                                                 ,{ max:12,message:$t('public.len')}
                ] }]"
                placeholder="请输入原因代号"
              />
            </a-form-item>
          </a-col>
          <a-col
            :xs="24"
            :sm="24"
            :md="12"
            :lg="8"
            :xl="8"
          >
            <a-form-item
              label="不合格原因描述"
              v-bind="formItemLayout"
            >
              <a-input
                :disabled="formStatus"
                v-decorator="['name', { rules: [{ required: true, message:'不合格原因描述' }
                                                ,{ max:50,message:$t('public.len')}
                ] }]"
                placeholder="请输入不合格原因描述"
              />
            </a-form-item>
          </a-col> -->
          <!-- <a-col
            :xs="24"
            :sm="24"
            :md="12"
            :lg="8"
            :xl="8"
          >
            <a-form-item
              label="备注"
              v-bind="formItemLayout"
            >
              <a-input
                :disabled="formStatus"
                v-decorator="['rem', { rules: [{ required: false, message:'备注' }
                                                   ,{ max:40,message:$t('public.len')}
                ] }]"
                placeholder="请输入备注"
              />
            </a-form-item>
          </a-col> -->
        </a-row>
      </a-form>
      <a-row :gutter="16">
        <a-col
          class="gutter-row"
          :span="12"
          style="text-align:right"
        >
        <!-- &&this.permissions.basic_salm_edit -->
          <a-button
            type="primary"
            v-if="modeType==='0'"
            :loading="loading"
            @click="handleOK()"
          >{{ $t('public.save') }}</a-button>
          <a-button
            type="primary"
            v-if="modeType==='1'"
            @click="handleMenuClick()"
          >{{ $t('public.edit') }}</a-button>
          <a-button
            type="primary"
            v-if="modeType==='2'"
            :loading="loading"
            @click="handleEdit()"
          >{{ $t('public.save') }}</a-button>
        </a-col>
        <a-col
          class="gutter-row"
          :span="12"
          style="text-align:left"
        >
          <a-button @click="onClose">{{ $t('public.cancel') }}</a-button>
        </a-col>
      </a-row>
    </a-drawer>
  </div>
</template>

<script>
import { add,qcItmadd, edit, getTogto,salmquery,groupaddTeam } from '@/api/salm'
import MySelectList from '@/components/MySelectList'
import moment from 'moment'
import { mapGetters } from 'vuex'
export default {
  name: 'SalmModal',
  components: {
    MySelectList
  },
  data () {
    return {
      title: '',
      loading: false,
      visible: false,
      formStatus: false,
      formIndex: false,
      UpStatus: false,
      data: '',
      Updata: '',
      row: {},
      modeType: '',
      form: this.$form.createForm(this),
      formItemLayout: {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 7 },
          md: { span: 8 },
          lg: { span: 7 }
        },
        wrapperCol: {
          xs: { span: 5 },
          sm: { span: 16 },
          md: { span: 17 },
          lg: { span: 16 }
        }
      },
      flag:'',
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      tableData:[],
      groupNo:'',
      objNo:''
    }
  },
  computed: {
    ...mapGetters(['permissions'])
  },
  created () {
    this.salNoList()
  },
  methods: {
     handlePageChange ({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.salNoList()

    },
    salNoList(){
      salmquery(
        Object.assign(
          {
           current: this.tablePage.currentPage,
            size: this.tablePage.pageSize,
            salNo:''
            // qcItm: this.queryParam.qcItm,
            // name: this.queryParam.name,
            // current: this.tablePage.currentPage,
            // size: this.tablePage.pageSize
          }
        )
      )
        .then(res => {
         
          this.tableData = res.data.records
          this.tablePage.total = res.data.total
          this.tablePage.currentPage = res.data.current
          this.loading = false
        })
        .catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    disabledDate (current) {
      return current && current < moment().endOf('day')
    },
    // 渲染组件
    choose (obj) {
    
      var map = {}
      if (obj.obj.name === 'upSalNo') {
        map[obj.obj.name] = obj.obj.data.salNo
      }
      if (obj.obj.name === 'dep') {
        map[obj.obj.name] = obj.obj.data.dep
      }
      this.form.setFieldsValue(map)
    },
    // 取消关闭
    onClose () {
      this.form.resetFields()
      this.loading = false
      this.visible = false
      this.keyStatus = ''
      this.data = ''
      this.Updata = ''
      this.modeType = '0'
      this.row = {}
    },
    create (model, obj) {
      if(obj){
        this.objNo = obj
        this.groupNo = obj
      }else{
        this.objNo = ''
        this.groupNo = ''
      }
      
      this.formIndex = false
      this.title = model.title
      this.modeType = '0'
      this.visible = true
      this.formStatus = false
      setTimeout(() => {
        if (obj.name && obj.id !== '') {
          this.data = obj.name
          this.form.setFieldsValue({
            'dep': obj.id
          })
        }
        this.form.setFieldsValue({
          'logon': 'F'
        })
      }, 1)
    },
    // 点击编辑按钮
    handleMenuClick () {
      this.modeType = '2'
      this.title = this.$t('public.edit')
      this.formIndex = true
      this.UpStatus = false
      this.formStatus = false
    },
    // 双击弹出框
    edit (model, row) {
      this.title = model.title
      this.modeType = '1'
      this.row = row
      this.formStatus = true
      this.UpStatus = true
      this.formIndex = true
      this.visible = true
      setTimeout(() => {
        // this.data = row.sysDept.name
        // this.Updata = row.upSalm === null ? '' : row.upSalm.name
        this.form.setFieldsValue({
          // dutOtD: row.dutOtD == null ? null : moment(row.dutOtD),
          // dutInD: row.dutInD == null ? null : moment(row.dutInD),
          // bth: row.bth == null ? null : moment(row.bth),
          'qcItm': row.qcItm,
          'name': row.name,
          qcMth: row.qcMth,
          qcItmUp: row.qcItmUp,
          rem: row.rem,
          // 'engName': row.engName,
          // 'tel1': row.tel1,
          // 'pos': row.pos,
          // deproNo: row.deproNo,
          // 'upSalNo': row.upSalNo,
          // 'rem': row.rem,
          // 'logon': row.logon,
          // 'email': row.email,
          // 'dep': row.dep + ''
        })
      }, 1)
    },

    // 添加确认
    handleOK () {

      const selectRecords = this.$refs.xTable.getCheckboxRecords()
     
      let salNoList = []
      selectRecords.forEach(element => {
        salNoList.push(element.salNo)
      });
      if(this.groupNo){
        if (selectRecords.length) {
          groupaddTeam({
            groupNo:this.groupNo,
            ygNos:salNoList,
          })
              .then((res) => {
                if (res !== null) {

                  getTogto().then(() => { })
                  this.$emit('onOk')
                  this.loading = false
                  this.visible = false
                  this.$message.success(this.$t('public.success'))
                }
              })
              .catch(err => this.requestFailed(err))
              .finally(() => {
                this.loading = false
              })
          return
        }else{
          this.$message.warning('至少选择一条数据')
        }
      }else{
         this.$message.warning('请输入组号')
      }
      
      // this.form.validateFields((err, values) => {
      //   const Params = {
      //     ...values,
      //     flag:1 
      //   }
      //   if (!err) {
      //     this.loading = true
      //      qcItmadd(Params)
      //       .then((res) => {
      //         if (res !== null) {
      //           getTogto().then(() => { })
      //           this.$emit('onOk',values.qcItmUp)
      //           this.loading = false
      //           this.visible = false
      //           this.$message.success(this.$t('public.success'))
      //         }
      //       })
      //       .catch(err => this.requestFailed(err))
      //       .finally(() => {
      //         this.loading = false
      //       })
      //   }
      // })
    },
    // 确认编辑
    handleEdit () {

      this.form.validateFields((err, values) => {
      

        const Params = {
          ...values,
          id: this.row.id,
          flag:''
          // dutOtD: values.dutOtD == null ? null : moment(values.dutOtD).format('YYYY-MM-DD'),
          // dutInD: values.dutInD == null ? null : moment(values.dutInD).format('YYYY-MM-DD'),
          // bth: values.bth == null ? null : moment(values.bth).format('YYYY-MM-DD')
        }
        // if (Params.salNo === Params.upSalNo) {
        //   this.$notification.error({
        //     message: this.$t('public.errorInfo'),
        //     description: this.$t('public.upLeavel')
        //   })
        //   err = true
        // }
        if(values.qcItmUp == values.qcItm){
            this.$notification.error({
              message: this.$t('public.errorInfo'),
              description: this.$t('public.upLeavel')
            })
            err = true
        }
        if (!err) {
          // edit
          this.loading = true
           qcItmadd(Params)
            .then((res) => {
              if (res !== null) {
                if(res.msg == 'fail'){
                   this.$message.warning(res.data)
                   return
                }else{
                   getTogto().then(() => { })
                    this.loading = false
                    this.$emit('onOk',values.qcItmUp)
                    this.visible = false
                    this.$message.success(this.$t('public.success'))
                    this.data = ''
                }
              }
            })
            .catch(err => this.requestFailed(err))
            .finally(() => {
              this.loading = false
            })
        }
      })
    }

  }
}
</script>
