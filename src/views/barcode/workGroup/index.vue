<template>
  <div class="layout">
    <vTable ref="vTable" v-bind="vTableProps" @toolbarClick="handleToolbarClick" @formItemIconClick="handleIconClick"
      @formDataChange="handleFormDataChange" @cellDblclick="handleDblclick">
    </vTable>
    <CreateDialog ref="createRef" @refresh="handleSubmit"></CreateDialog>
  </div>
</template>
<script>
import vTable from '@/components/amtxts/vTable/vGrid.vue'
import CreateDialog from './create.vue'
import { qcItmpage, pdaEmpGrouplistTeam, qcItmdel, getTog, getTogto, groupdelTeam } from '@/api/salm'
export default {
  components: {
    vTable, CreateDialog
  },
  data() {
    return {
      vTableProps: {
        api_find: pdaEmpGrouplistTeam,
        api_delete: null,
        toolbarItems: [
          { label: '工作组', value: 'title' },
          { value: 'remove', visible: false },
        ],
        formDataRaw: [
          { field: 'workGroup.groupNo', type: 'input', },
        ],
        tableColumn: [
          { field: "groupNo", title: "workGroup.groupNo", },
          { field: "qtySum", title: "workGroup.qtySum", },
        ],
      }
    }
  },
  methods: {
    handleToolbarClick(params) {
      switch (params.code) {
        case 'create':
          this.$refs.createRef.handleVisible();
          break;
        default:
      }
    },
    handleIconClick(params) {
    },
    handleFormDataChange(params) {
    },
    handleChoose(params) {
    },
    handleSelectListEvent(params) {
    },
    handleDblclick(param) {
      this.$router.push({
        path: '/barcode/workGroup/detail',
        query: {
          groupNo: param.groupNo
        }
      })
    },
    handleSubmit() {
      this.$refs.vTable.handleGet();
    }
  },
}
</script>
