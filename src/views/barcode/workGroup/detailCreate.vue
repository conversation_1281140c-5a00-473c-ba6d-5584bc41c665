<template>
  <div>
    <!-- 新增弹窗 -->
    <el-drawer :title="$t(title)" :visible.sync="drawer" size="40%" ref="drawerRef" class="JustMake-drawer">
      <div class="drawer-content">
        <el-form ref="drawerFormRef" :model="formData" label-position="right" label-width="120px" class="drawer-form">
          <el-form-item :label="$t('workGroup.groupNo')">
            <el-input v-model="formData.groupNo" :disabled="true" style="width: 160px;"></el-input>
          </el-form-item>
        </el-form>
        <!-- 主表内容 -->
        <vxe-grid ref="gridRef" border stripe resizable show-overflow show-header-overflow height="97%"
                  :loading="loading" :checkbox-config="{ trigger: 'row', highlight: true, range: true }"
                  :columns="tableColumn" :data="tableData" :pager-config="tablePage" @page-change="handlePageChange">
        </vxe-grid>
      </div>
      <div class="drawer-footer">
        <el-button @click="handleCancel">{{ $t('public.cancel') }}</el-button>
        <el-button type="primary" @click="handleCreate" :loading="drawerLoading">{{ drawerLoading ? 'Loading ...' :
          $t('public.sure')
          }}</el-button>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { throttle } from 'lodash';
import Forms from '@/components/Forms/index.vue'
import { add, qcItmadd, edit, getTogto, salmquery, groupaddTeam } from '@/api/salm'
export default {
  components: {
    Forms,
  },
  data() {
    return {
      api_find: salmquery,
      api_save: groupaddTeam,
      drawer: false,
      drawerLoading: false,
      title: 'public.add',
      status: '0', // 0 新增 1 编辑
      formData: {},
      currentRow: {},
      tableColumn: [
        { type: "checkbox", fixed: "left", width: "50" },
        { field: "salNo", title: "workGroup.salNo", },
        { field: "name", title: "workGroup.name", },
      ],
      loading: false,
      tablePage: {
        layouts: ['Total', 'Sizes', 'PrevPage', 'JumpNumber', 'NextPage', 'FullJump'],
        total: 0,
        currentPage: 1,
        pageSize: 20
      },
      tableData: [],
    }
  },
  watch: {
    status: function (newVal) {
      if (newVal === '0') {
        this.title = 'public.add'
        this.api_save = groupaddTeam
      } else {
        this.title = 'public.edit'
        this.api_save = qcItmadd
      }
    }
  },
  mounted() {
    this.handleGet()
  },
  methods: {
    async handleGet() {
      this.loading = true;
      if (!this.api_find || typeof this.api_find !== 'function') {
        this.loading = false;
        return this.$message.error('请配置 api_find 参数');
      }
      try {
        const query = {
          current: this.tablePage.currentPage,
          size: this.tablePage.pageSize
        }
        const res = await this.api_find(query);
        this.tableData = res.data.records;
        this.tablePage.total = res.data.total;
        this.tablePage.currentPage = res.data.current;
        return res;
      } catch (err) {
        return Promise.reject(err);
      } finally {
        this.loading = false;
      }
    },
    handleVisible(param) {
      this.status = '0'
      this.formData = {}
      this.formData.groupNo = param
      this.$nextTick(() => {
        this.$refs.drawerFormRef.clearValidate()
      })
      this.drawer = !this.drawer
    },
    getPropName(field) {
      return field.includes('.') ? field.split('.').pop() : field;
    },
    handlePageChange({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.handleGet()
    },
    handleCancel() {
      this.formData = {}
      this.$nextTick(() => {
        this.$refs.drawerFormRef.clearValidate()
      })
      this.drawer = false
    },
    async handleCreate() {
      if (this.drawerLoading) {
        return;
      }
      if (!this.api_save || typeof this.api_save !== 'function') {
        return this.$message.error('请配置 api_save 参数');
      }
      // 表单校验
      try {
        await this.$refs.drawerFormRef.validate();
      } catch (error) {
        return;
      }
      let result = null;
      try {
        this.drawerLoading = true;
        const records = this.$refs.gridRef.getCheckboxRecords()
        if (records.length === 0) {
          this.$message.error('请选择数据')
          return
        }
        const queryParam = {
          groupNo: this.formData.groupNo,
          ygNos: records.map(item => item.salNo)
        }
        result = await this.api_save(queryParam);
        if (result.code === 0) {
          this.$message.success(this.$t('public.success'))
          this.$emit('refresh')
          this.drawer = false;
        }
      } catch (err) {
        this.$message.error(err || this.$t('public.error'));
      } finally {
        this.drawerLoading = false;
      }
    },
    handleEdit(row) {
      this.currentRow = row
      this.status = '1'
      this.drawer = true
      this.$nextTick(() => {
        this.formData = Object.assign({}, row) // 拷贝数据

      })
    },
  },

}
</script>
