<template>
  <div class="layout">
    <vTable ref="vTable" v-bind="vTableProps" @toolbarClick="handleToolbarClick">
    </vTable>
    <CreateDialog ref="createRef" @refresh="handleSubmit"></CreateDialog>
  </div>
</template>
<script>
import vTable from '@/components/amtxts/vTable/vGrid.vue'
import CreateDialog from './detailCreate.vue'
import { grouplistTeamCus,groupdelTeam } from '@/api/salm'
export default {
  components: {
    vTable, CreateDialog
  },
  data() {
    return {
      groupNo: '',
      vTableProps: {
        delete_key:'$ITEM',
        api_find: grouplistTeamCus,
        api_delete: groupdelTeam,
        toolbarItems: [
          { label: '工作组详情', value: 'title' },
          { value: 'edit', visible: false },
        ],
        formDataRaw: [
          { field: 'workGroup.ygNo', type: 'input', },
          { field: 'workGroup.name', type: 'input', },
          { field: 'groupNos', value:'', hidden: true },
        ],
        tableColumn: [
          { field: "ygNo", title: "workGroup.ygNo", },
          { field: "name", title: "workGroup.name", },
        ],
      }
    }
  },
  created() {
    this.groupNo = this.$route.query.groupNo
    this.vTableProps.formDataRaw[2].value = this.groupNo
  },
  methods: {
    handleToolbarClick(params) {
      switch (params.code) {
        case 'create':
          this.$refs.createRef.handleVisible(this.groupNo);
          break;
        default:
      }
    },
    handleSubmit() {
      this.$refs.vTable.handleGet();
    },
  },
}
</script>
