<template>
<div class="consrm">
  <div  style="text-align:right;margin-right:16px;position: absolute;top: 102px;right:0px;z-index:999">
    <a-button
                size='small'
                type="primary"
                style="margin-left:10px"
                v-if="permissions['addGroupN']"
                @click="add()"
              >{{ $t('public.add') }}</a-button>
              <!-- <a-button
                size='small'
                type="primary"
                style="margin-left:10px"
                v-if="permissions['basic_salm_add']"
               
                :loading1="loading1"
              >{{ $t('public.synchro') }}</a-button> -->
               <!-- @click="handletog" -->
                   
                    <!-- <a-button
                      size='small'
                      type="primary"
                      style="margin-left: 8px"
                      @click="dropdownMenuEvent('remove')"
                    >{{ $t('public.delete') }}</a-button> -->
                  <!-- <a-button
                      size='small'
                      type="primary"
                      style="margin-left: 8px"
                      @click="reset"
                    >{{ $t('public.reset') }}</a-button> -->
  </div>
  <el-tabs type="border-card">
      <el-tab-pane label="工作组">
  <a-card :bordered="false">
    <a-spin :spinning="spinning">
      <a-row :gutter="8">
        <!-- <a-col :span="4">
          <div style="height:600px;overflow:scroll">
          <my-tree
            ref="tree"
            @onSelect="onSelect"
            :url="props.url"
            :params="props.params"
          ></my-tree>
          </div>
        </a-col> -->
        <a-col :span="24">
          <div class="table-page-search-wrapper">
            <a-form layout="inline">
              <!-- <a-row :gutter="48">
                <a-col
                  :md="8"
                  :sm="24"
                >
                  <a-form-item label="原因代号">
                    <a-input
                      v-model="queryParam.spcNo"
                      placeholder="请输入原因代号"
                    />
                  </a-form-item>
                </a-col>
                <a-col
                  :md="8"
                  :sm="24"
                >
                  <a-form-item label="不合格原因描述">
                    <a-input
                      v-model="queryParam.name"
                      placeholder="请输入不合格原因描述"
                    />
                  </a-form-item>
                </a-col>
                <a-col
                  :md="8"
                  :sm="24"
                  style="margin-top:5px;"
                >
                  <span class="table-page-search-submitButtons">
                    
                   
                  </span>
                </a-col>
              </a-row> -->
              <a-button
                      size='small'
                      type="primary"
                      @click="search"
                    >{{ $t('public.query') }}</a-button>
            </a-form>
          </div>
          <vxe-toolbar custom>
            <template v-slot:buttons>
              <!-- <a-dropdown :trigger="['click']">
                <a-button size='small'>
                  {{ $t('public.action') }}
           
                </a-button>
                <a-menu slot="overlay">
                  <a-menu-item key="0">
                    <a @click="dropdownMenuEvent('remove')">{{ $t('public.delete') }}</a>
                  </a-menu-item>
                </a-menu>
              </a-dropdown> -->
              
            </template>
          </vxe-toolbar>

          <!-- <div v-for="(i,index) in tableData" :key="index">
            <div style="height:50px;border-bottom:1px solid #ccc;background-color:#ddd">
              <div style="display:inline-block;width:50%;">
                <span style="margin-right:5px;">组号</span>{{i.groupNo}} 
              </div>
              <div style="display:inline-block;width:50%;text-align:right">
                <span style="margin-right:5px;">总人数</span>{{i.qtySum}}
              </div>
              </div>
          </div> -->
          <vxe-table
            size='small'
            border
            resizable
            stripe
            highlight-current-row
            show-overflow
            highlight-hover-row
            export-config
            ref="xTable"
            :loading="loading"
            :data="tableData"
            :keyboard-config="{ isArrow: true }"
            @cell-dblclick="cellDBLClickEvent"
            :edit-config="{ trigger: 'click', mode: 'row' }"
          >
            <vxe-table-column
              type="radio"
              fixed="left"
              align="center"
              :width="50"
            ></vxe-table-column>
            <vxe-table-column
              field="groupNo"
              fixed="left"
              title="组号"
              align="center"
            ></vxe-table-column>
            <vxe-table-column
              field="qtySum"
              title="总人数"
              align="center"
            ></vxe-table-column>
          </vxe-table>
          <vxe-pager
            :loading="loading"
            :current-page="tablePage.currentPage"
            :page-size="tablePage.pageSize"
            :total="tablePage.total"
            :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
            @page-change="handlePageChange"
          ></vxe-pager>
        </a-col>
        <!-- 添加弹出框 -->
        <salm-drawer
          ref="modal"
          @onOk="onOk"
        />
      </a-row>
    </a-spin>
  </a-card>
      </el-tab-pane>
  </el-tabs>
</div>
</template>

<script>
import Vue from 'vue'
import { tenantList } from '@/api/login'
import { DEFAULT_TENANT_ID } from '@/store/mutation-types'
import { qcItmpage,pdaEmpGrouplistTeam, qcItmdel, getTog, getTogto,groupdelTeam } from '@/api/salm'
import { deptTree } from '@/api/admin/dept'
import salmDrawer from './salmDrawer'
import { mapGetters } from 'vuex'
import MyTree from '@/components/MyTree'
export default {
  name: 'SalmList',
  components: {
    salmDrawer,
    MyTree
  },
  data () {
    return {
      node: {},
      spinning: false,
      props: {
        // url: '/admin/dept/lazyTree',
         url: '/mes/qcItm/allQcitm',
        params: 'qcItm' // 索引
        // deptId
      },
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      loading: false,
      loading1: false,
      // 查询参数
      queryParam: {},
      tableData: []
    }
  },

  created () {
    this.getList()
  },
  computed: {
    ...mapGetters(['permissions'])
  },
  methods: {
    search () {
      this.tablePage.currentPage = 1
      this.getList()
    },
    // 人员同步
    async handletog () {
      this.loading1 = true
      this.spinning = true
      const TENANT_ID = Vue.ls.get(DEFAULT_TENANT_ID)
      const tenant = await tenantList()
      const newArr = tenant.data.find(i => {
        return i.value === TENANT_ID
      })
      const dataSourceVo = newArr.dataSourceVo
      if (JSON.stringify(dataSourceVo) !== '{}') {
        try {
          const res = await deptTree()
          if (res.data.length !== 0) {
            const res1 = await getTog()
            if (res1.data > 0) {
              const res2 = await getTogto()
              if (res2) {
                this.loading1 = false
                this.spinning = false
                this.$message.success(this.$t('public.success'))
                this.getList()
              } else {
                this.$message.error(this.$t('public.error'))
                this.loading1 = false
                this.spinning = false
              }
            } else {
              this.spinning = false
              this.loading1 = false
              this.$message.success(this.$t('public.success'))
              this.getList()
            }
          } else {
            this.spinning = false
            this.loading1 = false
            this.$notification.error({
              message: this.$t('public.del.title'),
              description: this.$t('public.dept_tog')
            })
          }
        } catch (err) {
          this.loading1 = false
          this.spinning = false
        }
      } else {
        this.loading = false
        this.spinning = false
        this.$notification.error({
          message: this.$t('public.del.title'),
          description: this.$t('public.set')
        })
      }
    },
    getList () {
      // fetchList
      this.loading = true
       pdaEmpGrouplistTeam(
        Object.assign(
          {
            groupNo:'',
            // qcItm: this.queryParam.qcItm,
            // name: this.queryParam.name,
            // current: this.tablePage.currentPage,
            // size: this.tablePage.pageSize
          }
        )
      )
        .then(res => {
     
          this.tableData = res.data
          this.tablePage.total = res.data.total
          this.tablePage.currentPage = res.data.current
          this.loading = false
        })
        .catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    // 分页
    handlePageChange ({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getList()
    },
    onSelect (node) {

   
      // this.queryParam.dep = node.dataRef.deptCode
      this.queryParam.qcItm = node.dataRef.qcItm
      this.node = node
      this.getList()
    },
    // 重置搜索内容
    reset () {
      this.node = ''
      this.queryParam = {}
      // this.$refs.tree.onLoadData(this.node)
      this.getList()
    },
    // 新增
    add () {
      // const obj = {
      //   id: this.node.dataRef === undefined ? '' : this.node.dataRef.id,
      //   name: this.node.dataRef === undefined ? '' : this.node.dataRef.name
      // }
      this.$refs.modal.create({ title: this.$t('public.add') }, this.groupNo)
    },
    // add () {
    //   const obj = {
    //     id: this.node.dataRef === undefined ? '' : this.node.dataRef.id,
    //     name: this.node.dataRef === undefined ? '' : this.node.dataRef.name
    //   }
    //   this.$refs.modal.create({ title: this.$t('public.add') }, obj)
    // },
    // 双击弹出编辑框
    cellDBLClickEvent ({ row }) {
 
      this.$router.push({path: '/barcode/workGroup/detail',
          query:{
            groupNo: row.groupNo
          }
        })
      // this.$refs.modal.edit({ title: this.$t('public.Detailed') }, row)
    },
    onOk () {
      this.reset()
      // this.$refs.tree.onLoadData(this.node)
    },
    // 删除按钮
    dropdownMenuEvent (name) {
      switch (name) {
        case 'remove': {
           const selectRecords = this.$refs.xTable.getRadioRecord()
          if (selectRecords) {
            // const arr = []
            // selectRecords.forEach(i => {
            //   return arr.push(i.qcItm)
            // })
            const that = this
            this.$confirm({
              title: this.$t('public.del.title'),
              content: this.$t('public.del.content'),
              okText: this.$t('public.sure'),
              okType: 'danger',
              cancelText: this.$t('public.cancel'),
              onOk () {
                // delAll 
                that.loading = true
                groupdelTeam({ygNo:selectRecords.ygNo,groupNo:selectRecords.groupNo})
                  .then(() => {
                    that.getList()
                    that.loading = false
                    // that.$refs.tree.getTree()
                    that.$message.success(that.$t('public.success'))
                  })
                  .catch(err => that.requestFailed(err))
                  .finally(() => {
                    that.loading = false
                  })
              },
              onCancel () {
                that.loading = false
              }
            })
          } else {
            this.$message.warning(this.$t('public.list'))
          }
          break
        }
      }
    }

  }
}
</script>
<style lang="less">
</style>
