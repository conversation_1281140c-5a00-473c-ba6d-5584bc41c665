<template>
  <div v-show="false">
    <div ref="print">
      <div
        v-for="(item, index) in data"
        :key="index"
        style="padding: 40px 20px 20px 20px; position: relative"
        id="showPrint"
      >
        <div :id="`qrcode${index}`"></div>
        <div style="position: absolute; right: 20px; top: 38px">
          <div style="display: flex">
            <div style="height: 25px; width: 80px">下单日期：</div>
            <div style="border-bottom: 1px solid #dbdbdb; width: 140px; height: 25px">{{ item.pgDd }}</div>
          </div>
          <div style="display: flex; margin-top: 5px">
            <div style="height: 25px; width: 80px">预计完工：</div>
            <div style="border-bottom: 1px solid #dbdbdb; width: 140px; height: 25px">{{ item.endDd }}</div>
          </div>
          <div style="display: flex; margin-top: 5px">
            <div style="height: 25px; width: 80px">作业人员：</div>
            <div style="border-bottom: 1px solid #dbdbdb; width: 140px; height: 25px">{{ item.name }}</div>
          </div>
        </div>
        <div style="display: flex; margin: 20px 0px" v-for="(i, index) in textList" :key="index">
          <div>{{ i.name }}：</div>
          <div style="border-bottom: 1px solid #dbdbdb; width: 80%">{{ item[i.field] }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import printHtml from '@/utils/print.js'
import QRCode from 'qrcodejs2'
export default {
  data() {
    return {
      textList: [
        {
          name: '工 序 号',
          field: 'zcNo2'
        },
        {
          name: '工 序 名',
          field: 'zcName2'
        },
        {
          name: '成品代号',
          field: 'prdNo'
        },
        {
          name: '成品名称',
          field: 'prdName'
        },
        {
          name: '成品规格',
          field: 'spc'
        },
        {
          name: '非标规格',
          field: 'prdMark'
        },
        {
          name: '生产数量',
          field: 'pgQty'
        },
        {
          name: '制令单号',
          field: 'moNo2'
        },
        {
          name: '派工单号',
          field: 'pgNo'
        }
      ],
      timer: null,
      data: [],
      type: [undefined, null, '']
    }
  },
  destroyed() {
    this.timer = null
  },
  methods: {
    createPrint(data) {
      this.$nextTick(() => {
        this.data = data
        for (let i = 0; i < this.data.length; i++) {
          let str = 'qrcode' + i
          if (
            !this.type.includes(document.getElementById(str)) &&
            !this.type.includes(document.getElementById(str).innerHTML)
          ) {
            document.getElementById(str).innerHTML = ''
          }
        }
      })
      if (this.timer) clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.$nextTick(() => {
          let pro = []
          for (let i = 0; i < this.data.length; i++) {
            pro[i] = new Promise(resolve => {
              let index = 'qrcode' + i
              new QRCode(index, {
                width: 90,
                height: 90,
                text: this.data[i].pgNo
              })
              let dom = `#qrcode${i} img`
              document.querySelector(dom).onload = () => {
                resolve()
              }
            })
          }
          Promise.all(pro).then(() => {
            let printData = this.$refs.print.innerHTML
            printHtml(printData)
          })
        })
      }, 1)
    }
  }
}
</script>

<style scoped>
#showPrint {
  page-break-before: always;
}
@media print {
  #gfvrgh {
    page-break-before: always;
  }
}

@page {
  margin: 0;
}
</style>
