<template>
<div class="consrm">
<div  style="text-align:right;margin-right:16px;position: absolute;top: 95px;right:0px;z-index:999">
  <a-button
                  style="margin-left: 8px"
                  type="primary"
                  @click="reportWork"
                  v-permission="barcode_wmszcpg_submit"
                  >提交派工</a-button
                >
                <a-button
                  style="margin-left: 8px"
                  type="primary"
                  @click="handlePrint"
                  v-permission="barcode_wmszcpg_print"
                  >打印</a-button
                >
 <a-button type="primary" style="margin-left: 8px" @click="handleAdd()" v-permission="barcode_wmszcpg_reset">
                 {{ $t('public.add') }}</a-button>
                 <a-button type="primary" style="margin-left: 8px" @click="handleDel()" v-permission="barcode_wmszcpg_reset">
                 {{ $t('public.delete') }}</a-button>


                <a-button type="primary" style="margin-left: 8px" @click="reset" v-permission="barcode_wmszcpg_reset">{{
                  $t('public.reset')
                }}</a-button>
         
           
 
  </div>
<el-tabs type="border-card">
      <el-tab-pane label="工序派工">
  <a-card :bordered="false">
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row>
          <a-col :span="7">
            <a-form-item label="制令日期">
              <a-date-picker
                v-model="staDd"
                :disabled-date="disabledStartDate"
                format="YYYY-MM-DD"
                :placeholder="$t('information.staDd')"
                @openChange="handleStartOpenChange"
                style="width: 45%"
              />
              <a-date-picker
                v-model="endDd"
                :disabled-date="disabledEndDate"
                format="YYYY-MM-DD"
                :placeholder="$t('information.endDd')"
                :open="endOpen"
                @openChange="handleEndOpenChange"
                style="width: 45%; margin-left: 8px"
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="部门">
              <a-select
                placeholder="请选择部门"
                v-model="dep"
                allowClear
                show-search
                :filter-option="false"
                @search="searchDep"
                @focus="handleSearchDeps"
                style="width: 90%"
              >
                <a-select-option v-for="(i, index) in depList" :key="index" :value="i.dep">
                  {{ i.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="制令单">
              <a-input v-model="moNo" style="width: 90%;" />
            </a-form-item>
          </a-col>
          <a-col :span="5">
            <a-form-item label="制程">
              <a-select
                :allowClear="true"
                :placeholder="$t('information.placeholder.zcNo')"
                :filterOption="false"
                @focus="handle_zcNo"
                v-model="zcNos"
                style="width: 100%"
              >
                <a-select-option v-for="(i, index) in ZcList" :key="index" :value="i.zcNo"
                  >{{ i.zcNo }}{{ i.zcName }}</a-select-option
                >
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <div class="wmszcpg-padding">
          <a-row>
            <a-col :span="3">
              <a-form-item label="通知结案否">
                <a-checkbox v-model="jaFlag"></a-checkbox>
              </a-form-item>
            </a-col>
            <a-col :span="5">
              <a-form-item label="已派工数据显示否">
                <a-checkbox v-model="pgFlag"></a-checkbox>
              </a-form-item>
            </a-col>
            <a-col :span="14" style="float:right;text-align:right;">
              <span>
                <a-button type="primary" @click="getList" v-permission="barcode_wmszcpg_search">{{
                  $t('public.query')
                }}</a-button>
                
              </span>
            </a-col>
          </a-row>
        </div>
      </a-form>
    </div>
    <vxe-toolbar>
      <template v-slot:buttons>
        <a-dropdown :trigger="['click']">
          <a-button
            >{{ $t('public.action') }}
            <a-icon type="down" />
          </a-button>
          <a-menu slot="overlay">
            <a-menu-item key="0">
              <a @click="handleAdd()">{{ $t('public.add') }}</a>
            </a-menu-item>
            <a-menu-item key="1">
              <a @click="handleDel()">{{ $t('public.delete') }}</a>
            </a-menu-item>
          </a-menu>
        </a-dropdown>
      </template>
    </vxe-toolbar>
    <vxe-table
      border
      show-overflow
      size="mini"
      row-id="id"
      resizable
      highlight-hover-row
      class="mytable-style"
      show-header-overflow
      @cell-click="cellclick"
      :loading="spinning"
      :height="tableHeight"
      :cell-class-name="cellClassName"
      :row-class-name="rowClassName"
      :scroll-x="{ enabled: false }"
      :scroll-y="{ enabled: false }"
      :edit-config="{ trigger: 'click', mode: 'cell' }"
      :tree-config="{
        lazy: true,
        children: 'tzList',
        hasChild: 'hasChild',
        expandRowKeys: defaultExpandRowKeys,
        loadMethod: loadChildrenMethod
      }"
      :data="tableData"
      ref="xTable"
      v-if="isShowTable"
    >
      <vxe-table-column type="checkbox" width="50" align="center" fixed="left"></vxe-table-column>
      <vxe-table-column field="moNo" sortable title="制令单" align="center" fixed="left" width="120">
        <template slot-scope="scope">
          <span style="color:black;font-weight:700">
            {{ scope.row.moNo }}
          </span>
        </template>
      </vxe-table-column>
      <vxe-table-column field="prdNo" title="品号" align="center" width="120">
        <template slot-scope="scope">
          <span style="color:black;font-weight:700" v-if="scope.row.moNo">
            {{ scope.row.prdNo }}
          </span>
        </template>
      </vxe-table-column>
      <vxe-table-column field="prdNo" title="品名" align="center" width="120">
        <template slot-scope="scope">
          <span style="color:black;font-weight:700" v-if="scope.row.moNo">
            {{ scope.row.prdName }}
          </span>
        </template>
      </vxe-table-column>
      <vxe-table-column field="prdNo" title="规格" align="center" width="80">
        <template slot-scope="scope">
          <span style="color:black;font-weight:700" v-if="scope.row.moNo">
            {{ scope.row.spc }}
          </span>
        </template>
      </vxe-table-column>
      <vxe-table-column field="prdMark" title="特征" align="center" width="80">
        <template slot-scope="scope">
          <span style="color:black;font-weight:700">
            {{ scope.row.prdMark }}
          </span>
        </template>
      </vxe-table-column>
      <vxe-table-column align="center" field="zcNo" title="制程" tree-node width="160">
        <template slot-scope="scope" v-if="scope.row.zcNo && scope.row.zcName">
          <span style="color:black;font-weight:700"> {{ scope.row.zcNo }}{{ scope.row.zcName }} </span>
        </template>
      </vxe-table-column>
      <vxe-table-column field="tzNo" align="center" title="通知单号" width="120">
        <template slot-scope="scope" v-if="!scope.row.zcNo2 && !scope.row.zcName2">
          <span style="color:black;font-weight:700">
            {{ scope.row.tzNo }}
          </span>
        </template>
      </vxe-table-column>
      <vxe-table-column field="qty" align="center" title="数量" width="120">
        <template slot-scope="scope" v-if="!scope.row.zcNo2 && !scope.row.zcName2">
          {{ scope.row.qty }}
        </template>
      </vxe-table-column>
      <vxe-table-column field="zcNo2" align="center" title="二级制程" width="180">
        <template slot-scope="scope" v-if="scope.row.zcNo2 && scope.row.zcName2">
          {{ scope.row.zcNo2 }}{{ scope.row.zcName2 }}
        </template>
      </vxe-table-column>
      <vxe-table-column field="pgQty" align="center" title="派工数量" width="120">
        <template slot-scope="scope" v-if="scope.row.zcNo2 && scope.row.zcName2">
          <vxe-input v-model="scope.row.pgQty" @change="pgQtyChange" type="number"></vxe-input>
          <!-- <a-input-number :min="0" :max="99999999" v-model="scope.row.pgQty" @change="pgQtyChange"/> -->
        </template>
      </vxe-table-column>
      <vxe-table-column field="ygNoName" align="center" title="作业人员" width="120">
        <template slot-scope="scope">
          <div v-if="scope.row.zcNo2 && !scope.row.ygNoName">
            <a-icon style="float:right" type="database" />
          </div>
          <div v-else>
            {{ scope.row.ygNoName }}
          </div>
        </template>
      </vxe-table-column>
      <vxe-table-column field="staDd" align="center" title="预计开工时间" width="170">
        <template slot-scope="scope">
          <div v-if="scope.row.zcNo2 && scope.row.zcName2" class="time_width">
            <a-date-picker
              v-model="scope.row.staDd"
              showTime
              format="YYYY-MM-DD HH:mm:ss"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              @change="pgQtyChange"
              style="min-width: 150px;"
            />
          </div>
        </template>
      </vxe-table-column>
      <vxe-table-column field="endDd" align="center" title="预计完工时间" width="170">
        <template slot-scope="scope">
          <div v-if="scope.row.zcNo2 && scope.row.zcName2" class="time_width">
            <a-date-picker
              v-model="scope.row.endDd"
              showTime
              format="YYYY-MM-DD HH:mm:ss"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              @change="pgQtyChange"
              style="min-width: 150px;"
            />
          </div>
        </template>
      </vxe-table-column>
      <vxe-table-column field="prdNo" title="工程案号" align="center" width="120">
        <template slot-scope="scope">
          <span style="color:black;font-weight:700" v-if="scope.row.moNo">
            {{ scope.row.casNo }}
          </span>
        </template>
      </vxe-table-column>
      <vxe-table-column field="xqr" align="center" title="需求日" width="120">
        <template slot-scope="scope">
          <span style="color:black;font-weight:700" v-if="scope.row.moNo && scope.row.xqr">
            {{ moment(scope.row.xqr).format('YYYY-MM-DD') }}
          </span>
        </template>
      </vxe-table-column>
      <vxe-table-column field="sebei" align="center" title="设备" width="140">
        <template slot-scope="scope">
          <div v-if="scope.row.zcNo2 && !scope.row.sebei">
            <a-icon style="float:right" type="database" />
          </div>
          <div v-else>{{ scope.row.sebei }}{{ scope.row.sebName }}</div>
        </template>
      </vxe-table-column>
      <vxe-table-column field="pgNo" align="center" title="派工单号" width="120"></vxe-table-column>
      <vxe-table-column align="center" title="派工人" width="100">
        <template slot-scope="scope">
          <div v-if="scope.row.zcNo2 && scope.row.zcName2">
            {{ scope.row.usrPg }}
          </div>
        </template>
      </vxe-table-column>
      <vxe-table-column field="pgDd" align="center" title="派工日期" width="140"></vxe-table-column>
      <vxe-table-column align="center" title="通知部门" width="120">
        <template slot-scope="scope">
          <div v-if="scope.row.zcNo2 && scope.row.zcName2">
            {{ scope.row.depName }}
          </div>
        </template>
      </vxe-table-column>
    </vxe-table>
    <Modal ref="modal" @touch="touch" />
    <Print ref="Print" />
  </a-card>
   </el-tab-pane>
</el-tabs>
</div>
</template>

<script>
import { findList, findZc2, getZc, getSalm, add, delPg, getCas, getDep } from '@/api/report/dispatch'
import Modal from './modal'
import Print from './print'
import moment from 'moment'
export default {
  components: {
    Modal,
    Print
  },
  data() {
    return {
      barcode_wmszcpg_search: 'barcode_wmszcpg_search',
      barcode_wmszcpg_submit: 'barcode_wmszcpg_submit',
      barcode_wmszcpg_reset: 'barcode_wmszcpg_reset',
      barcode_wmszcpg_print: 'barcode_wmszcpg_print',
      sureDelText: '确认删除派工？',
      spinning: false,
      endD: false,
      tableData: [],
      tableHeight: window.innerHeight - 280,
      types: [null, undefined, ''],
      date: moment(new Date()).format('YYYY-MM-DD HH:MM:SS'),
      status: true,
      endOpen: false,
      loading: false,
      isShowTable: true,
      jaFlag: false,
      pgFlag: false,
      staDd: null,
      endDd: null,
      start: null,
      endDate: null,
      ZcList: [],
      zcNos: '',
      casNos: [],
      defaultExpandRowKeys: [],
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      type: {
        zcNo2: false
      },
      queryParam: {},
      csaList: [],
      moNo: '',
      dep: '',
      depList: []
    }
  },
  mounted() {
    this.staDd = moment().subtract(7, 'd')
    this.endDd = moment(new Date())
    window.onresize = () => {
      return (() => {
        this.tableHeight = window.innerHeight - 280
      })()
    }
  },
  methods: {
    moment,
    // 递归便利方法
    Zctwo(arr, id, obj) {
      arr.forEach(e => {
        if (e.id === id) {
          Object.assign(e, obj)
        } else {
          if (e.tzList && e.tzList.length > 0) {
            this.Zctwo(e.tzList, id, obj)
          }
        }
      })
      return arr
    },
    handleSearchDeps(val) {
      getDep({
        key: val
      })
        .then(res => {
          this.depList = res.data
        })
        .catch(err => this.requestFailed(err))
    },
    searchDep(val) {
      this.handleSearchDeps(val)
    },
    cellclick({ columnIndex, row }) {
      let urls = ''
      let tableColumn = []
      switch (columnIndex) {
        case 11: {
          if (row.zcNo2) {
            tableColumn = [
              { type: 'checkbox', align: 'center', width: 50 },
              { field: 'salNo', title: 'public.numb', align: 'center', width: '30%' },
              { field: 'depName', title: '部门', align: 'center' },
              { field: 'salName', title: '人员', align: 'center' }
            ]
            urls = '/barcode/scpg/salm'
            this.$refs.modal.create({ title: '作业人员' }, tableColumn, urls, row, row.moNo2, { multiple: true })
          }
          break
        }
        case 16: {
          if (row.zcNo2) {
            tableColumn = [
              { type: 'radio', align: 'center', width: 50 },
              { field: 'sebNo', title: 'public.numb', align: 'center', width: '30%' },
              { field: 'sebName', title: 'public.name', align: 'center' }
            ]
            urls = '/barcode/scpg/sebei'
            this.$refs.modal.create({ title: '设备' }, tableColumn, urls, row, undefined, { multiple: false })
          }
          break
        }
      }
    },
    touch(data, reset) {
      let obj = {}
      reset
        ? data.type === 1
          ? (obj = { sebei: '', sebName: '' })
          : (obj = { ygNoName: '', ygNos: '' })
        : data.type === 1
        ? (obj = { sebei: data.sebei, sebName: data.sebName })
        : (obj = { ygNoName: data.ygNoName, ygNos: data.ygNos })
      this.isResetAddData(data, obj)
    },
    isResetAddData(data, obj) {
      for (let i = 0; i < this.tableData.length; i++) {
        if (this.tableData[i].tzList && this.tableData[i].tzList.length > 0) {
          for (let j = 0; j < this.tableData[i].tzList.length; j++) {
            if (this.tableData[i].tzList[j].tzList && this.tableData[i].tzList[j].tzList.length > 0) {
              for (let e = 0; e < this.tableData[i].tzList[j].tzList.length; e++) {
                if (this.tableData[i].tzList[j].tzList[e].id === data.id) {
                  if (data.type === 1) {
                    this.tableData[i].tzList[j].tzList[e].sebei = obj.sebei
                    this.tableData[i].tzList[j].tzList[e].sebName = obj.sebName
                  } else {
                    this.tableData[i].tzList[j].tzList[e].ygNoName = obj.ygNoName
                    this.tableData[i].tzList[j].tzList[e].ygNos = obj.ygNos
                  }
                  break
                }
              }
            }
          }
        }
      }
      this.$forceUpdate()
    },
    // 懒加载树节点(查询二级制程)
    loadChildrenMethod({ row }) {
      const obj = {
        ...this.getMoNo(row),
        tzNo: row.tzNo,
        zcNo: row.zcNo,
        qty: row.qty,
        depNo: row.depNo,
        depName: row.depName,
        pgFlag: this.pgFlag ? 'T' : 'F'
      }
      return new Promise((resolve, reject) => {
        findZc2(obj)
          .then(res => {
            resolve(res.data)
          })
          .catch(err => {
            this.requestFailed(err)
          })
      })
    },
    getMoNo(row) {
      let resObj = {}
      for (let i = 0; i < this.tableData.length; i++) {
        if (this.tableData[i].tzList && this.tableData[i].tzList.length > 0) {
          for (let j = 0; j < this.tableData[i].tzList.length; j++) {
            if (this.tableData[i].tzList[j].id === row.tzNo) {
              resObj = {
                moNo: this.tableData[i].moNo,
                prdNo: this.tableData[i].prdNo,
                prdMark: this.tableData[i].prdMark,
                xqr: this.tableData[i].xqr,
                spc: this.tableData[i].spc,
                prdName: this.tableData[i].prdName,
                casNo: this.tableData[i].casNo,
                casName: this.tableData[i].casName
              }
              break
            }
          }
        }
      }
      return resObj
    },
    pgQtyChange() {
      this.$forceUpdate()
    },
    disabledStartDate(startValue) {
      const endValue = this.endDd
      if (!startValue || !endValue) {
        return false
      }
      return startValue.valueOf() > endValue.valueOf()
    },
    disabledEndDate(endValue) {
      const startValue = this.staDd
      if (!endValue || !startValue) {
        return false
      }
      return startValue.valueOf() >= endValue.valueOf()
    },
    // 制程
    handle_zcNo() {
      getZc().then(res => {
        this.ZcList = res.data
      }).catch(err => this.requestFailed(err))
    },
    // 变色突出行
    cellClassName({ row, rowIndex, column, columnIndex }) {
      if (column.property === 'ygNoName' && row.ygNoName) {
        return 'col-red'
      }
    },
    rowClassName({ row, rowIndex }) {
      if (row.moNo) {
        return 'row-green'
      }
    },
    // 查询列表
    getList() {
      this.tableData = []
      this.defaultExpandRowKeys = []
      this.spinning = true
      const pramaobj = {
        staDd: !this.types.includes(this.staDd) ? moment(this.staDd).format('YYYY-MM-DD 12:00:00') : null,
        endDd: !this.types.includes(this.endDd) ? moment(this.endDd).format('YYYY-MM-DD 12:00:00') : null,
        moNo: this.moNo,
        dep: this.dep,
        zcNos: !this.types.includes(this.zcNos) ? [this.zcNos] : [],
        jaFlag: this.jaFlag ? 'T' : 'F'
      }
      findList(pramaobj)
        .then(res => {
          this.isShowTable = false
          this.tableData = res.data
          this.defaultExpandRowKeys = res.data.map(i => i.id)
          this.$nextTick(() => {
            this.isShowTable = true
            this.spinning = false
          })
        })
        .catch(err => {
          this.spinning = false
          this.requestFailed(err)
        })
    },
    handleStartOpenChange(open) {
      if (!open) {
        this.endOpen = true
      }
    },
    handleEndOpenChange(open) {
      this.endOpen = open
    },
    reset() {
      this.dep = ''
      this.moNo = ''
      this.zcNos = ''
      this.jaFlag = false
      this.pgFlag = false
      this.staDd = null
      this.endDd = null
    },
    handleAdd() {
      const selectArr = this.$refs.xTable.getCheckboxRecords()
      if (selectArr.length === 0) return this.$message.warning('请至少选择一条数据！')
      const tzNosArr = []
      selectArr.forEach(i => {
        if (!i.tzList && i.zcNo2 && i.zcName2) {
          tzNosArr.push(
            Object.assign(
              {},
              {
                id: i.id,
                tzNo: i.tzNo,
                zcNo2: i.zcNo2,
                zcName2: i.zcName2,
                moNo2: i.moNo2,
                xqr: i.xqr,
                qty: i.qty,
                prdNo: i.prdNo,
                prdName: i.prdName,
                prdMark: i.prdMark,
                casNo: i.casNo,
                casName: i.casName,
                depNo: i.depNo,
                depName: i.depName,
                pgDd: null,
                pgNo: null,
                pgQty: null,
                staDd: null,
                endDd: null,
                ygNoName: null,
                ygNos: null,
                ygNo: null,
                sebNo: null,
                sebei: null,
                sebName: null
              }
            )
          )
        }
      })
      if (tzNosArr.length === 0) return this.$message.warning('请选择二级制程！')

      for (let i = 0; i < this.tableData.length; i++) {
        if (this.tableData[i].tzList && this.tableData[i].tzList.length > 0) {
          for (let j = 0; j < this.tableData[i].tzList.length; j++) {
            if (this.tableData[i].tzList[j].tzList && this.tableData[i].tzList[j].tzList.length > 0) {
              for (let e = 0; e < this.tableData[i].tzList[j].tzList.length; e++) {
                for (let c = 0; c < tzNosArr.length; c++) {
                  if (this.tableData[i].tzList[j].tzList[e].id == tzNosArr[c].id) {
                    let obj = JSON.parse(JSON.stringify(tzNosArr[c]))
                    let time = new Date().getTime() + c
                    obj.id = time.toString()
                    this.tableData[i].tzList[j].tzList.push(obj)
                    break
                  }
                }
              }
            }
          }
        }
      }
      this.$nextTick(() => {
        this.$forceUpdate()
        this.$refs.xTable.clearCheckboxRow()
      })
    },
    handleDel() {
      const that = this
      const selectDelArr = this.$refs.xTable.getCheckboxRecords()
      if (selectDelArr.length === 0) return this.$message.warning('请至少选择一条数据！')
      const filterArr = selectDelArr.filter(i => i.pgNo)
      if (filterArr.length === 0) return this.$message.warning('没有可删除的已派工！')
      const pgNos = filterArr.map(i => i.pgNo)
      this.$confirm({
        title: this.sureDelText,
        okText: this.$t('public.sure'),
        okType: 'warn',
        cancelText: this.$t('public.cancel'),
        onOk() {
          delPg(pgNos)
            .then(res => {
              that.$message.success(that.$t('public.success'))
              that.getList()
            })
            .catch(err => {
              that.requestFailed(err)
            })
        },
        onCancel() {}
      })
    },
    // 保存派工
    reportWork() {
      let arr = this.tableData
      let that = this
      let newArr = []
      let value = Zctwo(arr, newArr)
      if (value && value.length > 0) {
        add(value)
          .then(res => {
            if (res) {
              that.$message.success(that.$t('public.success'))
              that.getList()
            }
          })
          .catch(err => {
            that.requestFailed(err)
          })
      } else {
        this.$notification['error']({
          message: this.$t('public.message'),
          description: '请先选择作业人员后提交派工！'
        })
      }
      function Zctwo(arr, newArr) {
        arr.forEach(e => {
          if (e.ygNoName && e.pgQty && !e.pgNo) {
            newArr.push(e)
          } else {
            if (e.tzList && e.tzList.length > 0) {
              Zctwo(e.tzList, newArr)
            }
          }
        })
        return newArr
      }
    },
    handlePrint() {
      const selectPrintArr = this.$refs.xTable.getCheckboxRecords()
      if (selectPrintArr.length === 0) return this.$message.warning('请至少选择一条数据！')
      const data = selectPrintArr.filter(i => i.zcNo2 && i.zcName2 && i.pgNo)
      if (data.length === 0) return this.$message.warning('请至少选择一条有派工单号的数据！')
      data.forEach(i => {
        i.name = ''
        if (i.ygNos != '' && i.ygNos != null) {
          let names = i.ygNos.split(',')
          names.forEach((n, index) => {
            let m = i.ygNoName.split(',')[index].split(n)[1]
            i.name = i.name ? i.name + ',' + m : m
          })
        }
        this.tableData.forEach(j => {
          if (i.moNo2 === j.moNo) {
            i.prdNo = j.prdNo
            i.prdName = j.prdName
            i.spc = j.spc
            i.prdMark = j.prdMark
          }
        })
      })
      this.$refs.Print.createPrint(data)
    }
  }
}
</script>

<style lang="css">
.mytable-style.vxe-table .vxe-header--column.col-blue {
  background-color: #2db7f5;
  color: #fff;
}
.mytable-style.vxe-table .vxe-body--column.col-red {
  background-color: red;
  color: #fff;
}
.mytable-style.vxe-table .vxe-body--column.col-orange {
  background-color: #cccccc;
  color: #fff;
}
.mytable-style .vxe-body--row.row-green {
  background-color: #cccccc;
  color: #fff;
}
.salesNo /deep/ .ant-select-selection {
  position: relative !important;
  z-index: 999 !important;
}
.time_width .ant-calendar-picker {
  min-width: 140px !important;
}
.wmszcpg-padding .ant-form-item-label {
  padding-right: 0px !important;
}
</style>
