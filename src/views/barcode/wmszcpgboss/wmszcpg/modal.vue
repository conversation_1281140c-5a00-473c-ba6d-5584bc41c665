<template>
  <el-dialog
    :title="title"
    :destroy-on-close="true"
    :width="'50%'"
    :visible.sync="visible"
    :loading="confirmLoading"
    @close="handleCancel"
  >
    <div style="margin-bottom: 10px;" v-if="multiple">
      <el-checkbox v-model="flag">
        是否显示下级部门人员
      </el-checkbox>
      <el-button type="primary" size="small" @click="getList">{{ $t('public.query') }}</el-button>
    </div>
    <vxe-grid
      border
      size="medium"
      max-height="500"
      resizable
      show-overflow
      highlight-hover-row
      highlight-current-row
      @cell-click="cellClickEvent"
      @page-change="handlePageChange"
      @form-submit="getList"
      :radio-config="{ labelField: '', trigger: 'row', highlight: true }"
      :checkbox-config="{ trigger: 'row', highlight: true, range: true }"
      :seq-config="{ startIndex: (tablePage.currentPage - 1) * tablePage.pageSize }"
      :columns="tableColumn"
      :data="tableData"
      ref="xTable"
    ></vxe-grid>
    <!-- :pager-config="tablePage" -->
    <!-- :form-config="tableForm" -->
    <template slot="footer">
      <el-button key="ok" @click="save">{{ $t('public.sure') }}</el-button>
      <el-button @click="handleReset">清空</el-button>
      <el-button key="cancel" @click="handleCancel">{{ $t('public.cancel') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import axios from '@/router/axios'
export default {
  name: 'SelectModal',
  data() {
    return {
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
        pageSizes: [10, 20, 50, 100, 200, 500],
        layouts: ['Sizes', 'PrevPage', 'Number', 'NextPage', 'Total'],
        perfect: true
      },
      id: '',
      multiple: false,
      obj: {},
      row: {},
      tableColumn: [],
      urls: '',
      moNo: '',
      confirmLoading: false,
      field: '',
      cellValue: '',
      title: '',
      visible: false,
      flag: false,
      tableData: []
    }
  },
  methods: {
    getList() {
      this.tableData = []
      axios({
        url: this.urls,
        method: 'get',
        params: Object.assign({
          moNo: this.moNo,
          tzNo: this.obj.tzNo,
          flag: this.multiple ? this.flag ? 'T' : 'F' : undefined
        })
      })
        .then(res => {
          if (Array.isArray(res.data)) {
            this.tableData = res.data
            this.multiple ? this.setCheck() : this.setRadio()
          }
        })
        .catch(err => {
          this.tableData = []
          this.requestFailed(err)
        })
    },
    handlePageChange({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getList()
    },
    setCheck() {
      if (this.obj.ygNos) {
        const showList = []
        const ygNos = this.obj.ygNos.split(',')
        this.tableData.forEach(i => {
          ygNos.forEach(e => {
            if (i.salNo === e) {
              showList.push(i)
            }
          })
        })
        this.$refs.xTable.setCheckboxRow([...showList], true)
      }
    },
    setRadio() {
      if (this.obj.sebei) {
        const row = this.tableData.find(i => i.sebNo === this.obj.sebei)
        if (row) this.$refs.xTable.setRadioRow(row)
      }
    },
    cellClickEvent({ row }) {
      this.row = row
    },
    create(model, tableColumn, urls, row, moNo, multiple) {
      this.multiple = multiple.multiple
      this.obj = row
      this.urls = urls
      this.moNo = moNo
      this.tableColumn = tableColumn
      this.title = model.title
      this.visible = true
      this.getList()
    },
    // 添加确认
    save() {
      if (JSON.stringify(this.row) === '{}') return this.handleCancel()
      this.multiple ? this.multipleChoice() : this.singleChoice()
    },
    multipleChoice() {
      const name = []
      const ygNos = []
      const selectRecords = this.$refs.xTable.getCheckboxRecords()
      selectRecords.forEach(i => {
        name.push(i.salNo + i.salName)
        ygNos.push(i.salNo)
      })
      this.$emit('touch', { type: 2, ygNoName: name.join(','), ygNos: ygNos.join(','), id: this.obj.id })
      this.handleCancel()
    },
    singleChoice() {
      this.$emit('touch', {
        type: 1,
        sebei: this.row.sebNo,
        sebNo: this.row.sebNo,
        sebName: this.row.sebName,
        id: this.obj.id
      })
      this.handleCancel()
    },
    handleReset() {
      let reset = true
      this.multiple
        ? this.$emit('touch', { type: 2, id: this.obj.id }, reset)
        : this.$emit('touch', { type: 1, id: this.obj.id }, reset)
      this.visible = false
    },
    handleCancel() {
      this.row = {}
      this.visible = false
    }
  }
}
</script>
