<template>
  <div class="layout">
    <vTable ref="vTable" v-bind="vTableProps" @toolbarClick="handleToolbarClick">
    </vTable>
    <Export ref="exportRef"></Export>
  </div>
</template>
<script>
import vTable from '@/components/amtxts/vTable/vGrid.vue'
import { getDep, queryCas, gxlist2, findPgWgTj } from '@/api/report/dispatch'
import Export from '@/components/barcodeExport/barcodeExport'
export default {
  components: {
    vTable, Export
  },
  data() {
    return {
      queryResult: [],
      vTableProps: {
        api_find: findPgWgTj,
        api_delete: null,
        toolbarItems: [
          { label: '工序派工完工统计表', value: 'title' },
          { label: '导出', value: 'export', },
        ],
        formDataRaw: [
          {
            field: 'reWork.staDd', type: 'datePicker',
            placeholder: this.$t('information.staDd')
          },
          {
            field: 'reWork.endDd', type: 'datePicker',
            placeholder: this.$t('information.staDd')
          },
          { field: 'reWork.dep', type: 'select', values: [] },
          { field: 'reWork.moNo', type: 'input', },
          { field: 'reWork.casNo2', type: 'select', values: [] },
          { field: 'reWork.zcNo2', type: 'select', values: [] },
          { field: 'reWork.usrPg', type: 'input',},
          { field: 'reWork.ygNoName', type: 'input',},
          { field: 'reWork.jaFlag', type: 'radio',
             values:[
            { label: 'public.T', value: 'T' },
            { label: 'public.F', value: 'F' },
          ]},
        ],
        tableColumn: [
          { field: "depName", title: "reWork.depName", width: "150", },
          { field: "pgDd", title: "reWork.pgDd", width: "150" },
          { field: "prdNo", title: "reWork.prdNo", width: "150" },
          { field: "ygNoName", title: "reWork.ygNoName", width: "150" },
          { field: "zcNo2", title: "reWork.zcNo2", width: "150" },
          { field: "zcName2", title: "reWork.zcName2", width: "150" },
          { field: "prdNo", title: "reWork.prdNo", width: "150" },
          { field: "prdName", title: "reWork.prdName", width: "150" },
          { field: "spc", title: "reWork.spc", width: "150" },
          { field: "pgQty", title: "reWork.pgQty", width: "150" },
          { field: "qtyFin", title: "reWork.qtyFin", width: "150" },
          { field: "wwQty", title: "reWork.wwQty", width: "150" },
          { field: "talGs", title: "reWork.talGs", width: "150" },
          { field: "jaFlag", title: "reWork.jaFlag", width: "150" },
          { field: "moNo", title: "reWork.moNo", width: "150" },
          { field: "casNo2", title: "reWork.casNo2", width: "150" },
          { field: "casName", title: "reWork.casName", width: "150" },
          { field: "usrPg", title: "reWork.usrPg", width: "150" },
        ],
      }
    }
  },
  mounted() {
    this.setValues()
  },
  methods: {
    handleToolbarClick(params) {
      switch (params.code) {
        case 'query':
          this.queryResult = params.result.data.records
          break;
        case 'export':
          this.handleExport()
          break;
        default:
      }
    },
    handleDblclick(param) {
      this.$refs.createRef.handleEdit(param);
    },
    handleExport() {
      if (this.queryResult && this.queryResult.length === 0) return this.$message.warning('当前无数据')
      const hide = this.$message.loading('导出中..', 0)
      try {
        setTimeout(hide, 10)
        this.$message.success('导出成功')
        const arr = this.getColumn()
        const obj = {
          data: res.records,
          tableColumnZh: arr[0].filter(i => i !== '序号'),
          tableColumnEn: arr[1].filter(i => i !== undefined),
          name: '工序完工状况表'
        }
        this.$refs.exportRef.create(obj)
      }
      catch (error) {
        this.$message.error('导出失败')
      }
    },
    getColumn() {
      const { collectColumn } = this.$refs.vTable.getTableColumn()
      return [collectColumn.map(i => i.title), collectColumn.map(i => i.property)]
    },
    setValues() {
      getDep({ key: '' })
        .then(res => (
          this.vTableProps.formDataRaw.find(i => i.field === 'reWork.dep').values = res.data
        ))

      queryCas({ current: 1, size: 10, })
        .then(res => (
          this.vTableProps.formDataRaw.find(i => i.field === 'reWork.casNo2').values = res.data.records
        ))

      gxlist2()
        .then(res => (
          this.vTableProps.formDataRaw.find(i => i.field === 'reWork.zcNo2').values = res.data
        ))
    }
  },
}
</script>
