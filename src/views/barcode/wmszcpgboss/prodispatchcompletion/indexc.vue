<template>
<div class="consrm">
<div  style="text-align:right;margin-right:16px;position: absolute;top: 95px;right:0px;z-index:999">
   <a-button style="margin-left: 8px" @click="handleExport()" type="primary" v-permission="barcode_prodispatch_reset">
               {{ $t('public.export') }}</a-button>
              <a-button style="margin-left: 8px" type="primary" @click="reset" v-permission="barcode_prodispatch_reset">{{
                $t('public.reset')
              }}</a-button>
  </div>
<el-tabs type="border-card">
      <el-tab-pane label="工序派工完工统计表">
  <a-card :bordered="false">
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row>
          <a-col :span="7">
            <a-form-item label="派工日期">
              <a-date-picker
                v-model="staDd"
                :disabled-date="disabledStartDate"
                format="YYYY-MM-DD"
                valueFormat="YYYY-MM-DD"
                :placeholder="$t('information.staDd')"
                @openChange="handleStartOpenChange"
                style="width: 45%"
              />
              <a-date-picker
                v-model="endDd"
                :disabled-date="disabledEndDate"
                format="YYYY-MM-DD"
                valueFormat="YYYY-MM-DD"
                :placeholder="$t('information.endDd')"
                :open="endOpen"
                @openChange="handleEndOpenChange"
                style="width: 45%; margin-left: 8px"
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="部门">
              <a-select
                v-model="queryParam.dep"
                allowClear
                show-search
                :filter-option="false"
                @search="searchDep"
                @focus="handleSearchDeps"
                style="width: 90%"
              >
                <a-select-option v-for="(i, index) in depList" :key="index" :value="i.dep">
                  {{ i.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="制令单号">
              <a-input v-model="queryParam.moNo" style="width: 90%" />
            </a-form-item>
          </a-col>
          <a-col :span="5">
            <a-form-item label="工程案号">
              <a-select
                show-search
                allowClear
                :filter-option="false"
                v-model="queryParam.casNo2"
                @search="searchCasNo"
                @focus="handleSearchCasNo"
                style="width: 100%"
              >
                <a-select-option v-for="(i, index) in casNoList" :key="index" :value="i.casNo2">
                  {{ i.casName2 }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="7">
            <a-form-item label="工序号">
              <a-select v-model="queryParam.zcNo2" allowClear @focus="handleSearchZcNo" style="width: 92%">
                <a-select-option v-for="(i, index) in zcNoList" :key="index" :value="i.zcNo2">
                  {{ i.zcName2 }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="派工人员">
              <a-input v-model="queryParam.usrPg" style="width: 90%" />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="作业人员">
              <a-input v-model="queryParam.ygNoName" style="width: 90%" />
            </a-form-item>
          </a-col>
          <a-col :span="5">
            <a-form-item label="结案显示否">
              <a-checkbox v-model="jaFlag"></a-checkbox>
            </a-form-item>
          </a-col>
          <a-col style="float: right">
            <span>
              <a-button type="primary" @click="hanleQuery" v-permission="barcode_prodispatch_search">{{
                $t('public.query')
              }}</a-button>
             
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <div>
      <!-- <vxe-toolbar custom>
        <template v-slot:buttons>
          <a-dropdown :trigger="['click']" v-permission="barcode_prodispatch_export">
            <a-button
              >{{ $t('public.action') }}
              <a-icon type="down" />
            </a-button>
            <a-menu slot="overlay">
              <a-menu-item key="0">
                <a @click="handleExport()">{{ $t('public.export') }}</a>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </template>
      </vxe-toolbar> -->
      <vxe-table
        border
        resizable
        stripe
        highlight-current-row
        show-overflow
        highlight-hover-row
        export-config
        ref="xTable"
        size="mini"
        max-height="650"
        :loading="spinning"
        :data="tableData"
        :keyboard-config="{ isArrow: true }"
        :edit-config="{ trigger: 'click', mode: 'row' }"
      >
        <vxe-table-column
          v-for="item in tableColumn"
          :key="item.field"
          :field="item.field"
          align="center"
          :title="item.title"
          width="150"
        ></vxe-table-column>
      </vxe-table>
      <vxe-pager
        :loading="spinning"
        :current-page="tablePage.currentPage"
        :page-size="tablePage.pageSize"
        :total="tablePage.total"
        :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
        @page-change="handlePageChange"
      >
      </vxe-pager>
    </div>
    <Export ref="Export"></Export>
  </a-card>
  </el-tab-pane>
</el-tabs>
</div>
</template>

<script>
import { getDep, queryCas, gxlist2, findPgWgTj } from '@/api/report/dispatch'
import Export from '@/components/barcodeExport/barcodeExport'
import moment from 'moment'
export default {
  components: {
    Export
  },
  data() {
    return {
      barcode_prodispatch_search: 'barcode_prodispatch_search',
      barcode_prodispatch_reset: 'barcode_prodispatch_reset',
      barcode_prodispatch_export: 'barcode_prodispatch_export',
      spinning: false,
      expandKeys: [],
      tableData: [],
      depList: [],
      custList: [],
      ZcList: [],
      zcNos: [],
      casNoList: [],
      zcNoList: [],
      tableColumn: [
        {
          title: '部门',
          field: 'depName'
        },
        {
          title: '派工日期',
          field: 'pgDd'
        },
        {
          title: '派工单号',
          field: 'pgNo'
        },
        {
          title: '作业人员',
          field: 'name'
        },
        {
          title: '工序代号',
          field: 'zcNo2'
        },
        {
          title: '工序名称',
          field: 'zcName2'
        },
        {
          title: '品号',
          field: 'prdNo'
        },
        {
          title: '品名',
          field: 'prdName'
        },
        {
          title: '规格',
          field: 'spc'
        },
        {
          title: '非标规格',
          field: 'prdMark'
        },
        {
          title: '派工数量',
          field: 'pgQty'
        },
        {
          title: '完工数量',
          field: 'qtyFin'
        },
        {
          title: '未完工量',
          field: 'wwQty'
        },
        {
          title: '累计工时',
          field: 'talGs'
        },
        {
          title: '结案否',
          field: 'jaStatus'
        },
        {
          title: '制令单号',
          field: 'moNo'
        },
        {
          title: '工程案号',
          field: 'casNo'
        },
        {
          title: '工程名称',
          field: 'casName'
        },
        {
          title: '派工人员',
          field: 'usrPg'
        }
      ],
      status: true,
      endOpen: false,
      loading: false,
      jaFlag: false,
      staDd: null,
      endDd: null,
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      type: {
        zcNo2: false
      },
      queryParam: {
        staDd: null,
        endDd: null,
        dep: '',
        moNo: '',
        casNo2: '',
        zcNo2: '',
        usrPg: '',
        ygNoName: ''
      }
    }
  },
  created() {
    this.staDd = moment(new Date()).format('YYYY-MM-DD')
    this.endDd = moment(new Date()).format('YYYY-MM-DD')
  },
  mounted() {},

  watch: {
    staDd(val) {
      if (val) {
        this.queryParam.staDd = moment(val).format('YYYY-MM-DD 00:00:00')
      } else {
        this.queryParam.staDd = null
      }
    },
    endDd(val) {
      if (val) {
        this.queryParam.endDd = moment(val).format('YYYY-MM-DD 23:59:59')
      } else {
        this.queryParam.endDd = null
      }
    }
  },
  methods: {
    getList() {
      this.spinning = true
      findPgWgTj(
        Object.assign(
          {
            current: this.tablePage.currentPage,
            size: this.tablePage.pageSize,
            jaFlag: this.jaFlag ? 'T' : 'F'
          },
          this.queryParam
        )
      )
        .then(res => {
          this.tableData = res.records
          this.tablePage.total = res.total
          this.getName()
          this.spinning = false
        })
        .catch(err => {
          this.tableData = []
          this.spinning = false
          this.requestFailed(err)
        })
    },
    hanleQuery() {
      this.tablePage.currentPage = 1
      this.getList()
    },
    getName() {
      if (this.tableData.length > 0) {
        this.tableData.forEach(i => {
          i.name = ''
          i.jaStatus = i.jaFlag === 'T' ? '已结案' : '未结案'
          if (i.ygNo) {
            let names = i.ygNo.split(',')
            names.forEach((n, index) => {
              let m = i.ygNoName.split(',')[index].split(n)[1]
              i.name = i.name ? i.name + ',' + m : m
            })
          }
        })
      }
    },
    // 分页
    handlePageChange({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getList()
    },
    handleSearchDeps(val = '') {
      getDep({
        key: val
      })
        .then(res => (this.depList = res.data))
        .catch(err => this.requestFailed(err))
    },
    searchDep(val) {
      this.handleSearchDeps(val)
    },
    handleSearchCasNo(val) {
      queryCas({
        current: 1,
        size: 10,
        casName2: val
      })
        .then(res => (this.casNoList = res.data.records))
        .catch(err => this.requestFailed(err))
    },
    handleSearchZcNo() {
      gxlist2()
        .then(res => (this.zcNoList = res.data))
        .catch(err => this.requestFailed(err))
    },
    searchCasNo(val) {
      this.handleSearchCasNo(val)
    },
    disabledStartDate(startValue) {
      const endValue = this.endDd
      if (!startValue || !endValue) {
        return false
      }
      return startValue.valueOf() > endValue.valueOf()
    },
    disabledEndDate(endValue) {
      const startValue = this.staDd
      if (!endValue || !startValue) {
        return false
      }
      return startValue.valueOf() >= endValue.valueOf()
    },
    handleStartOpenChange(open) {
      if (!open) {
        this.endOpen = true
      }
    },
    handleEndOpenChange(open) {
      this.endOpen = open
    },
    reset() {
      this.queryParam = {
        staDd: null,
        endDd: null,
        dep: '',
        moNo: '',
        casNo2: '',
        zcNo2: '',
        usrPg: '',
        ygNoName: ''
      }
      this.staDd = null
      this.endDd = null
      this.jaFlag = false
    },
    handleExport() {
      if (this.tableData.length === 0) return this.$message.warning('请查询后再导出！')
      const hide = this.$message.loading('导出中..', 0)
      findPgWgTj(
        Object.assign(
          {
            current: 1,
            size: -1,
            jaFlag: this.jaFlag ? 'T' : 'F'
          },
          this.queryParam
        )
      )
        .then(res => {
          setTimeout(hide, 10)
          this.$message.success('导出成功')
          const arr = this.getColumn()
          const obj = {
            data: this.exportZyName(res.records),
            tableColumnZh: arr[0].filter(i => i !== '序号'),
            tableColumnEn: arr[1].filter(i => i !== undefined),
            name: '工序派工完工统计表'
          }
          this.$refs.Export.create(obj)
        })
        .catch(err => {
          this.$message.error('导出失败')
        })
    },
    getColumn() {
      const { collectColumn } = this.$refs.xTable.getTableColumn()
      return [collectColumn.map(i => i.title), collectColumn.map(i => i.property)]
    },
    exportZyName(arr) {
      arr.forEach(i => {
        i.name = ''
        i.jaStatus = i.jaFlag === 'T' ? '已结案' : '未结案'
        if (i.ygNo) {
          let names = i.ygNo.split(',')
          names.forEach((n, index) => {
            let m = i.ygNoName.split(',')[index].split(n)[1]
            i.name = i.name ? i.name + ',' + m : m
          })
        }
      })
      return arr
    }
  }
}
</script>
