<template>
  <div class="layout">
    <vTable ref="vTable" v-bind="vTableProps" @toolbarClick="handleToolbarClick">
    </vTable>
    <Export ref="exportRef"></Export>
  </div>
</template>
<script>
import vTable from '@/components/amtxts/vTable/vGrid.vue'
import { queryfindAll } from '@/api/report/dispatch'
import Export from '@/components/barcodeExport/barcodeExport'
export default {
  components: {
    vTable, Export
  },
  data() {
    return {
      queryResult:[],
      vTableProps: {
        api_find: queryfindAll,
        api_delete: null,
        toolbarItems: [
          { label: '工序完工状况', value: 'title' },
          { label: '导出', value: 'export', },
        ],
        formDataRaw: [
          {
            field: 'reWork.staDd', type: 'datePicker',
            placeholder: this.$t('information.staDd')
          }, 
          {
            field: 'reWork.endDd', type: 'datePicker',
            placeholder: this.$t('information.staDd')
          },
          { field: 'reWork.pgNo', type: 'input', },
        ],
        tableColumn: [
          { type:"seq", width: "60", fixed:"left"},
          { field: "pgNo", title: "reWork.pgNo", width:"150",},
          { field: "pgDd", title: "reWork.pgDd", width:"150"},
          { field: "prdNo", title: "reWork.prdNo", width:"150"},
          { field: "prdName", title: "reWork.prdName", width:"150"},
          { field: "spc", title: "reWork.spc", width:"150"},
          { field: "prdMark", title: "reWork.prdMark", width:"150"},
          { field: "pgQty", title: "reWork.pgQty", width:"150"},
          { field: "qtyFin", title: "reWork.qtyFin", width:"150"},
          { field: "ygNoName", title: "reWork.ygNoName", width:"150"},
          { field: "zcNo2", title: "reWork.zcNo2", width:"150"},
          { field: "zcName2", title: "reWork.zcName2", width:"150"},
          { field: "sebName", title: "reWork.sebName", width:"150"},
          { field: "depNo", title: "reWork.depNo", width:"150"},
          { field: "depName", title: "reWork.depName", width:"150"},
          { field: "usrPg", title: "reWork.usrPg", width:"150"},
          { field: "moNo", title: "reWork.moNo", width:"150"},
          { field: "talGs", title: "reWork.talGs", width:"150"},
          { field: "staDd", title: "reWork.staDd", width:"150"},
          { field: "endDd", title: "reWork.endDd", width:"150"},
        ],
      }
    }
  },
  methods: {
    handleToolbarClick(params) {
      switch (params.code) {
        case 'query':
          this.queryResult = params.result.data.records
          break;
          case 'export':
            this.handleExport()
            break;
        default:
      }
    },
    handleDblclick(param) {
      this.$refs.createRef.handleEdit(param);
    },
    handleExport() {
      if (this.queryResult && this.queryResult.length === 0) return this.$message.warning('当前无数据')
      const hide = this.$message.loading('导出中..', 0)
      try {
        setTimeout(hide, 10)
        this.$message.success('导出成功')
        const arr = this.getColumn()
        const obj = {
          data: res.records,
          tableColumnZh: arr[0].filter(i => i !== '序号'),
          tableColumnEn: arr[1].filter(i => i !== undefined),
          name: '工序完工状况表'
        }
        this.$refs.exportRef.create(obj)
      }
      catch (error) {
        this.$message.error('导出失败')
      }
    },
    getColumn() {
      const { collectColumn } = this.$refs.vTable.getTableColumn()
      return [collectColumn.map(i => i.title), collectColumn.map(i => i.property)]
    }
  },
}
</script>
