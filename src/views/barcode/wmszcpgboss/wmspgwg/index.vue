<template>
  <a-card :bordered="false">
    <a-spin :spinning="spinning">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row>
            <a-col :span="7">
              <a-form-item :label="$t('information.date')">
                <a-date-picker
                  v-model="staDd"
                  :disabled-date="disabledStartDate"
                  format="YYYY-MM-DD"
                  valueFormat="YYYY-MM-DD"
                  :placeholder="$t('information.staDd')"
                  @openChange="handleStartOpenChange"
                  style="width: 45%"
                />
                <a-date-picker
                  v-model="endDd"
                  :disabled-date="disabledEndDate"
                  format="YYYY-MM-DD"
                  valueFormat="YYYY-MM-DD"
                  :placeholder="$t('information.endDd')"
                  :open="endOpen"
                  @openChange="handleEndOpenChange"
                  style="width: 45%; margin-left: 8px"
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item :label="$t('work_details.casNo')">
                <div class="salesNo">
                  <a-select
                    mode="tags"
                    placeholder="请选择唯一码"
                    v-model="queryParam.casNos"
                    @focus="handleSearch_casNo"
                    style="width: 90%"
                  >
                    <a-select-option v-for="i in csaList" :key="i">
                      {{ i }}
                    </a-select-option>
                  </a-select>
                </div>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="一级工序">
                <a-select
                  mode="multiple"
                  showSearch
                  :allowClear="true"
                  placeholder="请输入一级工序"
                  :filterOption="false"
                  @focus="handle_zcNo"
                  v-model="queryParam.zcNos"
                  style="width: 90%"
                >
                  <a-select-option v-for="(i, index) in ZcList" :key="index" :value="i.zcNo"
                    >{{ i.zcNo }}{{ i.zcName }}</a-select-option
                  >
                </a-select>
              </a-form-item>
            </a-col>
            <a-col style="float:right">
              <span class="table-page-search-submitButtons">
                <a-button type="primary" size="small" @click="getList" v-permission="barcode_wmspgwg_search">{{ $t('public.query') }}</a-button>
                <a-button size="small" style="margin-left: 8px" type="primary" @click="reportWork" v-permission="barcode_wmspgwg_submit">提交派工</a-button>
                <a-button size="small" style="margin-left: 8px" @click="reset" v-permission="barcode_wmspgwg_reset">{{ $t('public.reset') }}</a-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <el-table
        v-if="status"
        :data="tableData"
        style="width: 100%"
        row-key="id"
        id="tab"
        size="mini"
        max-height="500px"
        border
        :fit="false"
        @cell-click="cellclick"
        :row-class-name="tableRowClassName"
        :cell-style="timeStyle"
        :expand-row-keys="expandKeys"
        :header-cell-style="{ background: '#f8f8f9', color: '#606266', paddingTop: '0px', paddingBottom: '0px' }"
        @selection-change="handleSelectionChange"
        lazy
        :load="load"
        :tree-props="{ children: 'tzList', hasChildren: 'hasChildren' }"
      >
        <el-table-column prop="moNo" label="制令单号" align="center" :width="150" sortable> </el-table-column>
        <el-table-column prop="casNo" label="唯一码" align="center" :width="150"> </el-table-column>
        <el-table-column align="center" prop="caiz" :width="150" label="材质"> </el-table-column>
        <el-table-column align="center" prop="sbsl" :width="150" label="试棒数量"> </el-table-column>
        <el-table-column prop="jhq" align="center" :width="150" label="交货期" sortable> </el-table-column>
        <el-table-column prop="zcNo" :width="150" align="center" label="一级工序" show-overflow-tooltip>
          <template slot-scope="scope" v-if="scope.row.zcName && scope.row.zcNo">
            {{ scope.row.zcNo }}{{ scope.row.zcName }}
          </template>
        </el-table-column>
        <el-table-column prop="tzNo" :width="150" align="center" label="通知单号"> </el-table-column>
        <el-table-column prop="wr" :width="150" align="center" label="一级工序报工"> </el-table-column>
        <el-table-column prop="zcNo2" align="center" :width="150" label="二级工序" show-overflow-tooltip>
          <template slot-scope="scope" v-if="scope.row.zcName2 && scope.row.zcNo2">
            {{ scope.row.zcNo2 }}{{ scope.row.zcName2 }}
          </template>
        </el-table-column>
        <el-table-column prop="ygNoName" align="center" :width="150" label="作业人员">
          <template slot-scope="scope">
            <div v-if="scope.row.zcNo2 && !scope.row.ygNoName">
              <a-icon style="float:right" type="database" />
            </div>
            <div v-else>
              {{ scope.row.ygNoName }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="sebName" :width="150" align="center" label="设备">
          <template slot-scope="scope">
            <div v-if="scope.row.zcNo2 && !scope.row.sebName">
              <a-icon style="float:right" type="database" />
              <!-- <a-input
                style="width: 100%;height:100%"
                @focus="blur_zcNo2"
              /> -->
            </div>
            <div v-else>{{ scope.row.sebei }}{{ scope.row.sebName }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="staDd" :width="150" align="center" label="开始时间">
          <!-- <template slot-scope="scope">
            <div v-if="scope.row.zcNo2&&!scope.row.staDd">
              {{ date}}
            </div>
            <div v-else>
              {{ scope.row.staDd}}
            </div>
          </template> -->

          <template slot-scope="scope">
            <el-popover placement="top" trigger="click">
              <a-date-picker
                @ok="clickStaDd(scope.row.id)"
                v-model="start"
                format="YYYY-MM-DD HH:mm:ss"
                :show-time="{ defaultValue: moment('00:00:00', 'HH:mm:ss') }"
              />
              <span slot="reference">{{ scope.row.staDd }}</span>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column prop="endDd" align="center" :width="150" label="结束时间">
          <!-- <template slot-scope="scope">
            <el-popover
              placement="top"
              trigger="click"
            >
              <a-date-picker
                @ok='clickEndDd(scope.row.id)'
                v-model="endDate"
                format="YYYY-MM-DD HH:mm:ss"
                :show-time="{ defaultValue: moment('00:00:00', 'HH:mm:ss') }"
              />
              <span slot="reference">{{ scope.row.endDd }}</span>
            </el-popover>
          </template> -->
          <template slot-scope="scope">
            <a-date-picker
              style="min-width: 60px"
              placeholder="日期"
              v-if="scope.row.zcNo2 && !scope.row.endDd"
              @ok="clickEndDd(scope.row.id)"
              v-model="endDate"
              format="YYYY-MM-DD HH:mm:ss"
              :show-time="{ defaultValue: moment('00:00:00', 'HH:mm:ss') }"
            />

            <!-- <a-popover
              v-else
              trigger="hover"
            >
              <template slot="content">
                <a-date-picker
                  @ok='clickEndDd(scope.row.id)'
                  v-model="endDate"
                  format="YYYY-MM-DD HH:mm:ss"
                  :show-time="{ defaultValue: moment('00:00:00', 'HH:mm:ss') }"
                />
              </template>
              <span>{{ scope.row.endDd }}</span>
            </a-popover> -->
            <el-popover v-else placement="top" trigger="click">
              <div id="father">
                <a-date-picker
                  style="min-width: 60px"
                  @ok="clickEndDd(scope.row.id)"
                  v-model="endDate"
                  placeholder="日期"
                  format="YYYY-MM-DD HH:mm:ss"
                  :show-time="{ defaultValue: moment('00:00:00', 'HH:mm:ss') }"
                />
              </div>
              <span slot="reference">{{ scope.row.endDd }}</span>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column prop="pgCount" align="center" :width="150" label="派工单数">
          <template slot-scope="scope">
            <div v-if="scope.row.pgCount === 0">
              {{ scope.row.pgCount }}
            </div>
            <!-- <span>{{ scope.row.pgCount }}</span> -->
            <a v-else @click="click_pgCount(scope.row)"> {{ scope.row.pgCount }}</a>
          </template>
        </el-table-column>
        <el-table-column prop="wgCount" align="center" :width="150" label="完工单数">
          <template slot-scope="scope">
            <div v-if="scope.row.wgCount === 0">
              {{ scope.row.wgCount }}
            </div>
            <a v-else @click="click_wgCount(scope.row)"> {{ scope.row.wgCount }}</a>
          </template>
        </el-table-column>

        <!-- <el-table-column
          type="selection"
          width="55"
        >
        </el-table-column> -->
      </el-table>
      <el-table
        v-else
        :data="tableData"
        style="width: 100%"
        row-key="id"
        id="tab"
        size="mini"
        max-height="500px"
        border
        :fit="false"
        @cell-click="cellclick"
        :row-class-name="tableRowClassName"
        :cell-style="timeStyle"
        :expand-row-keys="expandKeys"
        :header-cell-style="{ background: '#f8f8f9', color: '#606266', paddingTop: '0px', paddingBottom: '0px' }"
        @selection-change="handleSelectionChange"
        lazy
        :load="load"
        :tree-props="{ children: 'tzList', hasChildren: 'hasChildren' }"
      >
        <el-table-column prop="moNo" label="制令单号" fixed="left" align="center" :width="150" sortable>
        </el-table-column>
        <el-table-column prop="casNo" label="唯一码" fixed="left" align="center" :width="150"> </el-table-column>
        <el-table-column align="center" prop="caiz" :width="150" label="材质"> </el-table-column>
        <el-table-column align="center" prop="sbsl" :width="150" label="试棒数量"> </el-table-column>
        <el-table-column prop="jhq" align="center" :width="150" label="交货期" sortable> </el-table-column>
        <el-table-column prop="zcNo" :width="150" align="left" label="一级工序">
          <template slot-scope="scope" v-if="scope.row.zcName && scope.row.zcNo">
            {{ scope.row.zcNo }}{{ scope.row.zcName }}
          </template>
        </el-table-column>
        <el-table-column prop="tzNo" :width="150" align="center" label="通知单号"> </el-table-column>
        <el-table-column prop="wr" :width="150" align="center" label="一级工序报工"> </el-table-column>
        <el-table-column prop="zcNo2" align="center" :width="150" label="二级工序" show-overflow-tooltip>
          <template slot-scope="scope" v-if="scope.row.zcName2 && scope.row.zcNo2">
            {{ scope.row.zcNo2 }}{{ scope.row.zcName2 }}
          </template>
        </el-table-column>
        <el-table-column prop="ygNoName" align="center" :width="150" label="作业人员">
          <template slot-scope="scope">
            <div v-if="scope.row.zcNo2 && !scope.row.ygNoName">
              <a-icon style="float:right" type="database" />
            </div>
            <div v-else>
              {{ scope.row.ygNoName }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="sebName" :width="150" align="center" label="设备">
          <template slot-scope="scope">
            <div v-if="scope.row.zcNo2 && !scope.row.sebName">
              <a-icon style="float:right" type="database" />
              <!-- <a-input
                style="width: 100%;height:100%"
                @focus="blur_zcNo2"
              /> -->
            </div>
            <div v-else>{{ scope.row.sebei }}{{ scope.row.sebName }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="staDd" :width="150" align="center" label="开始时间">
          <!-- <template slot-scope="scope">
            <div v-if="scope.row.zcNo2&&!scope.row.staDd">
              {{ date}}
            </div>
            <div v-else>
              {{ scope.row.staDd}}
            </div>
          </template> -->

          <template slot-scope="scope">
            <el-popover placement="top" trigger="click">
              <a-date-picker
                @ok="clickStaDd(scope.row.id)"
                v-model="start"
                format="YYYY-MM-DD HH:mm:ss"
                :show-time="{ defaultValue: moment('00:00:00', 'HH:mm:ss') }"
              />
              <span slot="reference">{{ scope.row.staDd }}</span>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column prop="endDd" align="center" :width="150" label="结束时间">
          <!-- <template slot-scope="scope">
            <el-popover
              placement="top"
              trigger="click"
            >
              <a-date-picker
                @ok='clickEndDd(scope.row.id)'
                v-model="endDate"
                format="YYYY-MM-DD HH:mm:ss"
                :show-time="{ defaultValue: moment('00:00:00', 'HH:mm:ss') }"
              />
              <span slot="reference">{{ scope.row.endDd }}</span>
            </el-popover>
          </template> -->
          <template slot-scope="scope">
            <a-date-picker
              v-if="scope.row.zcNo2 && !scope.row.endDd"
              style="min-width: 60px"
              placeholder="日期"
              @ok="clickEndDd(scope.row.id)"
              v-model="endDate"
              format="YYYY-MM-DD HH:mm:ss"
              :show-time="{ defaultValue: moment('00:00:00', 'HH:mm:ss') }"
            />

            <!-- <a-popover
              v-else
              trigger="hover"
            >
              <template slot="content">
                <a-date-picker
                  @ok='clickEndDd(scope.row.id)'
                  v-model="endDate"
                  format="YYYY-MM-DD HH:mm:ss"
                  :show-time="{ defaultValue: moment('00:00:00', 'HH:mm:ss') }"
                />
              </template>
              <span>{{ scope.row.endDd }}</span>
            </a-popover> -->
            <el-popover v-else placement="top" trigger="click">
              <div id="father">
                <a-date-picker
                  @ok="clickEndDd(scope.row.id)"
                  v-model="endDate"
                  style="min-width: 60px"
                  placeholder="日期"
                  format="YYYY-MM-DD HH:mm:ss"
                  :show-time="{ defaultValue: moment('00:00:00', 'HH:mm:ss') }"
                />
              </div>
              <span slot="reference">{{ scope.row.endDd }}</span>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column prop="pgCount" align="center" :width="150" label="派工单数">
          <template slot-scope="scope">
            <div v-if="scope.row.pgCount === 0">
              {{ scope.row.pgCount }}
            </div>
            <a v-else @click="click_pgCount(scope.row)"> {{ scope.row.pgCount }}</a>
          </template>
        </el-table-column>
        <el-table-column prop="wgCount" align="center" :width="150" label="完工单数">
          <template slot-scope="scope">
            <div v-if="scope.row.wgCount === 0">
              {{ scope.row.wgCount }}
            </div>
            <a v-else @click="click_wgCount(scope.row)"> {{ scope.row.wgCount }}</a>
          </template>
        </el-table-column>

        <!-- <el-table-column
          type="selection"
          width="55"
        >
        </el-table-column> -->
      </el-table>
      <Modal ref="modal" @touch="touch" />
      <ModalWG ref="ModalWG" />
      <ModalPG ref="ModalPG" />
    </a-spin>
  </a-card>
</template>

<script>
import { findList, findZc2, getMo, getLoad, getZc, getCas } from '@/api/report/unrestricted'
import { add } from '@/api/report/dispatch'
import Modal from './modal'
import ModalWG from './ModalWG'
import ModalPG from './ModalPG'

import moment from 'moment'
export default {
  components: {
    Modal,
    ModalWG,
    ModalPG
  },
  data() {
    return {
      barcode_wmspgwg_search: 'barcode_wmspgwg_search',
      barcode_wmspgwg_submit: 'barcode_wmspgwg_submit',
      barcode_wmspgwg_reset: 'barcode_wmspgwg_reset',
      spinning: false,
      expandKeys: [],
      tableData: [],
      status: true,
      endOpen: false,
      loading: false,
      staDd: null,
      endDd: null,
      start: null,
      endDate: null,
      custList: [],
      date: moment(new Date()).format('YYYY-MM-DD HH:MM:SS'),
      ZcList: [],
      zcNos: [],
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      type: {
        zcNo2: false
      },
      queryParam: {
        moNo: '',
        casNo: '',
        zcNos: []
      },
      id: '',
      wd: '',
      tzNos: [],
      usrID: '',
      csaList: []
    }
  },
  created() {
    // this.getList()
    this.staDd = moment(new Date()).format('YYYY-MM-DD')
    this.endDd = moment(new Date()).format('YYYY-MM-DD')
  },
  mounted() {
    let a = document.getElementById('tab')
    this.$nextTick(() => {
      this.wd = a.clientWidth / 17
    })
  },

  watch: {
    staDd(e) {
      this.staDd = e
      this.queryParam.moNo = ''
      // this.queryParam.casNo = ''
      this.queryParam.zcNo = ''
      // this.handleSearch()
    },
    endDd(e) {
      this.endDd = e
      this.queryParam.moNo = ''
      // this.queryParam.casNo = ''
      this.queryParam.zcNo = ''
      // this.handleSearch()
    }
  },
  methods: {
    moment,
    // 递归便利方法
    Zctwo(arr, id, obj) {
      arr.forEach(e => {
        if (e.id === id) {
          Object.assign(e, obj)
        } else {
          if (e.tzList && e.tzList.length > 0) {
            this.Zctwo(e.tzList, id, obj)
          }
        }
      })
      return arr
    },
    // 懒加载第三级
    load(tree, treeNode, resolve) {
      this.tzNos.push(tree.tzNo)
    
      let that = this
      findZc2({
        tzNo: tree.tzNo
        // tzNo: 'TZ2009100001'
      })
        .then(function(response) {
          let a = response.data
          a.forEach(e => {
            Object.assign(e, { staDd: that.date, endDd: '', ygNoName: '', sebName: '' })
          })
          that.Zctwo(that.tableData, tree.id, { tzList: a })
          resolve(response.data)
        })
        .catch(err => this.requestFailed(err))
        .finally(() => {})
    },

    tableRowClassName({ row, rowIndex }) {
      if (row.moNo) {
        return 'warning-row '
      }
      return ''
    },
    timeStyle({ row, column, rowIndex, columnIndex }) {
      if (row.ygNoName && column.label == '作业人员') {
        return ' background: red; color: white'
      }
    },
    handleSearch_casNo() {
      let staDd
      let endDd
      if (this.endDd !== null) {
        staDd = moment(this.staDd).format('YYYY-MM-DD 12:00:00')
      } else {
        staDd = null
      }
      if (this.endDd !== null) {
        endDd = moment(this.endDd).format('YYYY-MM-DD 12:00:00')
      } else {
        endDd = null
      }
      getCas(
        Object.assign({
          staDd: staDd,
          endDd: endDd
        })
      ).then(res => {
       
        this.csaList = res.data
      }).catch(err => this.requestFailed(err))
    },
    // 点击日期组件
    clickStaDd(id) {
      let date = moment(this.start).format('YYYY-MM-DD HH:MM:SS')
      let obj = { staDd: date }
      let ids = id
      this.tableData = this.Zctwo(this.tableData, ids, obj)
      this.start = null
    },
    clickEndDd(id) {
      let date = moment(this.endDate).format('YYYY-MM-DD HH:MM:SS')
      let obj = { endDd: date }
      let ids = id
      this.tableData = this.Zctwo(this.tableData, ids, obj)
     
      this.endDate = null
    },

    touch(data, reset) {
      if (reset !== undefined) {
        this.status = !this.status
        let arr = this.tableData
        let id = data.id
        let obj
        if (data.type === 1) {
          obj = { sebei: '', sebName: '' }
        }
        if (data.type === 2) {
          obj = { ygNoName: '', ygNos: [] }
        }
        this.tableData = Zctwo(arr, id, obj)
      } else {
        var obj
        if (data.type === 1) {
          obj = { sebei: data.sebei, sebName: data.sebName }
        }
        if (data.type === 2) {
          obj = { ygNoName: data.ygNoName, ygNos: data.ygNos }
        }
        this.status = !this.status
        // 递归查询当前id 对象添加属性值
        let arr = this.tableData
        let id = data.id
        this.tableData = Zctwo(arr, id, obj)
      }
      function Zctwo(arr, id, obj) {
        arr.forEach(e => {
          if (e.id === id) {
            Object.assign(e, obj)
          } else {
            if (e.tzList && e.tzList.length > 0) {
              Zctwo(e.tzList, id, obj)
            }
          }
        })
        return arr
      }
    },
    cellclick(row, column, cell, event) {
      switch (column.label) {
        case '作业人员': {
          if (row.zcNo2) {
            let tableColumn = [
              { type: 'checkbox', width: 50 },
              { field: 'salNo', title: 'public.numb', align: 'center', width: '30%' },
              { field: 'depName', title: '部门', align: 'center' },
              { field: 'salName', title: '人员', align: 'center' }
            ]
            let urls = '/barcode/scpg/salm'
            this.usrID = row.id
            this.$refs.modal.create({ title: '作业人员' }, tableColumn, urls, row, { multiple: true })
          }
          break
        }
        case '设备': {
          if (row.zcNo2) {
            let tableColumn = [
              { type: 'radio', width: 50 },
              { field: 'sebNo', title: 'public.numb', align: 'center', width: '30%' },
              { field: 'sebName', title: 'public.name', align: 'center' }
            ]
            let urls = '/barcode/scpg/sebei'
            this.$refs.modal.create({ title: '设备' }, tableColumn, urls, row, { multiple: false })
          }
          break
        }
        case '开始时间': {
          // alert(1)
          // if (row.zcNo2) {

          // }
          break
        }
      }
    },
    // 多选栏位
    handleSelectionChange(val) {

    },

    handleSearch() {
      let staDd
      let endDd
      if (this.endDd !== null) {
        staDd = moment(this.staDd).format('YYYY-MM-DD 00:00:00')
      } else {
        staDd = null
      }
      if (this.endDd !== null) {
        endDd = moment(this.endDd).format('YYYY-MM-DD 23:59:59')
      } else {
        endDd = null
      }
      getMo(
        Object.assign({
          staDd: staDd,
          endDd: endDd
        })
      ).then(res => {
        this.custList = res.data
      }).catch(err => this.requestFailed(err))
    },

    disabledStartDate(startValue) {
      const endValue = this.endDd
      if (!startValue || !endValue) {
        return false
      }
      return startValue.valueOf() > endValue.valueOf()
    },
    disabledEndDate(endValue) {
      const startValue = this.staDd
      if (!endValue || !startValue) {
        return false
      }
      return startValue.valueOf() >= endValue.valueOf()
    },
    // 一级制程
    handle_zcNo() {
      getZc(
        Object.assign({
          moNo: this.queryParam.moNo,
          casNo: this.queryParam.casNo
        })
      ).then(res => {
        this.ZcList = res.data
      }).catch(err => this.requestFailed(err))
    },
    // 查询列表
    getList() {
      let staDd
      let endDd
      if (this.endDd !== null) {
        staDd = moment(this.staDd).format('YYYY-MM-DD 00:00:00')
      } else {
        staDd = null
      }
      if (this.endDd !== null) {
        endDd = moment(this.endDd).format('YYYY-MM-DD 23:59:59')
      } else {
        endDd = null
      }
      this.spinning = true
      findList(
        Object.assign({
          pgBz: 3,
          moNo: '',
          casNos: this.queryParam.casNos ? this.queryParam.casNos : [],
          staDd: staDd,
          endDd: endDd,
          zcNos: this.queryParam.zcNos
        })
      )
        .then(res => {
         
          let a = res.data
          a.forEach(e => {
            this.expandKeys.push(e.id)
          })
          setTimeout(() => {
            this.tableData = res.data
          }, 100)
          this.spinning = false
        })
        .catch(err => this.requestFailed(err))
        .finally(() => {
          this.spinning = false
        })
    },
    handleStartOpenChange(open) {
      if (!open) {
        this.endOpen = true
      }
    },
    handleEndOpenChange(open) {
      this.endOpen = open
    },
    // 日期改变事件
    sexChangeEvent({ column }) {
      this.status = !this.status
    },
    reset() {
      this.queryParam = {
        moNo: '',
        casNos: [],
        zcNos: []
      }
      this.staDd = null
      this.endDd = null
    },
    click_wgCount(row) {
      this.$refs.ModalWG.create({ title: '完工详情' }, row)
    },
    click_pgCount(row) {
 
      this.$refs.ModalPG.create({ title: '派工详情' }, row)
    },
    // 提交
    reportWork() {
      let arr = this.tableData
      let that = this
      let newArr = []
      let value = Zctwo(arr, newArr)
      if (value && value.length > 0) {
        that.spinning = true
        add(value)
          .then(res => {
            if (res) {
              that.spinning = false
              that.$message.success(that.$t('public.success'))
              that.$nextTick(() => {
                getLoad({
                  tzNos: that.tzNos
                })
                  .then(function(response) {
                    let a = response.data
                    let ids = []
                    a.forEach(e => {
                      ids.push(e.id)
                    })
                    ids.forEach(e => {
                      let obj = { sebei: '', sebName: '', ygNoName: '', ygNos: [], endDd: '' }
                      that.tableData = that.Zctwo(that.tableData, e, obj)
                    })
                    that.status = !that.status
                    response.data.forEach(e => {
                      let obj = { pgCount: e.pgCount, wgCount: e.wgCount }
                      that.tableData = that.Zctwo(that.tableData, e.id, obj)
                    })
                  })
                  .catch(err => this.requestFailed(err))
                  .finally(() => {})
              })
            }
          })
          .catch(err => that.requestFailed(err))
          .finally(() => {
            that.spinning = false
          })
      } else {
        this.$notification['error']({
          message: this.$t('public.message'),
          description: '请先选择作业人员后提交派工！'
        })
      }
      function Zctwo(arr, newArr) {
        arr.forEach(e => {
          if (e.ygNoName) {
            newArr.push(e)
          } else {
            if (e.tzList && e.tzList.length > 0) {
              Zctwo(e.tzList, newArr)
            }
          }
        })
        return newArr
      }
    },
    // 分页
    handlePageChange({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getList()
    }
  }
}
</script>

<style lang="css" scoped>
.salesNo /deep/ .ant-select-selection {
  position: relative !important;
  z-index: 999 !important;
}
.el-table .warning-row {
  background: #cccccc;
}
.ant-calendar-picker-input.ant-input {
  border: none;
}
#father .ant-calendar-picker-input.ant-input {
  border: none;
}
</style>
