<template>
  <!-- @cancel="handleCancel" modal 关闭提示 -->
  <a-modal
    :title="title"
    destroyOnClose
    width="50%"
    :visible.sync="visible"
    :confirmLoading="confirmLoading"
    @cancel="handleCancel"
  >
    <!-- <a-form layout="inline" :form="form" >
      <a-form-item
        v-for="(itm, index) in list"
        :key="index">
        <a-input
          v-if="!itm.hidden"
          v-decorator="[
            `${itm.value}`
          ]"
          :placeholder="`${itm.placeholder}`"
        />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="getList">{{ $t('public.query') }}</a-button>
        <a-button style="margin-left: 8px" @click="reset">{{ $t('public.reset') }}</a-button>
      </a-form-item>
    </a-form> -->

    <vxe-grid
      border
      resizable
      show-overflow
      highlight-hover-row
      max-height="500"
      size='medium'
      highlight-current-row
      ref="xTable"
      :radio-config="{labelField: '', trigger: 'row'}"
      @cell-click="cellClickEvent"
      :seq-config="{startIndex: (tablePage.currentPage - 1) * tablePage.pageSize}"
      :checkbox-config="{trigger: 'row', highlight: true, range: true}"
      :columns="tableColumn"
      :data="tableData"
      @page-change="handlePageChange"
      @form-submit="getList"
    ></vxe-grid>
    <!-- :pager-config="tablePage" -->
    <!-- :form-config="tableForm" -->
    <template slot="footer">
      <a-button
        key="ok"
        @click="save"
      >{{ $t('public.sure') }}</a-button>
      <a-button @click="handleReset">清空</a-button>
      <a-button
        key="cancel"
        @click="handleCancel"
      >{{ $t('public.cancel') }}</a-button>

    </template>
  </a-modal>
</template>

<script>
import axios from '@/router/axios'

export default {
  name: 'SelectModal',
  // props: {
  //   urls: {
  //     type: String,
  //     required: true
  //   },
  //   multiple: {
  //     type: Boolean,
  //     required: true
  //   },
  //   // eslint-disable-next-line vue/require-default-prop
  //   tableColumn: Array,
  //   // eslint-disable-next-line vue/require-default-prop
  //   tableForm: Object
  // },
  data () {
    return {
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
        pageSizes: [10, 20, 50, 100, 200, 500],
        layouts: ['Sizes', 'PrevPage', 'Number', 'NextPage', 'Total'],
        perfect: true
      },
      id: '',
      multiple: false,
      obj: {},
      row: {},
      tableColumn: [],
      urls: '',
      confirmLoading: false,
      field: '',
      cellValue: '',
      title: '',
      visible: false,
      tableData: []
    }
  },

  created () { },
  methods: {
    getList () {
      axios({
        url: this.urls,
        method: 'get',
        params: Object.assign({
          tzNo: this.obj.tzNo,
        })
      }).then(res => {
        if (res.data) {
          this.tableData = res.data
          if (this.obj.ygNos) {
            this.obj.ygNos.forEach(e => {
            })
            let a = []
            this.tableData.forEach((i, index) => {
              this.obj.ygNos.forEach(e => {
                if (i.salNo === e) {
                  a.push(index)
                }
              })
            })
            let showList = []
            a.forEach(e => {
              showList.push(this.tableData[e])
            });
            this.$refs.xTable.setCheckboxRow([...showList], true)
          }
          // this.tablePage.total = res.data.total
          // this.tablePage.currentPage = res.data.current
        }
      }).catch(err => this.requestFailed(err))
    },
    // 分页
    handlePageChange ({
      currentPage,
      pageSize
    }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getList()
    },
    cellClickEvent ({
      row
    }) {
      this.row = row
    },
    create (model, tableColumn, urls, row, multiple) {
      this.multiple = multiple.multiple
      this.obj = row
      this.urls = urls
      this.tableColumn = tableColumn
      this.title = model.title
      this.visible = true
      this.getList()
    },
    // 添加确认
    save () {
      // 多选状态
      if (JSON.stringify(this.row) === "{}") {
        this.handleCancel()
      } else {
        if (this.multiple) {
          const selectRecords = this.$refs.xTable.getCheckboxRecords()
          const name = []
          const ygNos = []
          selectRecords.forEach(i => {
            name.push(i.salNo + i.salName)
            ygNos.push(i.salNo)
          })
          this.$emit('touch', {
            type: 2,
            ygNoName: name.join(','),
            ygNos: ygNos,
            id: this.obj.id
          })
          this.handleCancel()
        } else {
          // 单选状态
          // 根据不同数据传递来的代号查询id
          this.$emit('touch', {
            type: 1,
            sebei: this.row.sebNo,
            sebNo: this.row.sebNo,
            sebName: this.row.sebName,
            id: this.obj.id
          })
          this.handleCancel()
        }
      }

    },
    handleReset () {
      let reset = true
      if (this.multiple) {
        this.$emit('touch', {
          type: 2,
          id: this.obj.id
        }, reset)
      } else {
        this.$emit('touch', {
          type: 1,
          id: this.obj.id
        }, reset)
      }

      this.visible = false
    },
    handleCancel () {
      this.row = {}
      this.visible = false
    }

  }
}
</script>
