<template>
  <div>
    <a-modal
      :title="title"
      destroyOnClose
      width="65%"
      :visible.sync="visible"
      @cancel="onClose"
      :footer="null"
      :maskClosable='false'
    >
      <a-spin :spinning="spinning">
        <div>
          <vxe-table
            border
            resizable
            stripe
            highlight-current-row
            show-overflow
            highlight-hover-row
            export-config
            ref="xTable"
            :loading="loading"
            :data="tableData"
            :keyboard-config="{ isArrow: true }"
            :radio-config="{labelField: 'name', trigger: 'row'}"
          >
            <vxe-table-column
              field="pgNo"
              title="派工单号"
              align="center"
              :min-width="150"
            ></vxe-table-column>
            <vxe-table-column
              field="ygs"
              title="作业人"
              align="center"
              :min-width="80"
            >
              <template
                slot-scope="scope"
                v-if="scope.row.ygs&&scope.row.ygs.length>0"
              >
                <div
                  v-for="(i,index) in scope.row.ygs"
                  :key="index"
                >
                  {{ i.salNo }}-{{ i.salName }}
                </div>
              </template>
            </vxe-table-column>
            <vxe-table-column
              field="sebNo"
              :min-width="150"
              title="设备"
              align="center"
            >
              <template slot-scope="scope">

                <div>
                  {{ scope.row.sebNo }}{{scope.row.sebName}}
                </div>
              </template>
            </vxe-table-column>
            <vxe-table-column
              field="staDd"
              :min-width="150"
              title="开始时间"
              align="center"
            ></vxe-table-column>
            <vxe-table-column
              field="staDd"
              :min-width="150"
              title="结束时间"
              align="endDd"
            ></vxe-table-column>
          </vxe-table>
          <vxe-pager
            :loading="loading"
            :current-page="tablePage.currentPage"
            :page-size="tablePage.pageSize"
            :total="tablePage.total"
            :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
            @page-change="handlePageChange"
          >
          </vxe-pager>
          <!-- <a-row :gutter="16">
            <a-col style="float:right">
              <a-button
                style="margin-top:20px;margin-right:20px"
                @click="onClose"
              >{{ $t('public.cancel') }}</a-button>
            </a-col>
          </a-row> -->

        </div>
      </a-spin>

    </a-modal>
  </div>
</template>
<script>
import moment from 'moment'
import {
  getPgList
} from '@/api/report/unrestricted'
export default {
  data () {
    return {
      spinning: false,
      title: '',
      spcDd: '',
      tableData: [],
      visible: false,
      loading: false,
      row: {},
      tablePage: {
        currentPage: 1,
        pageSize: 5,
        total: 0
      },
      row: {}
    }
  },
  methods: {
    onClose () {
      this.tableData = []
      this.spinning = false
      this.loading = false
      this.visible = false
    },
    getList () {
      this.tableData = []
      this.spinning = true
      getPgList(
        Object.assign(
          {
            current: this.tablePage.currentPage,
            size: this.tablePage.pageSize,
            tzNo: this.row.tzNo,
            zcNo: this.row.zcNo2
          }
        )
      ).then(res => {
        this.tableData = res.data.records
        this.spinning = false
      })
        .catch(err => this.requestFailed(err))
        .finally(() => {
          this.spinning = false
        })
    },
    // 添加弹框
    create (model, row) {
      this.row = row
      this.getList()
      this.title = model.title
      this.visible = true
    },
    // 分页
    handlePageChange ({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getList()
    },
  }
}
</script>
