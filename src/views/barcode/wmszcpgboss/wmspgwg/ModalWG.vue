<template>
  <div>
    <a-modal
      :title="title"
      destroyOnClose
      width="65%"
      :visible.sync="visible"
      @cancel="onClose"
      :footer="null"
      :maskClosable='false'
    >
      <a-spin :spinning="spinning">
        <div>
          <vxe-table
            border
            resizable
            stripe
            highlight-current-row
            show-overflow
            highlight-hover-row
            export-config
            ref="xTable"
            :loading="loading"
            :data="tableData"
            :keyboard-config="{ isArrow: true }"
            :radio-config="{labelField: 'name', trigger: 'row'}"
          >
            <vxe-table-column
              field="wgNo"
              title="子工序完工单号"
              align="center"
              :min-width="150"
            >
              <template slot-scope="scope">
                <a @click="touch(scope.row.wgNo)">{{scope.row.wgNo}}</a>
              </template>
            </vxe-table-column>
            <vxe-table-column
              field="wgSum"
              title="合计工时"
              align="center"
              :min-width="80"
            ></vxe-table-column>
            <vxe-table-column
              field="kgsh"
              :min-width="150"
              title="开工时间"
              align="center"
            ></vxe-table-column>
            <vxe-table-column
              field="wgDd"
              :min-width="150"
              title="完工时间"
              align="center"
            ></vxe-table-column>
            <vxe-table-column
              field="staDd"
              :min-width="150"
              title="开始时间（补录工时）"
              align="center"
            ></vxe-table-column>
            <vxe-table-column
              field="endDd"
              :min-width="150"
              title="结束时间（补录工时）"
              align="center"
            ></vxe-table-column>
            <vxe-table-column
              field="depName"
              :min-width="90"
              title="作业部门"
              align="center"
            ></vxe-table-column>
            <vxe-table-column
              field="sebErr"
              :min-width="90"
              title="设备异常"
              align="center"
            ></vxe-table-column>
            <vxe-table-column
              field="wkTion"
              :min-width="90"
              title="交接说明"
              align="center"
            ></vxe-table-column>
            <!-- <vxe-table-column
              field="salNo"
              :min-width="120"
              title="作业人员"
              align="center"
            >
              <template
                slot-scope="scope"
                v-if="scope.row.salName&&scope.row.salNo"
              >
                {{ scope.row.salNo }}{{scope.row.salName}}
              </template>
            </vxe-table-column>
            <vxe-table-column
              field="wgWt"
              :min-width="90"
              title="个人工时"
              align="center"
            ></vxe-table-column> -->
          </vxe-table>
          <vxe-pager
            :loading="loading"
            :current-page="tablePage.currentPage"
            :page-size="tablePage.pageSize"
            :total="tablePage.total"
            :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
            @page-change="handlePageChange"
          >
          </vxe-pager>
          <!-- <a-row :gutter="16">
            <a-col style="float:right">
              <a-button
                style="margin-top:20px;margin-right:20px"
                @click="onClose"
              >{{ $t('public.cancel') }}</a-button>
            </a-col>
          </a-row> -->

        </div>
      </a-spin>

      <ModelWg ref="model_wg" />
    </a-modal>
  </div>
</template>
<script>
import moment from 'moment'
import {
  getwgList
} from '@/api/report/unrestricted'
import ModelWg from './model_wg'
export default {
  components: {
    ModelWg
  },
  data () {
    return {
      spinning: false,
      title: '',
      spcDd: '',
      tableData: [],
      visible: false,
      loading: false,
      row: {},
      tablePage: {
        currentPage: 1,
        pageSize: 5,
        total: 0
      },
      row: {}
    }
  },
  methods: {
    onClose () {
      this.tableData = []
      this.spinning = false
      this.loading = false
      this.visible = false
    },
    touch (id) {
      this.$refs.model_wg.create({ title: '详情' }, id)
    },
    getList () {
      this.tableData = []
      this.spinning = true
      getwgList(
        Object.assign(
          {
            current: this.tablePage.currentPage,
            size: this.tablePage.pageSize,
            tzNo: this.row.tzNo,
            zcNo: this.row.zcNo2
          }
        )
      ).then(res => {
        this.tableData = res.data.records
        this.spinning = false
      })
        .catch(err => this.requestFailed(err))
        .finally(() => {
          this.spinning = false
        })
    },
    // 添加弹框
    create (model, row) {
      this.row = row
      this.getList()
      this.title = model.title
      this.visible = true
    },
    // 分页
    handlePageChange ({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getList()
    },
  }
}
</script>
