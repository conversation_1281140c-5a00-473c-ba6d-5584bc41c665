<template>
  <a-card :bordered="false">
    <a-spin :spinning="spinning">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col
              :md="6"
              :sm="24"
            >
              <a-form-item label="条码">
                <a-input
                  v-model="queryParam.barNo"
                  placeholder="请输入内容"
                  @keyup.enter.native="getBarcode()"
                />
              </a-form-item>
            </a-col>
            <a-col
              :md="6"
              :sm="24"
            >
              <a-form-item label="库位">
                <a-select
                  @change="handleChange"
                  @search="handleSearch"
                  showSearch
                  allowClear
                  placeholder="请输入内容查询"
                  style="width: 100%"
                  :filterOption="false"
                  v-model="queryParam.wh"
                >
                  <a-select-option
                    v-for="(i,index) in List"
                    :key="index"
                    :value="i.wh"
                  >{{ i.name }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col
              :md="6"
              :sm="24"
            >
              <a-form-item label="数量">
                <a-input-number
                  style="width:100%"
                  id="inputNumber"
                  :min="0"
                  :max="9999"
                  v-model="queryParam.inputQty"
                />
              </a-form-item>
            </a-col>

            <!-- <a-col
              :md="6"
              :sm="24"
            >
              <span class="table-page-search-submitButtons">
                <a-button
                  type="primary"
                  @click="getList"
                >{{ $t('public.query') }}</a-button>
                <a-button
                  style="margin-left: 8px"
                  @click="reset"
                >{{ $t('public.reset') }}</a-button>
              </span>
            </a-col> -->
          </a-row>
        </a-form>
      </div>
      <vxe-toolbar custom>
        <template v-slot:buttons>
          <!-- <a-dropdown :trigger="['click']">
          <a-button>{{ $t('public.action') }}<a-icon type="down"/></a-button>
          <a-menu slot="overlay">
            <a-menu-item key="0">
              <a @click="dropdownMenuEvent('closed ')">{{ $t('submission.closed') }}</a>
            </a-menu-item>
          </a-menu>
        </a-dropdown> -->
          <a-button
            style="margin-left:10px"
            type="primary"
            @click="scanned"
            size='small'
          >已扫明细</a-button>
          <a-button
            style="margin-left:10px"
            type="primary"
            size='small'
            @click="source"
          >选择来源菜单</a-button>
          <a-button
            size='small'
            style="margin-left:10px"
            type="primary"
            @click="subimt()"
          >提交</a-button>
        </template>
      </vxe-toolbar>
      <vxe-table
        border
        resizable
        stripe
        size='small'
        highlight-current-row
        show-overflow
        highlight-hover-row
        export-config
        height='200'
        ref="xTable"
        :loading="loading"
        :data="tableData"
        :keyboard-config="{ isArrow: true }"
        :seq-config="{startIndex: (tablePage.currentPage - 1) * tablePage.pageSize}"
        :edit-config="{ trigger: 'click', mode: 'row' }"
      >
        <vxe-table-column
          fixed="left"
          type="seq"
          title="序号"
          align="center"
          :width="60"
        > </vxe-table-column>
        <vxe-table-column
          field="prdNo"
          title="货品代号"
          align="center"
          :min-width='120'
        ></vxe-table-column>
        <vxe-table-column
          field="prdName"
          title="货品名称"
          align="center"
          :min-width='120'
        ></vxe-table-column>
        <vxe-table-column
          field="prdMark"
          title="货品特征"
          align="center"
        >
        </vxe-table-column>
        <vxe-table-column
          field="ut"
          title="单位"
          :min-width='90'
          align="center"
        ></vxe-table-column>
        <vxe-table-column
          field="whName"
          title="库位"
          align="center"
        ></vxe-table-column>
        <vxe-table-column
          field="qty"
          title="数量"
          align="center"
        ></vxe-table-column>
        <vxe-table-column
          field="barNoCount"
          title="扫描数量"
          align="center"
        ></vxe-table-column>
        <vxe-table-column
          field="batNo"
          title="批号"
          align="center"
        ></vxe-table-column>
      </vxe-table>
      <!-- <vxe-pager
        :loading="loading"
        :current-page="tablePage.currentPage"
        :page-size="tablePage.pageSize"
        :total="tablePage.total"
        :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
        @page-change="handlePageChange"
      >
      </vxe-pager> -->
      <Modal
        ref="modal"
        @getNumber='getNumber'
      />
      <ModalSou
        ref="modalSou"
        @getSouce='getSouce'
      />
    </a-spin>
  </a-card>
</template>

<script>
import { getPrdtBarcode, queryWh, get, insert } from '@/api/barcode/ordertransferware/treasury'
import Modal from './modal'
import ModalSou from './ModalSou'

export default {
  components: {
    Modal, ModalSou
  },
  data () {
    return {
      List: [],
      moNo: '',
      spinning: false,
      tableData: [],
      loading: false,
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      queryParam: {
      },
      formItemLayout: {
        labelCol: {
          md: { span: 24 },
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          md: { span: 24 },
          xs: { span: 24 },
          sm: { span: 15 }
        }
      }
    }
  },
  created () {
    // this.getList()
  },
  methods: {
    getBarcode () {
      if (
        this.queryParam.barNo == "" ||
        this.queryParam.barNo == null ||
        this.queryParam.barNo == undefined
      ) {
        return;
      }
      // this.spinning = true
      getPrdtBarcode(
        Object.assign(
          {
            code: this.queryParam.barNo,
            inputQty: this.queryParam.inputQty,
            // inputQtyType: true,
            type: "MO",
            no: this.moNo,
            wh: this.queryParam.wh
          }
        )
      ).then(response => {
        
        if ("success" == response.msg) {
          this.queryParam.count = response.data[0].qty;
          this.tableData = response.data[0].temps;
          this.$message.success(this.$t('public.success'))
        } else {
          this.$message.error(response.data)
        }
      })
        .catch(err => this.requestFailed(err))
        .finally(() => {
          this.spinning = false
        })
    },
    handleSearch (e) {
      queryWh(
        Object.assign(
          {
            wh: e,
            name: e
          }
        )
      ).then(res => {
      
        this.List = res.data.records
      })
        .catch(err => this.requestFailed(err))
        .finally(() => {
        })
    },
    handleChange (e) {
 
    },
    // 已扫明细
    scanned () {
      this.$refs.modal.create({ title: '已扫明细' }, this.moNo)
    },
    source () {
      this.$refs.modalSou.create({ title: '来源单号' })
    },
    handleUse (row) {
   
    },
    getNumber (data) {
      this.tableData = data
    },
    reset () {
      this.data = {}
      this.queryParam = {}
      this.datawh = ''
      this.fbStartDd = null
      this.fbEndDd = null
      this.bjStartDd = null
      this.bjEndDd = null
    },
    // 分页
    handlePageChange ({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getList()
    },
    getSouce (data) {
      this.moNo = data.moNo
      if (!this.moNo) {
        return;
      }
      this.spinning = true
      get(
        Object.assign(
          {
            bilNo: this.moNo
          },
        )
      ).then(response => {
        this.tableData = response.data;
        this.spinning = false
      }).catch(err => this.requestFailed(err))
        .finally(() => {
          this.spinning = false
        })
    },
    subimt () {
      if (this.tableData.length > 0) {
        this.spinning = true
        insert(Object.assign(
          {
            bilType: "MO",
            bilNo: this.moNo
          },
        )).then(response => {
          if ("success" == response.msg) {
            this.$message.success(response.data)
            this.spinning = false
          } else {
            this.$message.error(response.data)
            this.spinning = false
          }
        }).catch(err => this.requestFailed(err))
          .finally(() => {
            this.spinning = false
          })
      } else {
        this.$message.error("请先扫码数据!");
      }

      // insert(
      //   Object.assign({
      //     bilNo: this.moNo,
      //     bilType: "MO"
      //   }).then(response => {
      //     if ("success" == response.data.msg) {
      //       this.$message.success(response.data.data)
      //     } else {
      //       this.$message.error(response.data.data)
      //     }
      //     //this.page.total = response.data.total;
      //     this.spinning = false
      //   }).catch(err => this.requestFailed(err))
      //     .finally(() => {
      //       this.spinning = false
      //     })
      // )
    }
  }

}
</script>
<style lang="less">
</style>
