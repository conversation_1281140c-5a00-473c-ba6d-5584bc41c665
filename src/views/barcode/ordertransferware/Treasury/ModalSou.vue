<template>
  <div>
    <a-modal
      :title="title"
      destroyOnClose
      width="75%"
      :visible.sync="visible"
      @cancel="onClose"
      :footer="null"
      :maskClosable='false'
    >
      <a-spin :spinning="spinning">
        <div>
          <div class="table-page-search-wrapper">
            <a-form layout="inline">
              <a-row :gutter="48">
                <a-col
                  :md="8"
                  :sm="24"
                >
                  <a-form-item label="来源">
                    <a-input
                      v-model="queryForm.moNo"
                      placeholder="请输入内容"
                    />
                  </a-form-item>
                </a-col>
                <a-col
                  :md="8"
                  :sm="24"
                >
                  <a-form-item label="需求客户">
                    <a-input
                      v-model="queryForm.cusNo"
                      placeholder="请输入客户"
                    />
                  </a-form-item>
                </a-col>
                <a-col
                  :md="8"
                  :sm="24"
                >
                  <a-form-item label="客户订单">
                    <a-input
                      v-model="queryForm.cusOsNo"
                      placeholder="请输入客户订单"
                    />
                  </a-form-item>
                </a-col>
                <a-col
                  :md="8"
                  :sm="24"
                >
                  <a-form-item label="开工日期起">
                    <a-date-picker
                      style="width:100%"
                      v-model="staDd"
                      :placeholder="$t('scheduling.placeholder.spcDd')"
                    />
                  </a-form-item>
                </a-col>
                <a-col
                  :md="8"
                  :sm="24"
                >
                  <a-form-item label="开工日期止">
                    <a-date-picker
                      style="width:100%"
                      v-model="endDd"
                      :placeholder="$t('scheduling.placeholder.spcDd')"
                    />
                  </a-form-item>
                </a-col>
                <a-col
                  :md="8"
                  :sm="24"
                >
                  <a-form-item label="工程案号">
                    <a-select
                      show-search
                      style="width:100%"
                      v-model="queryForm.casNo"
                      placeholder="请输入工程案号"
                      :default-active-first-option="false"
                      :show-arrow="false"
                      :filter-option="false"
                      :not-found-content="null"
                      @focus="casnClick"
                    >
                      <a-select-option
                        v-for="item in casns"
                        :key="item.casNo"
                        :label="item.name"
                        :value="item.casNo"
                      >
                        {{ item.name }}
                      </a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col
                  :md="8"
                  :sm="24"
                >
                  <a-form-item label="单据类别">
                    <a-select
                      show-search
                      style="width:100%"
                      v-model="queryForm.bilType"
                      placeholder="请输入单据类别"
                      :default-active-first-option="false"
                      :show-arrow="false"
                      :filter-option="false"
                      :not-found-content="null"
                      @focus="bilSpcClick"
                    >
                      <a-select-option
                        v-for="item in options"
                        :key="item.spcNo"
                        :label="item.name"
                        :value="item.spcNo"
                      >
                        {{ item.name }}
                      </a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col style="float:right">
                  <span class="table-page-search-submitButtons">
                    <a-button
                      size='small'
                      type="primary"
                      @click="getList"
                    >{{ $t('public.query') }}</a-button>
                    <a-button
                      size='small'
                      style="margin-left: 8px"
                      @click="reset"
                    >{{ $t('public.reset') }}</a-button>
                  </span>
                </a-col>
              </a-row>
            </a-form>
          </div>
          <vxe-table
            border
            size='small'
            resizable
            stripe
            highlight-current-row
            show-overflow
            highlight-hover-row
            export-config
            ref="xTable"
            :loading="loading"
            @cell-click="cellClickEvent"
            :data="tableData"
            :keyboard-config="{ isArrow: true }"
            :seq-config="{startIndex: (tablePage.currentPage - 1) * tablePage.pageSize}"
            :radio-config="{labelField: 'name', trigger: 'row'}"
          >
            <vxe-table-column
              fixed="left"
              type='radio'
              align="center"
              :min-width="50"
            ></vxe-table-column>
            <vxe-table-column
              fixed="left"
              type="seq"
              title="序号"
              align="center"
              :width="60"
            > </vxe-table-column>
            <vxe-table-column
              field="staDd"
              title="预开"
              align="center"
              :min-width="150"
            ></vxe-table-column>
            <vxe-table-column
              field="moNo"
              :min-width="150"
              title="单号"
              align="center"
            ></vxe-table-column>
            <vxe-table-column
              field="bilName"
              :min-width="150"
              title="单据类别"
              align="center"
            ></vxe-table-column>
            <vxe-table-column
              field="endDd"
              :min-width="150"
              title="预完"
              align="center"
            ></vxe-table-column>
            <vxe-table-column
              field="mrpNo"
              :min-width="150"
              title="成品代号"
              align="center"
            ></vxe-table-column>
            <vxe-table-column
              field="prdName"
              :min-width="150"
              title="成品名称"
              align="center"
            ></vxe-table-column>
          </vxe-table>
          <vxe-pager
            style="margin-top:10px"
            :loading="loading"
            :current-page="tablePage.currentPage"
            :page-size="tablePage.pageSize"
            :total="tablePage.total"
            :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
            @page-change="handlePageChange"
          >
          </vxe-pager>
          <a-row :gutter="16">
            <a-col style="float:right">
              <a-button
                style="margin-top:20px;margin-right:10px"
                type="primary"
                @click="save"
              >{{ $t('public.save') }}</a-button>
              <a-button
                style="margin-top:20px;margin-right:20px"
                @click="onClose"
              >{{ $t('public.cancel') }}</a-button>
            </a-col>
          </a-row>

        </div>
      </a-spin>

    </a-modal>
  </div>
</template>
<script>
import moment from 'moment'
import { fetchList3, queryCasn, querybilSpc, fetchList } from '@/api/barcode/ordertransferware/treasury'
export default {
  data () {
    return {
      queryForm: {
        moNo: '',
        bilType: '',
        casNo: '',
        cusNo: '',
        cusOsNo: ''
      },
      staDd: null,
      endDd: null,
      casns: [],
      options: [],
      spinning: false,
      title: '',
      spcDd: '',
      tableData: [],
      visible: false,
      loading: false,
      row: {},
      tablePage: {
        currentPage: 1,
        pageSize: 5,
        total: 0
      },
    }
  },
  methods: {
    onClose () {
      this.tableData = []
      this.spinning = false
      this.loading = false
      this.visible = false
      this.reset()
    },
    casnClick () {
      queryCasn().then(response => {
        this.casns = response.data;
      }).catch(err => this.requestFailed(err));
    },
    bilSpcClick () {
      querybilSpc(
        Object.assign({
          bilId: "MO"
        })
      ).then(response1 => {
        this.options = response1.data;
      }).catch(err => this.requestFailed(err));
    },
    getList () {
      this.tableData = []
      this.spinning = true
      let staDd
      let endDd
      if (this.staDd !== null) {
        staDd = moment(this.staDd).format('YYYY-MM-DD')
      } else {
        staDd = null
      }
      if (this.endDd !== null) {
        endDd = moment(this.endDd).format('YYYY-MM-DD')
      } else {
        endDd = null
      }
      fetchList(
        Object.assign(
          {
            current: this.tablePage.currentPage,
            size: this.tablePage.pageSize,
            moNo: this.queryForm.moNo,
            staDd: staDd,
            endDd: endDd,
            bilType: this.queryForm.bilType,
            casNo: this.queryForm.casNo,
            cusNo: this.queryForm.cusNo,
            cusOsNo: this.queryForm.cusOsNo
          }
        )
      ).then(response => {
        this.tableData = response.data.records
        this.tablePage.total = response.data.total
        this.tablePage.currentPage = response.data.current
        this.spinning = false
      })
        .catch(err => this.requestFailed(err))
        .finally(() => {
          this.spinning = false
        })
    },
    reset () {
      this.queryForm = {
        endDd: '',
        bilType: '',
        casNo: '',
        cusNo: '',
        cusOsNo: ''
      }
      this.staDd = null
      this.endDd = null
    },
    cellClickEvent ({
      row
    }) {
      this.row = row
    },
    // 添加弹框
    create (model, row) {
      this.row = row
      this.title = model.title
      this.visible = true
      this.getList()
    },
    // 分页
    handlePageChange ({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getList()
    },
    save () {

      this.$emit('getSouce', this.row)
      this.onClose()

    }
  }
}
</script>
