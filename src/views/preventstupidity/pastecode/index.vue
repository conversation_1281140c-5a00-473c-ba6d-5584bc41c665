<template>
  <div class="layout">
    <vTable ref="vTable" v-bind="vTableProps" @toolbarClick="handleToolbarClick"  @cellDblclick="handleDblclick" :print-type="printType">
    </vTable>
    <CreateDialog ref="createRef" @refresh="handleSubmit"></CreateDialog>
  </div>
</template>
<script>
import vTable from '@/components/amtxts/vTable/vGrid.vue'
import { tmquery, tmdel } from '@/api/barcode/mfbarrmvrule'
import CreateDialog from './create.vue'
export default {
  components: {
    vTable, CreateDialog
  },
  data() {
    return {
      printType:0,
      vTableProps: {
        delete_key: '$ITEM',
        api_find: tmquery,
        api_delete: tmdel,
        toolbarItems: [
          { label: '贴码防呆拆码规则', value: 'title' },
        ],
        formDataRaw: [
          {field: 'fp.cusNo', type: 'input', },
          {field: 'fp.type', type: 'select',
            values:[
              {label: '指定', value: '1'},
              {label: '截取', value: '2'},
            ]
           },
        ],
        tableColumn: [
          { field: "cusNo", title: "fp.cusNo", },
          { field: "cusName", title: "fp.cusName", },
          { field: 'itm', title: 'fp.itm', },
          { field: 'fgh', title: 'fp.fgh',},
          { field: 'fghB', title: 'fp.fghB', },
          { field: 'fghE', title: 'fp.fghE', },
        ],
      }
    }
  },
  methods: {
    handleToolbarClick(params) {
      switch (params.code) {
        case 'create':
          this.$refs.createRef.handleVisible();
          break;
        default:
      }
    },
    handleDblclick(param) {
      this.$refs.createRef.handleEdit(param);
    },
    handleSubmit() {
			this.$refs.vTable.handleGet();
		}
  },
}
</script>
