<template>
<div class="consrm">
<div  style="text-align:right;margin-right:16px;position: absolute;top: 95px;right:0px;z-index:999">
   <a-button style="margin-left:10px" type="primary" icon="plus" @click="handleAdd()" v-permission="cusrule_add">{{
              $t('public.add')
            }}</a-button>
   <a-button style="margin-left: 8px" @click="reset" type="primary">{{ $t('public.reset') }}</a-button>     
             
  </div>
<el-tabs type="border-card">
      <el-tab-pane label="贴码防呆拆码规则">
  <a-card :bordered="false">
    <a-row :gutter="8">
      <a-col :span="24">
        <a-row>
          <div class="table-page-search-wrapper">
            <a-form layout="inline">
              <a-row :gutter="48">
                <a-col :md="8" :sm="24">
                  <a-form-item label="客户号">
                    <a-input v-model="queryParam.cusNo" placeholder="请输入客户号" />
                  </a-form-item>
                </a-col>
                <a-col :md="8" :sm="24">
                  <a-form-item label="类型">
                    <a-select v-model="queryParam.type" :placeholder="$t('mfbarrmvrule.placeholder.ruleType')">
                      <a-select-option value="1">
                        指定
                      </a-select-option>
                      <a-select-option value="2">
                        截取
                      </a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :md="8" :sm="24">
                  <span class="table-page-search-submitButtons">
                    <a-button type="primary" @click="getList" v-permission="barcode_mfbarrmvrule_search">{{ $t('public.query') }}</a-button>
                    
                  </span>
                </a-col>
              </a-row>
               
            </a-form>
          </div>
        </a-row>
        <vxe-toolbar custom>
          <template v-slot:buttons>
           
            <!-- cusrule_add -->
            <!-- <a-button type="primary" style="margin-left:10px;" @click="getList" >{{ $t('public.query') }}</a-button> -->
            <!-- v-permission="cusrule_search" -->
          </template>
        </vxe-toolbar>
        <vxe-table
          border
          resizable
          size="small"
          stripe
          highlight-current-row
          show-overflow
          highlight-hover-row
          export-config
          ref="xTable"
          :loading="loading"
          :data="tableData"
          :keyboard-config="{ isArrow: true }"
          @cell-dblclick="cellDBLClickEvent"
          :edit-config="{ trigger: 'click', mode: 'row' }"
        >
          <vxe-table-column field="cusNo" title="客户" align="center">
            <template v-slot="scope">
              <div>{{ scope.row.cusNo }}</div>
            </template>
          </vxe-table-column>
          <vxe-table-column field="cusName" title="客户名称" align="center"></vxe-table-column>
          <vxe-table-column field="itm" title="货品号位置" align="center"></vxe-table-column>
          <vxe-table-column field="fgh" title="分隔符" align="center"></vxe-table-column>
          <vxe-table-column field="fghB" title="分隔符开始" align="center"></vxe-table-column>
          <vxe-table-column field="fghE" title="分隔符结束" align="center"></vxe-table-column>
        </vxe-table>
        <vxe-pager
          :loading="loading"
          :current-page="tablePage.currentPage"
          :page-size="tablePage.pageSize"
          :total="tablePage.total"
          :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
          @page-change="handlePageChange"
        >
        </vxe-pager>

        <!-- 弹出框 -->
        <rule-Drawer ref="Drawer" @getList="getList" />
      </a-col>
    </a-row>
  </a-card>
      </el-tab-pane>
</el-tabs>
</div>
</template>
<script>
import { tmquery } from '@/api/barcode/mfbarrmvrule'
import ruleDrawer from './ruleDrawer'

export default {
  name: 'UserList',
  components: {
    ruleDrawer
  },
  data() {
    return {
      barcode_mfbarrmvrule_search: 'barcode_mfbarrmvrule_search',
      barcode_mfbarrmvrule_add: 'barcode_mfbarrmvrule_add',
      cusrule_add: 'cusrule_add',
      cusrule_search: 'cusrule_search',
      data: '',
      loading: false,
      loadingsync: false,
      tableData: [],
      replaceFields: {},
      roleTypeList: [],
      gData: [],
      form: this.$form.createForm(this),
      value: '',
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      treeExpandedKeys: [],
      expandedKeys: [],
      selectedRows: [],
      queryParam: {},
      deptId: ''
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 查询列表
    getList() {
      this.loading = true
      tmquery(
        Object.assign(
          {
            current: this.tablePage.currentPage,
            size: this.tablePage.pageSize
          },
          this.queryParam
        )
      )
        .then(res => {
          this.tableData = res.data.records
          this.tablePage.total = res.data.total
          this.tablePage.currentPage = res.data.current
          this.loading = false
        })
        .catch(err => {
          this.requestFailed(err)
          this.loading = false
        }).finally(() => {
            this.loading = false
        })
    },
    // 分页
    handlePageChange({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getList()
    },

    // 双击弹出编辑框
    cellDBLClickEvent({ row }) {
      this.$refs.Drawer.edit({ title: this.$t('public.Detailed') }, row)
    },

    // 重置按钮
    reset() {
      this.queryParam = {}
    },
    // 新增
    handleAdd(item) {
      this.$refs.Drawer.create({ title: this.$t('public.add') })
    }
  }
}
</script>
<style lang="less" scoped></style>
<style></style>
