<template>
  <div class="pastecode">
    <a-drawer
      placement="right"
      :closable="false"
      @close="onClose"
      :visible="visible"
      :destroyOnClose="true"
      width="70%"
    >
      <template slot="title">
        <span class="title-name">{{ title }}</span>
        <span v-if="this.modeType !== '0'" class="title-age">
          <a-dropdown v-permission="cusrule_del">
            <!-- cusrule_del -->
            <a-button class="ant-dropdown-link">
              {{ $t('public.action') }}
              <a-icon type="down" />
            </a-button>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="del()" >{{ $t('public.delete') }}</a>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>
      </template>
      <a-form-model layout="horizontal" ref="ruleForm" :rules="rules" :model="form">
        <a-row>
          <!-- <a-col :span="12">
            <a-form-model-item v-bind="formItemLayout" :label="$t('priority.cusNo')" prop="cusNo">
              <a-input :max-length="20" style="width:95%" :disabled="disabled"  v-model="form.cusNo" :placeholder="$t('priority.placeholder.cusNo')" />
            </a-form-model-item>
          </a-col> -->
          <a-col  :span="12">
            <a-form-model-item v-bind="formItemLayout" label="规则类型:" prop="bilType">
                    <a-select style="width:95%" v-model="form.bilType"  :disabled="disabled" @change="changeguize" :placeholder="$t('mfbarrmvrule.placeholder.ruleType')">
                      <a-select-option value="0">
                        条码
                      </a-select-option>
                      <a-select-option value="1">
                       客户
                      </a-select-option>
                    </a-select>
                  </a-form-model-item>
                </a-col>
          



          <a-col :span="12">
                <a-form-model-item
                  v-bind="formItemLayout"
                  :label="$t('invoice.customer')"
                  prop="cusNo"
                >
                  <my-selectList
                    url="/sunlikecode/cust/page2"
                    :tableColumn="$Column.srmcus"
                    :form="$Form.srmcus"
                    :data="data"
                    :disabled="disabled"
                    name="cusNo"
                    @choose="choose($event)"
                    ref="selectList"
                    :placeholder="$t('invoice.placeholder.customer')"
                  >
                  </my-selectList>
                  <div v-if="custip" style="height:10px;font-size:12px;color:#f00;margin-left:48px;margin:0;padding:0">请输入客户代号</div>
                </a-form-model-item>
                 
              </a-col>


          <!-- <a-col :span="12">
            <a-form-model-item v-bind="formItemLayout" label="客户名称:" prop="cusName">
              <a-input style="width:95%" :max-length="50" :disabled="formStatus" v-model="form.cusName" placeholder="请输入客户名称" />
            </a-form-model-item>
          </a-col> -->
          <a-col  :span="12">
            <a-form-model-item v-bind="formItemLayout" label="类型:" prop="type">
                    <a-select style="width:95%" v-model="form.type"  :disabled="formStatus" @change="changeselect" :placeholder="$t('mfbarrmvrule.placeholder.ruleType')">
                      <a-select-option value="1">
                        指定
                      </a-select-option>
                      <a-select-option value="2">
                        截取
                      </a-select-option>
                    </a-select>
                  </a-form-model-item>
                </a-col>



          <a-col :span="12">
            <a-form-model-item v-bind="formItemLayout" :label="$t('propertySettings.Separator')" prop="fgh">
              <a-input
              :max-length="2"
                :disabled="formStatus"
                v-model="form.fgh"
                :placeholder="$t('propertySettings.Separator')"
                style="width:95%"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="12" v-show="seat">
            <a-form-model-item v-bind="formItemLayout" label="货品号位置:" prop="itm">
              <a-input style="width:95%" :disabled="formStatus" v-model="form.itm" placeholder="请输入货品号位置" />
            </a-form-model-item>
          </a-col>
          
          <a-col :span="12" v-show="delimiter">
            <a-form-model-item v-bind="formItemLayout" label="分隔符开始:" prop="fghB">
              <a-input-number  :disabled="formStatus"  style="font-size:12px;width:95%" :max-length="2"  v-model="form.fghB" placeholder="分隔符开始" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12" v-show="delimiter">
            <a-form-model-item v-bind="formItemLayout" label="分隔符结束:" prop="fghE">
              <a-input-number  :disabled="formStatus" style="font-size:12px;width:95%" :max-length="2"  v-model="form.fghE" placeholder="分隔符结束" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
      <a-row :gutter="16" style="margin-top:10px">
        <a-col class="gutter-row" :span="12" style="text-align:right">
          <a-button type="primary" :loading="loading" v-if="modeType === '0'" @click="handleOK()" v-permission="barcode_mfbarrmvrule_save">{{
            $t('public.save')
          }}</a-button>
          <a-button
            type="primary"
            v-if="modeType==='1'"
            @click="handleMenuClick()"
          >{{ $t('public.edit') }}</a-button>
          <a-button type="primary" :loading="loading" v-if="modeType === '2'" @click="handleEdit()" v-permission="barcode_mfbarrmvrule_save">{{ $t('public.save') }}</a-button>
        </a-col>
        <a-col class="gutter-row" :span="12" style="text-align:left">
          <a-button @click="onClose">{{ $t('public.cancel') }}</a-button>
        </a-col>
      </a-row>
    </a-drawer>
  </div>
</template>

<script>
import {tmdel,tmsaveOrUpdate, insert, update, delObj } from '@/api/barcode/mfbarrmvrule'
import { custpagetwo} from '@/api/basic/cust'
import MySelectList from '@/components/MySelectList'
export default {
  components: {
    MySelectList
  },
  data() {
    
    return {
      barcode_mfbarrmvrule_del: 'barcode_mfbarrmvrule_del',
      barcode_mfbarrmvrule_save: 'barcode_mfbarrmvrule_save',
      cusrule_del:'cusrule_del',
      title: '',
      targetKeys: [],
      mockData: [],
      ruleType1: [
        {
          id: '2',
          name: '箱条码规则'
        },
        {
          id: '3',
          name: '批号规则'
        }
      ],
      ruleType2: [
        {
          id: 'CUS_OS_NO',
          name: '客户订单'
        },
        {
          id: 'BIL_NO',
          name: '单据号码'
        },
        {
          id: 'BIL_ID',
          name: '单据别'
        },
        {
          id: 'PRD_NO',
          name: '货品代号'
        },
        {
          id: 'PRD_MARK',
          name: '货品特征'
        },
        {
          id: 'IDX1',
          name: '中类'
        },
        {
          id: 'MRK',
          name: '品牌'
        },
        {
          id: 'BAR_CODE',
          name: '条形码'
        },
        {
          id: 'WH',
          name: '预设仓库'
        },
        {
          id: 'BAT_NO',
          name: '批号'
        },
        {
          id: 'UNIT',
          name: '单位'
        },
        {
          id: 'QTY',
          name: '数量'
        },
        {
          id: 'CUS_NO',
          name: '客户/厂商'
        },
        {
          id: 'CREATE_DD',
          name: '生产日期'
        },
        {
          id: 'SQ_NO',
          name: '流水号'
        },
        {
          id: 'SYS_DATE',
          name: '输入日期'
        },
        {
          id: 'PRINT_DATE',
          name: '打印日期'
        },
        {
          id: 'SAL_NO',
          name: '员工号'
        },
        {
          id: 'DEP',
          name: '机台'
        }
      ],
      ruleType3: [
        {
          id: 'CUS_OS_NO',
          name: '客户订单'
        },
        {
          id: 'BIL_NO',
          name: '来源单号'
        },
        {
          id: 'PRD_NO',
          name: '货品代号'
        },
        {
          id: 'PRD_MARK',
          name: '货品特征'
        },
        {
          id: 'IDX1',
          name: '中类'
        },
        {
          id: 'MRK',
          name: '品牌'
        },
        {
          id: 'BAR_CODE',
          name: '条形码'
        },
        {
          id: 'WH',
          name: '预设仓库'
        },
        {
          id: 'BAT_NO',
          name: '批号'
        },
        {
          id: 'UNIT',
          name: '单位'
        },
        {
          id: 'QTY',
          name: '数量'
        },
        {
          id: 'CUS_NO',
          name: '客户/厂商'
        },
        {
          id: 'SQ_NO',
          name: '流水号'
        },
        {
          id: 'CREATE_DD',
          name: '生产日期'
        },
        {
          id: 'SYS_DATE',
          name: '输入日期'
        },
        {
          id: 'PRINT_DATE',
          name: '打印日期'
        },
        {
          id: 'SAL_NO',
          name: '员工号'
        },
        {
          id: 'DEP',
          name: '机台'
        }
      ],
      data: [],
      disabled: false,
      visible: false,
      formStatus: false,
      loading: false,
      modeType: '',
      row: {},
      form: {
        itm:'',
        fghB:'',
        fghE:'',
        cusNo:'',
        bilType:'',
        cusName:'',
        type:'1',
      },
      delimiter:false,
      seat:true,
      rules: {
        // cusNo: [
        //   {
        //     required: true,
        //     message: this.$t('sub.placeholder.cusNo')
        //   }
        // ],
        bilType: [
          {
            required: true,
            message: '请输入规则类型'
          }
        ],
         type: [
          {
            required: true,
            message: '请输入类型'
          }
        ],
        fgh: [
          {
            required: true,
            message: '请输入分隔符'
          }
        ],
        // cusName: [
        //   {
        //     required: true,
        //     message: this.$t('mfbarrmvrule.placeholder.ruleType')
        //   }
        // ],        
      },
      formItemLayout: {
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          }
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 15
          }
        }
      },
      tablekehu:[],
       tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      data: '',
      custip:false
    }
  },

  watch: {
    // 'form.ruleType': {
    //   handler(val) {
    //     if (val === '1') {
    //       this.data = this.ruleType1
    //     } else if (val === '2') {
    //       this.data = this.ruleType2
    //     } else if (val === '3') {
    //       this.data = this.ruleType3
    //     }
  
    //   },
    //   deep: true, // 对象内部的属性监听，也叫深度监听
    //   immediate: true // 立即监听
    // }
  },
  mounted () {
    

  },
  methods: {
    choose (obj) {
      this.form.cusNo = obj.obj.data.cusNo
      if(!this.form.cusNo&&this.form.bilType==1){
        this.custip = true
      }else{
         this.custip = false
      }
      this.form[obj.obj.name] = obj.obj.data.cusNo
      this.form.cusName =obj.obj.value
    },
    changeselect(val){
      if(val==1){
        this.seat = true
        this.delimiter = false
      }else if(val==2){
        this.seat = false
        this.delimiter = true
      }
    },
    changeguize(val){
      if(!this.form.cusNo&&this.form.bilType==1){
        this.custip = true
      }else{
         this.custip = false
      }
    },
    // 取消
    onClose() {
      this.targetKeys = []
      this.mockData = []
      this.data = []
      this.loading = false
      this.visible = false
      this.form = {}
    },
    create(model, row) {
      this.form.type = '1'
      this.title = model.title
      this.modeType = '0'
      this.visible = true
      this.formStatus = false
      this.disabled = false
        this.seat = true
        this.delimiter = false
      //   custpagetwo(
      //   Object.assign(
      //     {
      //       // cusNo: this.queryParam.cusNo,
      //       // name: this.queryParam.name,
      //       current: this.tablePage.currentPage,
      //       size: this.tablePage.pageSize
      //     }
      //   )
      // )
      //   .then(res => {
      //     this.tableData = res.data.records
     
      //     this.tablePage.total = res.data.total
      //     this.tablePage.currentPage = res.data.current
      //     this.loading = false
      //   })
      //   .catch(err => this.requestFailed(err))
      //   .finally(() => {
      //     this.loading = false
      //   })
      
    },
    // 点击编辑按钮
    handleMenuClick() {
      
      if(this.row.type==1){
        this.seat = true
        this.delimiter = false
      }else if(this.row.type==2){
        this.seat = false
        this.delimiter = true
      }
       this.formStatus = false

      this.disabled = true
      this.modeType = '2'
      this.title = this.$t('public.edit')
    },
    edit(model, row) {
      
      this.title = model.title
      this.modeType = '1'
      this.row = row
      this.formStatus = true
      this.visible = true
      this.disabled = true

      this.seat = true
      this.delimiter = true
      this.title = this.$t('public.edit')

      this.form = {
        bilType:row.bilType,
        cusNo: row.cusNo,
        cusName: row.cusName,
        fgh: row.fgh,
        fghB: row.fghB,
        fghE: row.fghE,
        itm: row.itm,
        type: row.type,
      }
      this.data = row.cusName
      // if (row.ruleType === '1') {
      //   this.data = this.ruleType1
      // } else if (row.ruleType === '2') {
      //   this.data = this.ruleType2
      // } else if (row.ruleType === '3') {
      //   this.data = this.ruleType3
      // }
      // this.targetKeys = row.splitFields.split(',')
   
    },
    // 添加确认
    handleOK() {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          this.loading = true
          
          if(this.form.bilType == 1){
            if(this.form.cusNo){
            }else{
              this.loading = false
              this.$message.warning('请输入客户')
              return
            }
          }
          if(this.form.type ==1){
            if(this.form.itm){}else{
            this.loading = false
            this.$message.warning('请输入货品号位置')
              return
            }
          }else if(this.form.type ==2){
            if(this.form.fghE&&this.form.fghB){
              if(this.form.fghE <=this.form.fghB){
              this.loading = false
              this.$message.warning('分隔符开始不能大于等于分隔符结束')
              return
              }
            }else{
                this.loading = false
              this.$message.warning('请输入分隔符开始和结束')
              return
            }
            
          }
          
          
          // this.form.splitFields = this.targetKeys.join(',')
          // this.form.barLen = String(this.form.barLen)
          tmsaveOrUpdate(this.form)
          // insert(this.form)
            .then(res => {
              this.loading = false
              this.onClose()
              this.$emit('getList')
              this.$message.success(this.$t('public.success'))
            })
            .catch(err => this.requestFailed(err))
            .finally(() => {
              this.loading = false
            })
        } else {
          this.loading = false
          this.$message.error(this.$t('public.error'))
          return false
        }
      })
    },
    // 确认编辑
    handleEdit() {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          this.loading = true
          

          if(this.form.type ==1){
            if(this.form.itm){}else{
            this.loading = false
            this.$message.warning('请输入货品号位置')
              return
            }
          }else if(this.form.type ==2){
            if(this.form.fghE&&this.form.fghB){
              if(this.form.fghE <=this.form.fghB){
              this.loading = false
              this.$message.warning('分隔符开始不能大于等于分隔符结束')
              return
              }
            }else{
                this.loading = false
              this.$message.warning('请输入分隔符开始和结束')
              return
            }
            
          }
          // this.form.splitFields = this.targetKeys.join(',')
          // eslint-disable-next-line no-undef
          // update(this.form)
          tmsaveOrUpdate(this.form)
            .then(res => {
              this.loading = false
              this.onClose()
              this.$emit('getList')
              this.$message.success(this.$t('public.success'))
            })
            .catch(err => this.requestFailed(err))
            .finally(() => {
              this.loading = false
            })
        } else {
          this.loading = false
          this.$message.error(this.$t('public.error'))
          return false
        }
      })
    },
    del() {
      const that = this
      this.$confirm({
        title: this.$t('public.del.title'),
        content: this.$t('public.del.content'),
        okText: this.$t('public.sure'),
        okType: 'danger',
        cancelText: this.$t('public.cancel'),
        onOk() {
          that.loading = true
          const row = that.row
          // eslint-disable-next-line no-undef
          // delObj(row)
          tmdel(row)
            .then(() => {
              that.loading = false
              that.$emit('getList')
              that.onClose()
              that.$message.success(that.$t('public.success'))
            })
            .catch(err => {
              that.requestFailed(err)
              that.loading = false
            }).finally(() => {
                that.loading = false
            })
        },
        onCancel() {
          that.loading = false
        }
      })
    }
  }
}
</script>

<style lang="less">
.transfer {
  margin-left: 100px;
}
// .ant-form-item-with-help{
//   margin-bottom:0px;
// }
.has-error .ant-form-explain, .has-error .ant-form-split{font-size:12px;}
</style>
