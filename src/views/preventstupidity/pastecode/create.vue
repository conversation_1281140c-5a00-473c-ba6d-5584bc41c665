<template>
  <div>
    <!-- 新增弹窗 -->
    <el-drawer :title="$t(title)" :visible.sync="drawer" size="40%" ref="drawerRef" class="JustMake-drawer">
      <div class="drawer-content">
        <el-form ref="drawerFormRef" :model="formData" label-position="right" label-width="100px" class="drawer-form">
          <el-row>
            <el-col v-for="(item, index) in formDataRaw" :key="index" :span="item.span || 12">
              <el-form-item :label="$t(item.field)" :title="$t(item.field)" :prop="getPropName(item.field)"
                :rules="item.rules">
                <Forms :data="item" v-model="formData[getPropName(item.field)]" @iconClick="handelCompEvent"
                  @change="handleFormsChange" @selectListEvent="handleSelectListEvent"></Forms>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="drawer-footer">
        <el-button @click="handleCancel">{{ $t('public.cancel') }}</el-button>
        <el-button type="primary" @click="handleCreate" :loading="drawerLoading">{{ drawerLoading ? 'Loading ...' :
          $t('public.sure')
          }}</el-button>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import Forms from '@/components/Forms/index.vue'
import {tmsaveOrUpdate} from '@/api/barcode/mfbarrmvrule'
export default {
  components: {
    Forms,
  },
  data() {
    return {
      api_save: tmsaveOrUpdate,
      drawer: false,
      drawerLoading: false,
      title: 'public.add',
      status: '0', // 0 新增 1 编辑
      formData: {},
      currentRow: {},
      formDataRaw: [
        {
          field: 'fp.bilType', type: 'select',
          values: [{ label: '条码', value: '0' }, { label: '客户', value: '1' }],
          rules: [{ required: true, }],
        },
        {
          field: 'fp.cusNo', type: 'selectList',
          props: {
            url: "/sunlikecode/cust/page2",
            tableColumn: this.$Column.srmcus,
            form: this.$Form.srmcus
          }
        },
        {
          field: 'fp.type', type: 'select',
          values: [
            { label: '指定', value: '1' },
            { label: '截取', value: '2' },
          ],
          rules: [{ required: true, }],
        },
        { field: 'fp.fgh', type: 'input', rules: [{ required: true, }], },
        { field: 'fp.itm', type: 'input', },
        { field: 'fp.fghB', type: 'input', },
        { field: 'fp.fghE', type: 'input', },
      ]
    }
  },
  mounted() {

  },
  watch: {
    status: function (newVal) {
      if (newVal === '0') {
        this.title = 'public.add'
        this.api_save = tmsaveOrUpdate
      } else {
        this.title = 'public.edit'
        this.api_save = tmsaveOrUpdate
      }
    },
    'formData.type': function (newVal) {
      if (newVal === '1') {
        this.setDisabled('fp.itm', false)
        this.setDisabled('fp.fghB', true)
        this.setDisabled('fp.fghE', true)
        this.formData.fghB = ''
        this.formData.fghE = ''
      } else if (newVal === '2') {
        this.setDisabled('fp.itm', true)
        this.formData.itm = ''
        this.setDisabled('fp.fghB', false)
        this.setDisabled('fp.fghE', false)
      }
    }
  },
  methods: {
    handleVisible() {
      this.status = '0'
      this.formData = {}
      this.$nextTick(() => {
        this.$refs.drawerFormRef.clearValidate()
      })
      this.drawer = !this.drawer
    },
    getPropName(field) {
      return field.includes('.') ? field.split('.').pop() : field;
    },
    handleFormsChange(params) {
      this.$emit('formDataChange', params)
    },
    handleSelectListEvent(param) {
      this.$emit('selectListEvent', param)
    },
    handelCompEvent(params) {
      this.$emit('formItemIconClick', params)
    },
    handleCancel() {
      this.formData = {}
      this.$nextTick(() => {
        this.$refs.drawerFormRef.clearValidate()
      })
      this.drawer = false
    },
    async handleCreate() {
      if (this.drawerLoading) {
        return;
      }
      if (!this.api_save || typeof this.api_save !== 'function') {
        return this.$message.error('请配置 api_save 参数');
      }
      // 表单校验
      try {
        await this.$refs.drawerFormRef.validate();
      } catch (error) {
        return;
      }
      let result = null;
      try {
        this.drawerLoading = true;
        result = await this.api_save(this.formData)
        if (result.code == 0) {
          this.$message.success(this.$t('public.success'))
          this.$emit('refresh')
          this.drawer = false;
        }
      } catch (err) {
        console.error(err)
        this.$message.error(err || this.$t('public.error'));
      } finally {
        this.drawerLoading = false;
      }
    },
    handleEdit(row) {
      this.currentRow = row
      this.status = '1'
      this.drawer = true
      this.$nextTick(() => {
        this.formData = Object.assign({}, row) // 拷贝数据
      })
    },
    setDisabled(field, control) {
      const index = this.formDataRaw.findIndex(item => item.field === field);
      if (index !== -1) {
        this.$set(this.formDataRaw[index], 'disabled', control);
      }
    },
  },

}
</script>
