<template>
  <a-card :bordered="false">
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="48">
          <a-col
            :md="6"
            :sm="24"
          >
            <a-form-item :label="$t('classification.name')">
              <a-input
                v-model="queryParam.name"
                :placeholder="$t('classification.placeholder.name')"
              />
            </a-form-item>
          </a-col>
          <a-col
            :md="6"
            :sm="24"
          >
            <span class="table-page-search-submitButtons">
              <a-button
                type="primary"
                @click="getList"
              >{{ $t('public.query') }}</a-button>
              <a-button
                style="margin-left: 8px"
                @click="reset"
              >{{ $t('public.reset') }}</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <vxe-toolbar custom>
      <template v-slot:buttons>
        <a-button
          style="margin-left:10px"
          type="primary"
          icon="plus"
          @click="handleAdd()"
        >{{ $t('public.add') }}</a-button>
      </template>
    </vxe-toolbar>
    <vxe-table
      border
      resizable
      stripe
      highlight-current-row
      show-overflow
      highlight-hover-row
      export-config
      ref="xTable"
      @cell-dblclick="cellDBLClickEvent"
      :loading="loading"
      :data="tableData"
      :keyboard-config="{ isArrow: true }"
      :edit-config="{ trigger: 'click', mode: 'row' }"
    >
      <vxe-table-column
        field="name"
        title="classification.name"
        align="center"
      ></vxe-table-column>
      <vxe-table-column
        field="code"
        title="classification.code"
        align="center"
      ></vxe-table-column>
      <vxe-table-column
        field="description"
        title="classification.description"
        align="center"
      ></vxe-table-column>
      <vxe-table-column
        field="createuser"
        title="classification.createuser"
        align="center"
      ></vxe-table-column>
    </vxe-table>
    <vxe-pager
      :loading="loading"
      :current-page="tablePage.currentPage"
      :page-size="tablePage.pageSize"
      :total="tablePage.total"
      :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
      @page-change="handlePageChange"
    >
    </vxe-pager>
    <Drawer
      ref="Drawer"
      @getList="getList"
    />
  </a-card>
</template>

<script>
import { fetchList } from '@/api/process/classification'
import Drawer from './drawer'
export default {
  components: {
    Drawer
  },
  data () {
    return {
      tableData: [
      ],
      loading: false,
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      endDd: '',
      staDd: '',
      endOpen: false,
      queryParam: {
      },
      formItemLayout: {
        labelCol: {
          md: { span: 24 },
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          md: { span: 24 },
          xs: { span: 24 },
          sm: { span: 15 }
        }
      },
    }
  },
  created () {
    this.getList()
  },
  methods: {
    // 查询列表
    getList () {
      this.loading = true
      // eslint-disable-next-line no-undef
      fetchList(
        Object.assign(
          {
            current: this.tablePage.currentPage,
            size: this.tablePage.pageSize
          }, this.queryParam
        )
      )
        .then(res => {
          this.tableData = res.data.records
          this.tablePage.total = res.data.total
          this.tablePage.currentPage = res.data.current
          this.loading = false
        })
        .catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },

    handleUse (row) {
     
    },
    reset () {
      this.queryParam = {}
      this.endDd = ''
      this.staDd = ''
    },
    // 分页
    handlePageChange ({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getList()
    },
    // 添加
    handleAdd () {
      this.$refs.Drawer.create({ title: this.$t('public.add') })
    },
    cellDBLClickEvent ({ row }) {
      this.$refs.Drawer.edit({ title: this.$t('public.Detailed') }, row)
    }
  }

}
</script>
<style lang="less">
</style>
