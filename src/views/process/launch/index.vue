<template>
  <a-card :bordered="false">
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="48">
          <a-col
            :md="6"
            :sm="24"
          >
            <a-form-item :label="$t('launch.name')">
              <a-input
                v-model="queryParam.name"
                :placeholder="$t('launch.placeholder.name')"
              />
            </a-form-item>
          </a-col>
          <a-col
            :md="6"
            :sm="24"
          >
            <a-form-item :label="$t('launch.date')">
              <a-row :gutter="15">
                <a-col
                  :xxl="12"
                  :xl="24"
                  :lg="24"
                  :md="24"
                  :sm="24"
                >
                  <a-date-picker
                    :disabled-date="disabledStartDate"
                    v-model="staDd"
                    format="YYYY-MM-DD"
                    :placeholder="$t('launch.staDd')"
                    @openChange="handleStartOpenChange"
                  />
                </a-col>
                <a-col
                  :xxl="12"
                  :xl="24"
                  :lg="24"
                  :md="24"
                  :sm="24"
                >
                  <a-date-picker
                    v-model="endDd"
                    :disabled-date="disabledEndDate"
                    format="YYYY-MM-DD"
                    :placeholder="$t('launch.endDd')"
                    :open="endOpen"
                    @openChange="handleEndOpenChange"
                  />
                </a-col>
              </a-row>
            </a-form-item>
          </a-col>

          <a-col
            :md="6"
            :sm="24"
          >
            <span class="table-page-search-submitButtons">
              <a-button
                type="primary"
                @click="getList"
              >{{ $t('public.query') }}</a-button>
              <a-button
                style="margin-left: 8px"
                @click="reset"
              >{{ $t('public.reset') }}</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <vxe-toolbar custom>
      <template v-slot:buttons>
        <!-- <a-dropdown :trigger="['click']">
          <a-button>{{ $t('public.action') }}<a-icon type="down"/></a-button>
          <a-menu slot="overlay">
            <a-menu-item key="0">
              <a @click="dropdownMenuEvent('closed ')">{{ $t('submission.closed') }}</a>
            </a-menu-item>
          </a-menu>
        </a-dropdown> -->
        <!-- <a-button
          style="margin-left:10px"
          type="primary"
          icon="plus"
          @click="handleAdd()"
        >{{ $t('public.add') }}</a-button> -->
      </template>
    </vxe-toolbar>
    <vxe-table
      border
      resizable
      stripe
      highlight-current-row
      show-overflow
      highlight-hover-row
      export-config
      ref="xTable"
      :loading="loading"
      :data="tableData"
      @cell-dblclick="cellDBLClickEvent"
      :keyboard-config="{ isArrow: true }"
      :edit-config="{ trigger: 'click', mode: 'row' }"
    >
      <vxe-table-column
        field="name"
        title="launch.name"
        align="center"
      ></vxe-table-column>
      <vxe-table-column
        field="currentUser"
        title="launch.currentUser"
        align="center"
      ></vxe-table-column>
      <vxe-table-column
        field="type"
        title="launch.type"
        align="center"
      ></vxe-table-column>
      <vxe-table-column
        field="status"
        title="launch.status"
        align="center"
      ></vxe-table-column>
      <vxe-table-column
        field="wfName"
        title="launch.wfName"
        align="center"
      ></vxe-table-column>
      <vxe-table-column
        field="startTime"
        title="launch.startTime"
        align="center"
      ></vxe-table-column>

    </vxe-table>
    <vxe-pager
      :loading="loading"
      :current-page="tablePage.currentPage"
      :page-size="tablePage.pageSize"
      :total="tablePage.total"
      :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
      @page-change="handlePageChange"
    >
    </vxe-pager>
    <Drawer
      ref="Drawer"
      @getList="getList"
    />
  </a-card>
</template>

<script>
import { fetchList, findList } from '@/api/process/launch'
import moment from 'moment'
import Drawer from './drawer'
export default {
  components: {
    Drawer
  },
  data () {
    return {
      tableData: [
      ],
      loading: false,
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      endDd: '',
      staDd: '',
      endOpen: false,
      queryParam: {
      },
      formItemLayout: {
        labelCol: {
          md: { span: 24 },
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          md: { span: 24 },
          xs: { span: 24 },
          sm: { span: 15 }
        }
      },
    }
  },
  created () {
    this.getList()
  },
  watch: {
    staDd (val) {
      if (val) {
        this.queryParam.staDd = moment(val).format('YYYY-MM-DD 00:00:00')
      }
    },
    endDd (val) {
      if (val) {
        this.queryParam.endDd = moment(val).format('YYYY-MM-DD 23:59:59')
      }
    }
  },
  methods: {
    disabledStartDate (startValue) {
      const endValue = this.endDd
      if (!startValue || !endValue) {
        return false
      }
      return startValue.valueOf() > endValue.valueOf()
    },
    disabledEndDate (endValue) {
      const startValue = this.startDd
      if (!endValue || !startValue) {
        return false
      }
      return startValue.valueOf() >= endValue.valueOf()
    },
    handleStartOpenChange (open) {
      if (!open) {
        this.endOpen = true
      }
    },
    handleEndOpenChange (open) {
      this.endOpen = open
    },
    // 查询列表
    getList () {
      this.loading = true
      // eslint-disable-next-line no-undef
      fetchList(
        Object.assign(
          {
            current: this.tablePage.currentPage,
            size: this.tablePage.pageSize
          }, this.queryParam
        )
      )
        .then(res => {
      
          this.tableData = res.data.records
          this.tablePage.total = res.data.total
          this.tablePage.currentPage = res.data.current
          this.loading = false
        })
        .catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },

    handleUse (row) {
  
    },
    reset () {
      this.queryParam = {}
      this.endDd = ''
      this.staDd = ''
    },
    // 分页
    handlePageChange ({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getList()
    },
    // 添加
    // handleAdd () {
    //   this.$refs.Drawer.create({ title: this.$t('public.add') })
    // },
    cellDBLClickEvent ({ row }) {
      let data = []
      findList(
        Object.assign(
          {
            sessionsId: row.id
          }
        )
      )
        .then(res => {
          data = res.data
          this.$refs.Drawer.edit({ title: this.$t('launch.Detailed') }, data)

        })
        .catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })

    }
  }

}
</script>
<style lang="less">
</style>
