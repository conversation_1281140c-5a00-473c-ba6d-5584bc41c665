<template>
  <div>
    <a-drawer
      placement="right"
      :closable="false"
      @close="onClose"
      :visible="visible"
      :destroyOnClose="true"
      width="70%"
    >
      <template slot="title">
        <span class="title-name">{{ title }}</span>
        <span
          v-if="this.modeType!=='0'"
          class="title-age"
        >
        </span>
      </template>
      <vxe-table
        border
        resizable
        stripe
        highlight-current-row
        show-overflow
        highlight-hover-row
        export-config
        ref="xTable"
        :loading="loading"
        :data="tableData"
        :keyboard-config="{ isArrow: true }"
        :edit-config="{ trigger: 'click', mode: 'row' }"
      >
        <vxe-table-column
          field="taskId"
          title="launch.taskId"
          align="center"
        ></vxe-table-column>
        <vxe-table-column
          field="nodeName"
          title="launch.nodeName"
          align="center"
        ></vxe-table-column>
        <vxe-table-column
          field="userName"
          title="launch.userName"
          align="center"
        ></vxe-table-column>
        <vxe-table-column
          field="time"
          title="launch.time"
          align="center"
        ></vxe-table-column>
        <vxe-table-column
          field="taskOutComeAction"
          title="launch.taskOutComeAction"
          align="center"
        ></vxe-table-column>
        <vxe-table-column
          field="remark"
          title="launch.remark"
          align="center"
        ></vxe-table-column>

      </vxe-table>
      <!-- <a-form-model
        layout="horizontal"
        ref="ruleForm"
        :rules="rules"
        :model="form"
      >
        <a-row>
          <a-col>
            <a-form-model-item
              v-bind="formItemLayout"
              :label="$t('creation.idxNo')"
              prop="idxNo"
            >
              <a-input
                v-model="form.idxNo"
                :disabled="formStatus"
                :placeholder="$t('creation.placeholder.idxNo')"
              />
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-model-item
              v-bind="formItemLayout"
              :label="$t('authority.name')"
              prop="name"
            >
              <my-selectList
                url="/basic/dept/page"
                :read-only="true"
                :tableColumn="$Column.salmDep"
                :form="$Form.salmDep"
                :data="data"
                name="dep"
                :disabled="formStatus"
                @choose="choose($event)"
                ref="selectList"
                v-model="form.depNo"
                :placeholder="$t('salm.placeholder.depName')"
              ></my-selectList>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model> -->
      <a-row
        :gutter="16"
        style="margin-top:50px"
      >
        <a-col
          class="gutter-row"
          :span="24"
          style="text-align:center"
        >
          <a-button @click="onClose">{{ $t('public.cancel') }}</a-button>
        </a-col>
      </a-row>
    </a-drawer>
  </div>
</template>

<script>
import { add } from '@/api/fm/creation'
import MySelectList from '@/components/MySelectList'

import { mapGetters } from 'vuex'
export default {
  components: {
    MySelectList
  },
  data () {
    return {
      tableData: [
      ],
      loading: false,
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      title: '',
      disabled: false,
      visible: false,
      formStatus: false,
      loading: false,
      modeType: '',
      data: '',
      row: {},
      form: {},
      rules: {
        idxNo: [
          { required: true, message: this.$t('creation.placeholder.idxNo'), trigger: 'blur' }
        ],
        name: [
          { required: true, message: this.$t('creation.placeholder.centerNo'), trigger: 'blur' }
        ]
      },
      formItemLayout: {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        }
      }
    }
  },
  created () {

  },
  computed: {
    ...mapGetters(['permissions'])
  },
  methods: {
    choose (obj) {
      var map = {}
      if (obj.obj.name === 'upSalNo') {
        map[obj.obj.name] = obj.obj.data.salNo
      }
      if (obj.obj.name === 'dep') {
        map[obj.obj.name] = obj.obj.data.dep
      }
      this.form.setFieldsValue(map)
    },
    // 取消
    onClose () {
      this.loading = false
      this.visible = false
      this.tableData = []
    },
    // create (model, row) {
    //   this.title = model.title
    //   this.modeType = '0'
    //   this.visible = true
    //   this.formStatus = false
    //   this.disabled = false
    // },
    // 点击编辑按钮
    handleMenuClick () {
      this.disabled = false
      this.modeType = '2'
      this.title = this.$t('public.edit')
      this.formStatus = false
    },
    edit (model, data) {
    
      this.disabled = true
      this.title = model.title
      this.modeType = '1'
      this.visible = true
      this.tableData = data
    },
    // 添加确认
    handleOK () {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          this.loading = true
          add(this.form).then((res) => {
            this.loading = false
            this.onClose()
            this.$emit('getList')
            this.$message.success(this.$t('public.success'))
          }).catch(err => this.requestFailed(err))
            .finally(() => {
              this.loading = false
            })
        } else {
          this.loading = false
          this.$message.error(this.$t('public.error'))
          return false
        }
      })
    }
    // 确认编辑
    // handleEdit () {
    //   this.$refs.ruleForm.validate(valid => {
    //     if (valid) {
    //       this.loading = true
    //       this.form.id = this.row.id
    //       edit(this.form).then((res) => {
    //         this.loading = false
    //         this.onClose()
    //         this.$emit('getList')
    //         this.$message.success(this.$t('public.success'))
    //       })
    //     } else {
    //       this.loading = false
    //       this.$message.error(this.$t('public.error'))
    //       return false
    //     }
    //   })
    // }
  }
}
</script>
