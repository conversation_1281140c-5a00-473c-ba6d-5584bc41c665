<template>
  <div>
    <a-drawer
      placement="right"
      :closable="false"
      @close="onClose"
      :visible="visible"
      :destroyOnClose="true"
      width="70%"
    >
      <template slot="title">
        <span class="title-name">{{ title }}</span>
        <span
          v-if="this.modeType!='0'"
          class="title-age"
        >
          <a-dropdown>
            <a-button class="ant-dropdown-link">
              {{ $t('public.action') }}
              <a-icon type="down" />
            </a-button>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="delet()">{{ $t('public.delete') }}</a>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>
      </template>
      <a-form-model
        layout="horizontal"
        ref="ruleForm"
        :rules="rules"
        :model="form"
      >
        <a-row>
          <a-col>
            <a-form-model-item
              v-bind="formItemLayout"
              :label="$t('formEdit.name')"
            >
              <a-input
                v-model="form.name"
                :disabled="formStatus"
                :placeholder="$t('formEdit.placeholder.name')"
              />
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-model-item
              v-bind="formItemLayout"
              :label="$t('formEdit.code')"
            >
              <a-input
                v-model="form.code"
                :disabled="formStatus"
                :placeholder="$t('formEdit.placeholder.code')"
              />
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-model-item
              v-bind="formItemLayout"
              :label="$t('formEdit.formjsp')"
            >
              <a-input
                v-model="form.formjsp"
                :disabled="formStatus"
                :placeholder="$t('formEdit.placeholder.formjsp')"
              />
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-model-item
              v-bind="formItemLayout"
              :label="$t('formEdit.displayorder')"
            >
              <a-input
                type='number'
                v-model="form.displayorder"
                :disabled="formStatus"
                :placeholder="$t('formEdit.placeholder.displayorder')"
              />
            </a-form-model-item>
          </a-col>
          <a-col v-if="this.modeType==='0'">
            <a-form-model-item
              v-bind="formItemLayout"
              :label="$t('formEdit.groupType')"
              prop='groupType'
            >
              <a-select
                style="width:100%"
                :disabled="formStatus"
                v-model="form.groupType"
                :placeholder="$t('formEdit.placeholder.groupType')"
              >
                <a-select-option
                  v-for="(i,index) in data"
                  :key='index'
                  :value='i.id'
                >
                  {{ i.name}}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
      <a-row :gutter="16">
        <a-col
          class="gutter-row"
          :span="12"
          style="text-align:right"
        >
          <a-button
            type="primary"
            :loading="loading"
            v-if="modeType==='0'"
            @click="handleOK()"
          >{{ $t('public.save') }}</a-button>
          <a-button
            type="primary"
            v-if="modeType==='1'"
            @click="handleMenuClick()"
          >{{ $t('public.edit') }}</a-button>
          <a-button
            type="primary"
            :loading="loading"
            v-if="modeType==='2'"
            @click="handleEdit()"
          >{{ $t('public.save') }}</a-button>
        </a-col>
        <a-col
          class="gutter-row"
          :span="12"
          style="text-align:left"
        >
          <a-button @click="onClose">{{ $t('public.cancel') }}</a-button>
        </a-col>
      </a-row>
    </a-drawer>
  </div>
</template>

<script>
import { fetchList } from '@/api/process/formTypes'
import { add, del } from '@/api/process/formEdit'
import MySelectList from '@/components/MySelectList'
export default {
  components: {
    MySelectList
  },
  data () {
    return {
      tableData: [
      ],
      loading: false,
      title: '',
      disabled: false,
      visible: false,
      formStatus: false,
      loading: false,
      modeType: '',
      data: [],
      row: {},
      form: {},
      rules: {
        groupType: [{ required: true, message: this.$t('formEdit.placeholder.groupType'), trigger: 'blur' }]
      },
      formItemLayout: {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        }
      }
    }
  },
  created () {

  },
  methods: {
    // 获取上级数据
    getUp () {
      fetchList().then((res) => {
        this.data = res.data.records
      }).catch(err => this.requestFailed(err))
    },
    // 取消
    onClose () {
      this.form = {}
      this.loading = false
      this.visible = false
    },
    create (model, row) {
      this.getUp()
      this.title = model.title
      this.modeType = '0'
      this.visible = true
      this.formStatus = false
    },
    // 点击编辑按钮
    handleMenuClick () {
      this.modeType = '2'
      this.title = this.$t('public.edit')
      this.formStatus = false
    },
    edit (model, row) {
      this.getUp()
      this.disabled = true
      this.title = model.title
      this.modeType = '1'
      this.row = row
      this.visible = true
      this.formStatus = true
    
      this.form = {
        name: row.name,
        code: row.code,
        displayorder: row.displayorder,
        formjsp: row.formjsp
      }
    },
    delet () {
      const that = this
      this.$confirm({
        title: this.$t('public.del.title'),
        content: this.$t('public.del.content'),
        okText: this.$t('public.sure'),
        okType: 'danger',
        cancelText: this.$t('public.cancel'),
        onOk () {
          that.loading = true
          del({ id: that.row.id })
            .then(() => {
              that.loading = false
              that.onClose()
              that.$emit('getList')
              that.$message.success(that.$t('public.success'))
            })
            .catch(err => that.requestFailed(err))
            .finally(() => {
              that.loading = false
            })
        },
        onCancel () {
          that.loading = false
        }
      })
    },
    // 添加确认
    handleOK () {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          this.loading = true
          let obj = {
            ...this.form
          }
       
          add1(obj).then((res) => {
            this.loading = false
            this.onClose()
            this.$emit('getList')
            this.$message.success(this.$t('public.success'))
          }).catch(err => this.requestFailed(err))
            .finally(() => {
              this.loading = false
            })
        } else {
          this.loading = false
          this.$message.error(this.$t('public.error'))
          return false
        }
      })
    },
    // 确认编辑
    handleEdit () {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          this.loading = true
          this.form.id = this.row.id
          add(this.form).then((res) => {
            this.loading = false
            this.onClose()
            this.$emit('getList')
            this.$message.success(this.$t('public.success'))
          }).catch(err => this.requestFailed(err))
        } else {
          this.loading = false
          this.$message.error(this.$t('public.error'))
          return false
        }
      })
    }
  }
}
</script>
