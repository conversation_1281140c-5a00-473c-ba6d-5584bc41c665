<template>
  <a-card :bordered="false">
    <vxe-toolbar
      custom
      :refresh="{query: getList}"
    >
      <template v-slot:buttons>
        <a-button
          type="primary"
          icon="plus"
          @click="handleAdd()"
        >{{ $t('public.add') }}</a-button>
      </template>
    </vxe-toolbar>
    <vxe-table
      border
      resizable
      stripe
      highlight-current-row
      show-overflow
      highlight-hover-row
      export-config
      ref="xTable"
      :loading="loading"
      :data="tableData"
      :keyboard-config="{ isArrow: true }"
      :edit-config="{ trigger: 'click', mode: 'row' }"
    >
      <vxe-table-column
        field="orderName"
        title="examples.orderName"
        align="center"
      ></vxe-table-column>
      <vxe-table-column
        field="oderNo"
        title="examples.oderNo"
        align="center"
      ></vxe-table-column>
      <vxe-table-column
        field="batNo"
        title="examples.batNo"
        align="center"
      ></vxe-table-column>
      <vxe-table-column
        field="tenantId"
        title="examples.tenantId"
        align="center"
      ></vxe-table-column>
      <vxe-table-column
        field="prdNo"
        title="examples.prdNo"
        align="center"
      ></vxe-table-column>
      <vxe-table-column
        field="prdName"
        title="examples.prdName"
        align="center"
      ></vxe-table-column>
    </vxe-table>
    <vxe-pager
      :loading="loading"
      :current-page="tablePage.currentPage"
      :page-size="tablePage.pageSize"
      :total="tablePage.total"
      :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
      @page-change="handlePageChange"
    >
    </vxe-pager>
    <Drawer
      ref="Drawer"
      @getList="getList"
    />
  </a-card>
</template>

<script>
import { fetchList, findList } from '@/api/process/examples'
import Drawer from './drawer'
export default {
  components: {
    Drawer
  },
  data () {
    return {
      tableData: [
      ],
      loading: false,
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      endDd: '',
      staDd: '',
      endOpen: false,
      queryParam: {
      },
      formItemLayout: {
        labelCol: {
          md: { span: 24 },
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          md: { span: 24 },
          xs: { span: 24 },
          sm: { span: 15 }
        }
      },
    }
  },
  created () {
    this.getList()
  },

  methods: {
    // 查询列表
    getList () {
      this.loading = true
      // eslint-disable-next-line no-undef
      fetchList(
        Object.assign(
          {
            current: this.tablePage.currentPage,
            size: this.tablePage.pageSize
          }, this.queryParam
        )
      )
        .then(res => {
          this.tableData = res.data.records
          this.tablePage.total = res.data.total
          this.tablePage.currentPage = res.data.current
          this.loading = false
        })
        .catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    reset () {
      this.queryParam = {}
      this.endDd = ''
      this.staDd = ''
    },
    // 分页
    handlePageChange ({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getList()
    },
    // 添加
    handleAdd () {
      this.$refs.Drawer.create({ title: this.$t('public.add') })
    },
    cellDBLClickEvent ({ row }) {
      let data = []
      findList(
        Object.assign(
          {
            taskId: row.id
          }
        )
      )
        .then(res => {
          data = res.data
          this.$refs.Drawer.edit({ title: this.$t('launch.Detailed') }, data)

        })
        .catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })

    }
  }

}
</script>
<style lang="less">
</style>
