<template>
  <div>
    <a-drawer
      placement="right"
      :closable="false"
      @close="onClose"
      :visible="visible"
      :destroyOnClose="true"
      width="70%"
    >
      <template slot="title">
        <span class="title-name">{{ title }}</span>
        <span
          v-if="this.modeType!=='0'"
          class="title-age"
        >
          <a-dropdown>
            <a-button class="ant-dropdown-link">
              {{ $t('public.action') }}
              <a-icon type="down" />
            </a-button>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="delet()">{{ $t('public.delete') }}</a>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>
      </template>
      <a-form-model
        layout="horizontal"
        ref="ruleForm"
        :rules="rules"
        :model="form"
      >
        <a-row>
          <a-col>
            <a-form-model-item
              v-bind="formItemLayout"
              :label="$t('examples.processCode')"
            >
              <my-selectList
                :read-only="true"
                :data="data"
                name="processCode"
                :disabled="formStatus"
                @choose="choose($event)"
                :placeholder="$t('examples.processCode')"
              ></my-selectList>
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-model-item
              v-bind="formItemLayout"
              :label="$t('examples.version')"
            >
              <a-input
                v-model="list.version"
                :disabled="true"
                :placeholder="$t('examples.placeholder.version')"
              />
            </a-form-model-item>
          </a-col>

          <a-col>
            <a-form-model-item
              v-bind="formItemLayout"
              :label="$t('examples.tableId')"
            >
              <a-input
                :disabled="formStatus"
                v-model="list.tableId"
                :placeholder="$t('examples.placeholder.tableId')"
              />
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-model-item
              v-bind="formItemLayout"
              :label="$t('examples.orderName')"
            >
              <a-input
                :disabled="formStatus"
                v-model="form.orderName"
                :placeholder="$t('examples.placeholder.orderName')"
              />
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-model-item
              v-bind="formItemLayout"
              :label="$t('examples.bilType')"
            >
              <a-input
                :disabled="formStatus"
                v-model="form.bilType"
                :placeholder="$t('examples.placeholder.bilType')"
              />
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-model-item
              v-bind="formItemLayout"
              :label="$t('examples.oderNo')"
            >
              <a-input
                :disabled="formStatus"
                v-model="form.oderNo"
                :placeholder="$t('examples.placeholder.oderNo')"
              />
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-model-item
              v-bind="formItemLayout"
              :label="$t('examples.batNo')"
            >
              <a-input
                :disabled="formStatus"
                v-model="form.batNo"
                :placeholder="$t('examples.placeholder.batNo')"
              />
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-model-item
              v-bind="formItemLayout"
              :label="$t('examples.tenantId')"
            >
              <a-input
                :disabled="formStatus"
                v-model="form.tenantId"
                :placeholder="$t('examples.placeholder.tenantId')"
              />
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-model-item
              v-bind="formItemLayout"
              :label="$t('examples.money')"
            >
              <a-input
                :disabled="formStatus"
                v-model="form.money"
                :placeholder="$t('examples.placeholder.money')"
              />
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-model-item
              v-bind="formItemLayout"
              :label="$t('examples.prdNo')"
            >
              <a-input
                :disabled="formStatus"
                v-model="form.prdNo"
                :placeholder="$t('examples.placeholder.prdNo')"
              />
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-model-item
              v-bind="formItemLayout"
              :label="$t('examples.prdName')"
            >
              <a-input
                :disabled="formStatus"
                v-model="form.prdName"
                :placeholder="$t('examples.placeholder.prdName')"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
      <a-row :gutter="16">
        <a-col
          class="gutter-row"
          :span="12"
          style="text-align:right"
        >
          <a-button
            type="primary"
            :loading="loading"
            v-if="modeType==='0'"
            @click="handleOK()"
          >{{ $t('public.save') }}</a-button>
          <a-button
            type="primary"
            v-if="modeType==='1'"
            @click="handleMenuClick()"
          >{{ $t('public.edit') }}</a-button>
          <a-button
            type="primary"
            :loading="loading"
            v-if="modeType==='2'"
            @click="handleEdit()"
          >{{ $t('public.save') }}</a-button>
        </a-col>
        <a-col
          class="gutter-row"
          :span="12"
          style="text-align:left"
        >
          <a-button @click="onClose">{{ $t('public.cancel') }}</a-button>
        </a-col>
      </a-row>
    </a-drawer>
  </div>
</template>

<script>
import { add } from '@/api/process/examples'
import MySelectList from './components'
export default {
  components: {
    MySelectList
  },
  data () {
    return {
      title: '',
      disabled: false,
      data: '',
      visible: false,
      formStatus: false,
      loading: false,
      modeType: '',
      row: {},
      form: {
      },
      list: {
        version: '-1'
      },
      rules: {
      },
      formItemLayout: {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        }
      }
    }
  },
  created () {

  },

  methods: {
    // 获取组件列表
    choose (obj) {
      this.data = obj.obj.data.processCode
      this.list.processCode = obj.obj.data.processCode
    },
    // 取消
    onClose () {
      this.loading = false
      this.visible = false
      this.data = ''
      this.form = {}
      this.list = {}
    },
    create (model, row) {
      this.title = model.title
      this.modeType = '0'
      this.visible = true
      this.formStatus = false
      this.disabled = true
    },
    // 点击编辑按钮
    handleMenuClick () {
      this.disabled = false
      this.modeType = '2'
      this.title = this.$t('public.edit')
      this.formStatus = false
    },
    edit (model, row) {
      this.disabled = true
      this.title = model.title
      this.modeType = '1'
      this.row = row
      this.formStatus = true
      this.visible = true
      this.form = {
        verNo: row.verNo,
        name: row.name,
        stopId: row.stopId
      }
    },
    // 添加确认
    handleOK () {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          this.loading = true
          const obj = {
            ...this.list,
            data: {
              ...this.form
            }
          }
          add(obj).then((res) => {
            this.loading = false
            this.onClose()
            this.$emit('getList')
            this.$message.success(this.$t('public.success'))
          }).catch(err => this.requestFailed(err))
            .finally(() => {
              this.loading = false
            })
        } else {
          this.loading = false
          this.$message.error(this.$t('public.error'))
          return false
        }
      })
    }
  }
}
</script>
