<template>
  <!-- @cancel="handleCancel" modal 关闭提示 -->
  <a-modal
    :title="title"
    destroyOnClose
    width="40%"
    :visible.sync="visible"
    @cancel="handleCancel"
  >
    <a-form
      layout="inline"
      :form="form"
    >

      <a-form-item label='流程分类'>
        <vxe-select
          v-model="form.id"
          placeholder="选择流程分类"
        >
          <vxe-option
            v-for="(i,index) in selectList "
            :key="index"
            :value="i.id"
            :label="i.code"
          ></vxe-option>
        </vxe-select>
      </a-form-item>

      <a-form-item>
        <a-button
          type="primary"
          @click="getList"
        >{{ $t('public.query') }}</a-button>
        <a-button
          style="margin-left: 8px"
          @click="reset"
        >{{ $t('public.reset') }}</a-button>
      </a-form-item>

    </a-form>

    <vxe-grid
      resizable
      export-config
      border
      height="400"
      :pager-config="tablePage"
      :radio-config="{labelField: '', trigger: 'row'}"
      :loading="loading"
      @cell-click="cellClickEvent"
      :data="tableData"
      :columns="tableColumn"
      @form-submit="getList"
    ></vxe-grid>
    <template slot="footer">
      <a-button
        key="ok"
        @click="save"
      >{{ $t('public.sure') }}</a-button>
      <a-button
        key="cancel"
        @click="handleCancel"
      >{{ $t('public.cancel') }}</a-button>
    </template>
  </a-modal>
</template>

<script>

import axios from '@/router/axios'
import { getClass1 } from '@/api/process/examples'
export default {
  data () {
    return {
      tableColumn: [{ type: 'radio', width: 40 },
      { field: 'name', title: 'examples.name', align: 'center' },
      { field: 'version', title: 'examples.version', align: 'center' },
      { field: 'processCode', title: 'examples.processCode', align: 'center' },
      { field: 'createuser', title: 'examples.createuser', align: 'center' },
      { field: 'createtime', title: 'examples.createtim', align: 'center' }
      ],
      // eslint-disable-next-line vue/require-default-prop
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
        pageSizes: [10, 20, 50, 100, 200, 500],
        layouts: ['Sizes', 'PrevPage', 'Number', 'NextPage', 'Total'],
        perfect: true
      },
      form: {},
      selectList: [],
      row: {},
      loading: false,
      field: '',
      cellValue: '',
      title: '',
      visible: false,
      tableData: []
    }
  },

  watch: {
    'form.id': {
      handler (newValue, oldValue) {
        this.getList()
      },
      deep: true, // 对象内部的属性监听，也叫深度监听
      immediate: true // 立即监听
    }
  },
  methods: {
    getList () {
      axios({
        url: '/act/process/change',
        method: 'get',
        params: Object.assign(
          {
            current: this.tablePage.currentPage,
            size: this.tablePage.pageSize,
            id: this.form.id
          }
        )
      }).then(res => {
        this.tableData = res.data.records
        this.tablePage.total = res.data.total
        this.tablePage.currentPage = res.data.current
      }).catch(err => this.requestFailed(err))
    },

    async getClass () {
      try {
        const res = await getClass1(Object.assign({}))
        this.selectList = res.data
      } catch (err) {
        this.requestFailed(err)
      } finally {
        this.loading = false
      }

    },
    reset () {
      this.form = {}
    },
    // 分页
    handlePageChange ({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getList()
    },
    cellClickEvent ({ row }) {
      this.row = row
    },
    create (model) {
      this.title = model.title
      this.visible = true
      this.getList()
      this.getClass()
    },
    // 添加确认
    save () {
      // 多选状态
      if (this.multiple) {
        const selectRecords = this.$refs.xTable.getCheckboxRecords()
        const name = []
        const id = []
        selectRecords.forEach(i => {
          name.push(i.name)
          id.push(i.id)
        })
        this.$emit('touch', {
          name: name.join(','),
          id: id,
          data: selectRecords
        })
        this.visible = false
      } else {
        // 单选状态
        // 根据不同数据传递来的代号查询id
        this.$emit('touch', {
          name: this.row.name,
          id: this.row.id,
          data: this.row
        })
        this.visible = false
        this.id = ''
      }

    },
    handleCancel () {
      this.visible = false

    }

  }
}
</script>
