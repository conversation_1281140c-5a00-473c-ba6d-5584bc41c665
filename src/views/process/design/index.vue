<template>
  <a-card :bordered="false">
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="48">
          <a-col
            :md="6"
            :sm="24"
          >
            <a-form-item :label="$t('design.name')">
              <a-input
                v-model="queryParam.name"
                :placeholder="$t('design.placeholder.name')"
              />
            </a-form-item>
          </a-col>
          <a-col :span='6'>
            <span class="table-page-search-submitButtons">
              <a-button
                type="primary"
                @click="getList"
              >{{ $t('public.query') }}</a-button>
              <a-button
                style="margin-left: 8px"
                @click="reset"
              >{{ $t('public.reset') }}</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <vxe-toolbar
      custom
      :refresh="{query: getList}"
    >
      <template v-slot:buttons>
        <a-dropdown :trigger="['click']">
          <a-button>{{ $t('public.action') }}
            <a-icon type="down" />
          </a-button>
          <a-menu slot="overlay">
            <a-menu-item key="0">
              <a @click="dropdownMenuEvent('design')">{{ $t('design.design') }}</a>
            </a-menu-item>
            <a-menu-item key="1">
              <a @click="dropdownMenuEvent('approval')">{{ $t('public.delete') }}</a>
            </a-menu-item>
          </a-menu>
        </a-dropdown>
        <a-button
          style="margin-left:10px"
          type="primary"
          icon="plus"
          @click="handleAdd()"
        >{{ $t('public.add') }}</a-button>
      </template>
    </vxe-toolbar>
    <vxe-table
      border
      resizable
      stripe
      highlight-current-row
      show-overflow
      highlight-hover-row
      export-config
      ref="xTable"
      :loading="loading"
      :data="tableData"
      :keyboard-config="{ isArrow: true }"
      :edit-config="{ trigger: 'click', mode: 'row' }"
    >
      <vxe-table-column
        type="checkbox"
        fixed="left"
        align="center"
        :width="50"
      ></vxe-table-column>
      <vxe-table-column
        field="processcode"
        title="design.processcode"
        align="center"
      ></vxe-table-column>
      <vxe-table-column
        field="name"
        title="design.name"
        align="center"
      ></vxe-table-column>
      <vxe-table-column
        field="version"
        title="design.version"
        align="center"
      ></vxe-table-column>
      <vxe-table-column
        field="state"
        title="design.state"
        align="center"
      ></vxe-table-column>
      <vxe-table-column
        field="participantsDebar"
        title="design.ParticipantsDebar"
        align="center"
      >
        <template v-slot="scope">
          <div>
            {{scope.row.participantsDebar|part}}
          </div>
        </template>
      </vxe-table-column>
      <vxe-table-column
        field="debarIncludeSponsor"
        title="design.debarIncludeSponsor"
        align="center"
      >
        <template v-slot="scope">
          <a-tag
            color="blue"
            type="primary"
          >{{scope.row.debarIncludeSponsor===1? $t('public.T'): $t('public.F')}}</a-tag>
        </template>
      </vxe-table-column>
      <vxe-table-column
        field="typename"
        title="design.typename"
        align="center"
      ></vxe-table-column>
      <vxe-table-column
        field="createuser"
        title="design.createuser"
        align="center"
      ></vxe-table-column>
      <vxe-table-column
        field="createtime"
        title="design.createtime"
        align="center"
      ></vxe-table-column>
    </vxe-table>
    <vxe-pager
      :loading="loading"
      :current-page="tablePage.currentPage"
      :page-size="tablePage.pageSize"
      :total="tablePage.total"
      :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
      @page-change="handlePageChange"
    >
    </vxe-pager>
    <Drawer
      ref="Drawer"
      @getList="getList"
    />
  </a-card>
</template>

<script>
import { fetchList, del } from '@/api/process/design'
import Drawer from './drawer'
import moment from 'moment'
import {
  mapState
} from 'vuex'
export default {
  components: {
    Drawer
  },
  data () {
    return {
      endDd: null,
      staDd: null,
      endOpen: false,
      tableData: [
      ],
      loading: false,
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      queryParam: {
      },
      formItemLayout: {
        labelCol: {
          md: { span: 24 },
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          md: { span: 24 },
          xs: { span: 24 },
          sm: { span: 15 }
        }
      },
    }
  },
  filters: {
    part (val) {
      const a = {
        'Debar_no_one': '不排除',
        'Debar_Previous_All': '排除所有已办活动人',
        'Debar_Previous': '	排除前一活动人'
      }
      return a[val]
    }
  },
  computed: {
    ...mapState({
      process: state => state.user.process
    })
  },
  created () {
    this.getList()
  },
  methods: {
    // 查询列表
    getList () {
      this.loading = true
      // eslint-disable-next-line no-undef
      fetchList(
        Object.assign(
          {
            current: this.tablePage.currentPage,
            size: this.tablePage.pageSize
          }, this.queryParam
        )
      )
        .then(res => {
          this.tableData = res.data.records
          this.tablePage.total = res.data.total
          this.tablePage.currentPage = res.data.current
          this.loading = false
        })
        .catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    reset () {
      this.queryParam = {}
      this.endDd = null
      this.staDd = null
    },
    // 分页
    handlePageChange ({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getList()
    },
    // 添加
    handleAdd () {
      this.$refs.Drawer.create({ title: this.$t('public.add') })
    },
    cellDBLClickEvent ({ row }) {
      this.$refs.Drawer.edit({ title: this.$t('public.Detailed') }, row)
    },
    dropdownMenuEvent (name) {
      const selectRecords = this.$refs.xTable.getCheckboxRecords()
      switch (name) {
        case 'approval': {
          const that = this
          if (selectRecords.length > 0 && selectRecords.length === 1) {
            this.$confirm({
              title: this.$t('public.del.title'),
              content: this.$t('public.del.content'),
              okText: this.$t('public.sure'),
              okType: 'danger',
              cancelText: this.$t('public.cancel'),
              onOk () {
                that.loading = true
                del({ id: selectRecords[0].id })
                  .then(() => {
                    that.loading = false
                    that.getList()
                    that.$message.success(that.$t('public.success'))
                  })
                  .catch(err => that.requestFailed(err))
                  .finally(() => {
                    that.loading = false
                  })
              },
              onCancel () {
                that.loading = false
              }
            })

          } else {
            this.loading = false
            this.$message.warning(this.$t('design.list'))
          }
          break
        }
        case 'design': {
          if (selectRecords.length > 0 && selectRecords.length === 1) {
            let url = `${this.process.url}viewServlet?processid=${selectRecords[0].id}&sessionId=${this.process.sessionId}`
            window.open(url, '_blank');
          } else {
            this.loading = false
            this.$message.warning(this.$t('design.designlist'))
          }
          break

        }

      }
    }
  }

}
</script>
<style lang="less">
</style>
