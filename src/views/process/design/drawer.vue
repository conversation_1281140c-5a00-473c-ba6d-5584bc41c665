<template>
  <div>
    <a-drawer
      placement="right"
      :closable="false"
      @close="onClose"
      :visible="visible"
      :destroyOnClose="true"
      width="70%"
    >
      <template slot="title">
        <span class="title-name">{{ title }}</span>
        <span
          v-if="this.modeType!='0'"
          class="title-age"
        >
          <a-dropdown>
            <a-button class="ant-dropdown-link">
              {{ $t('public.action') }}
              <a-icon type="down" />
            </a-button>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="delet()">{{ $t('public.delete') }}</a>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>
      </template>
      <a-form-model
        layout="horizontal"
        ref="ruleForm"
        :rules="rules"
        :model="form"
      >
        <a-row>
          <a-col>
            <a-form-model-item
              v-bind="formItemLayout"
              :label="$t('design.typename')"
            >
              <a-select
                style="width:100%"
                :disabled="formStatus"
                v-model="form.workflowprotypeId"
                :placeholder="$t('design.placeholder.typename')"
              >
                <a-select-option
                  v-for="(i,index) in data"
                  :key='index'
                  :value='i.id'
                >
                  {{ i.name}}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-model-item
              v-bind="formItemLayout"
              :label="$t('authority.name')"
            >
              <my-selectList
                url="/admin/dept/page"
                :read-only="true"
                :tableColumn="$Column.fmDep"
                :form="$Form.fmDep"
                :data="data1"
                name="orgid"
                :disabled="formStatus"
                @choose="choose($event)"
                ref="selectList"
                v-model="form.orgid"
                :placeholder="$t('salm.placeholder.depName')"
              ></my-selectList>
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-model-item
              v-bind="formItemLayout"
              :label="$t('design.ParticipantsDebar')"
            >
              <a-select
                v-model="form.participantsDebar"
                style="width:100%"
                :placeholder="$t('design.placeholder.ParticipantsDebar')"
              >
                <a-select-option value="Debar_no_one">
                  不排除
                </a-select-option>
                <a-select-option value="Debar_Previous_All">
                  排除所有已办活动人
                </a-select-option>
                <a-select-option value="Debar_Previous">
                  排除前一活动人
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-model-item
              v-bind="formItemLayout"
              :label="$t('design.debarIncludeSponsor')"
            >
              <a-radio-group
                :disabled="formStatus"
                v-model="form.debarIncludeSponsor"
              >
                <a-radio-button
                  :value="1"
                  style="margin-right:20px"
                >{{ $t('public.T') }}</a-radio-button>
                <a-radio-button :value="0">{{ $t('public.F') }}</a-radio-button>
              </a-radio-group>
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-model-item
              v-bind="formItemLayout"
              :label="$t('design.processcode')"
            >
              <a-input
                v-model="form.processvercode"
                :disabled="formStatus"
                :placeholder="$t('design.placeholder.processcode')"
              />
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-model-item
              v-bind="formItemLayout"
              :label="$t('design.name')"
            >
              <a-input
                v-model="form.name"
                :disabled="formStatus"
                :placeholder="$t('design.placeholder.name')"
              />
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-model-item
              v-bind="formItemLayout"
              :label="$t('design.description')"
            >
              <a-input
                type='textarea'
                v-model="form.description"
                :disabled="formStatus"
                :placeholder="$t('design.placeholder.description')"
              />
            </a-form-model-item>
          </a-col>

        </a-row>
      </a-form-model>
      <a-row :gutter="16">
        <a-col
          class="gutter-row"
          :span="12"
          style="text-align:right"
        >
          <a-button
            type="primary"
            :loading="loading"
            v-if="modeType==='0'"
            @click="handleOK()"
          >{{ $t('public.save') }}</a-button>
          <a-button
            type="primary"
            v-if="modeType==='1'"
            @click="handleMenuClick()"
          >{{ $t('public.edit') }}</a-button>
          <a-button
            type="primary"
            :loading="loading"
            v-if="modeType==='2'"
            @click="handleEdit()"
          >{{ $t('public.save') }}</a-button>
        </a-col>
        <a-col
          class="gutter-row"
          :span="12"
          style="text-align:left"
        >
          <a-button @click="onClose">{{ $t('public.cancel') }}</a-button>
        </a-col>
      </a-row>
    </a-drawer>
  </div>
</template>

<script>
import { add, del } from '@/api/process/design'
import { fetchList } from '@/api/process/classification'
import MySelectList from '@/components/MySelectList'
export default {
  components: {
    MySelectList
  },
  data () {
    return {
      tableData: [
      ],
      loading: false,
      title: '',
      disabled: false,
      visible: false,
      formStatus: false,
      loading: false,
      modeType: '',
      data: [],
      data1: '',
      row: {},
      form: {},
      rules: {
      },
      formItemLayout: {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        }
      }
    }
  },
  created () {

  },
  methods: {
    choose (obj) {
      this.form[obj.obj.name] = obj.obj.id
    },
    // 获取上级数据
    getType () {
      fetchList().then((res) => {
        this.data = res.data.records
      }).catch(err => this.requestFailed(err))
    },
    // 取消
    onClose () {
      this.form = {}
      this.loading = false
      this.visible = false
    },
    create (model, row) {
      this.getType()
      this.title = model.title
      this.modeType = '0'
      this.visible = true
      this.formStatus = false
    },
    // 点击编辑按钮
    handleMenuClick () {
      this.modeType = '2'
      this.title = this.$t('public.edit')
      this.formStatus = false
    },
    edit (model, row) {
      this.getType()
      this.disabled = true
      this.title = model.title
      this.modeType = '1'
      this.row = row
      this.visible = true
      this.formStatus = true
      this.form = {
        name: row.name,
        workflowprotypeId: row.workflowprotypeId,
        participantsDebar: row.participantsDebar,
        description: row.description,
        debarIncludeSponsor: row.debarIncludeSponsor,
        processvercode: row.processcode
      }
    },
    delet () {
      const that = this
      this.$confirm({
        title: this.$t('public.del.title'),
        content: this.$t('public.del.content'),
        okText: this.$t('public.sure'),
        okType: 'danger',
        cancelText: this.$t('public.cancel'),
        onOk () {
          that.loading = true
          del({ id: that.row.id })
            .then(() => {
              that.loading = false
              that.onClose()
              that.$emit('getList')
              that.$message.success(that.$t('public.success'))
            })
            .catch(err => that.requestFailed(err))
            .finally(() => {
              that.loading = false
            })
        },
        onCancel () {
          that.loading = false
        }
      })
    },
    // 添加确认
    handleOK () {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          this.loading = true
          let obj = {
            ...this.form
          }
          add(obj).then((res) => {
            this.loading = false
            this.onClose()
            this.$emit('getList')
            this.$message.success(this.$t('public.success'))
          }).catch(err => this.requestFailed(err))
            .finally(() => {
              this.loading = false
            })
        } else {
          this.loading = false
          this.$message.error(this.$t('public.error'))
          return false
        }
      })
    },
    // 确认编辑
    handleEdit () {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          this.loading = true
          this.form.id = this.row.id
          add(this.form).then((res) => {
            this.loading = false
            this.onClose()
            this.$emit('getList')
            this.$message.success(this.$t('public.success'))
          }).catch(err => this.requestFailed(err))
        } else {
          this.loading = false
          this.$message.error(this.$t('public.error'))
          return false
        }
      })
    }
  }
}
</script>
