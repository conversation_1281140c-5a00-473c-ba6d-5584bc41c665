<template>
  <div>
    <a-modal
      :title="title"
      destroyOnClose
      width="70%"
      :visible.sync="visible"
      @cancel="handleCancel"
    >

      <div v-if="sessionsId">
        <Approval
          :sessionsId='sessionsId'
          :data='data'
        />
      </div>
      <div v-else>
        <a-empty />
      </div>
      <a-divider />
      <a-form-model
        layout="horizontal"
        ref="ruleForm"
        :rules="rules"
        :model="form"
      >
        <a-row>
          <a-col :span="24">
            <a-form-model-item
              v-bind="formItemLayout"
              :label="$t('dealt.rem')"
            >
              <a-input
                type='textarea'
                v-model="form.suggestion"
                :placeholder="$t('dealt.placeholder.rem')"
              />
            </a-form-model-item>
          </a-col>
          <!-- <a-col :span="24">
            <a-form-model-item
              v-bind="formItemLayout"
              :label="$t('file.enclosure')"
            >
              <a-upload-dragger
                name="file"
                multiple
                :fileList="fileList"
                :remove="handleRemove"
                :beforeUpload="beforeUpload"
              >
                <p class="ant-upload-drag-icon">
                  <a-icon type="inbox" />
                </p>
                <p class="ant-upload-hint">
                  {{ $t('file.tip') }}
                </p>
              </a-upload-dragger>
            </a-form-model-item>
          </a-col> -->
        </a-row>
      </a-form-model>
      <template
        slot="footer"
        v-if="this.status==='2'"
      >
        <a-button
          v-for="(i,index) in btnList"
          :key="index"
          @click="handleOK(i.text)"
        >
          {{i.text}}
        </a-button>
        <!-- <a-button
          v-if="this.data.currentTaskVos.length>1"
          @click="handleOK('disagree')"
        >
          {{this.data.currentTaskVos[1].text}}
        </a-button> -->
      </template>
    </a-modal>
  </div>
</template>

<script>
import { approve } from '@/api/process/dealt'
import Approval from '@/components/Approval'
// import MySelectList from '@/components/MySelectList'
export default {
  components: {
    Approval
  },
  data () {
    return {
      status: '1', // 新增或者编辑状态
      title: '',
      data: {},
      row: {},
      form: {},
      fileList: [],
      rules: {},
      id: '',
      btnList: [],
      sessionsId: '',
      formItemLayout: {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        }
      },
      disabled: false,
      visible: false
    }
  },
  created () {

  },
  methods: {
    handleRemove (file) {
      const index = this.fileList.indexOf(file)
      const newFileList = this.fileList.slice()
      newFileList.splice(index, 1)
      this.fileList = newFileList
    },
    beforeUpload (file) {
      this.fileList = [...this.fileList, file]
      return false
    },
    create (model, data, id, sessionsId) {
      this.id = id
      this.sessionsId = sessionsId
      this.data = data
      this.btnList = data.currentTaskVos
   
      this.status = '2'
      this.title = model.title
      this.visible = true
    },
    // 添加确认
    // handleOK () {
    //   this.visible = false
    // },
    handleCancel () {
      this.status = ''
      this.visible = false
      this.disabled = false
      this.form = {}
    },

    del (row) {
      const that = this
      this.$confirm({
        title: this.$t('public.del.title'),
        content: this.$t('public.del.content'),
        okText: this.$t('public.sure'),
        okType: 'danger',
        cancelText: this.$t('public.cancel'),
        onOk () {
          const id = { id: row.id }
          delWh(id)
            .then(() => {
              that.getDept()
              that.$message.success(that.$t('public.success'))
            }).catch(err => that.requestFailed(err))
          that.$message.error(that.$t('public.error'))
        },
        onCancel () {
        }
      })
    },
    //  同意否
    handleOK (name, row) {
      const obj = {
        name: this.data.currentTaskVos.find(i => i.text === name).name,
        text: name,
        suggestion: this.form.suggestion,
        taskId: this.id
      }
      approve(
        Object.assign(
          obj
        )
      )
        .then(res => {
          if (res) {
            this.loading = false
            this.handleCancel()
            this.$emit('getList')
            this.$message.success(this.$t('public.success'))
          }
        })
        .catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    }

  }
}
</script>
