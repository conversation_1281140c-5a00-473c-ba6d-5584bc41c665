<template>
  <a-card :bordered="false">
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="48">
          <a-col
            :md="6"
            :sm="24"
          >
            <a-form-item :label="$t('dealt.name')">
              <a-input
                v-model="queryParam.name"
                :placeholder="$t('dealt.placeholder.name')"
              />
            </a-form-item>
          </a-col>
          <a-col
            :md="6"
            :sm="24"
          >
            <a-form-item :label="$t('dealt.sessions')">
              <a-input
                v-model="queryParam.sessions"
                :placeholder="$t('dealt.placeholder.sessions')"
              />
            </a-form-item>
          </a-col>
          <a-col
            :md="6"
            :sm="24"
          >
            <a-form-item :label="$t('dealt.date')">
              <a-row :gutter="15">
                <a-col
                  :xxl="12"
                  :xl="24"
                  :lg="24"
                  :md="24"
                  :sm="24"
                >
                  <a-date-picker
                    :disabled-date="disabledStartDate"
                    v-model="staDd"
                    format="YYYY-MM-DD"
                    :placeholder="$t('dealt.staDd')"
                    @openChange="handleStartOpenChange"
                  />
                </a-col>
                <a-col
                  :xxl="12"
                  :xl="24"
                  :lg="24"
                  :md="24"
                  :sm="24"
                >
                  <a-date-picker
                    v-model="endDd"
                    :disabled-date="disabledEndDate"
                    format="YYYY-MM-DD"
                    :placeholder="$t('dealt.endDd')"
                    :open="endOpen"
                    @openChange="handleEndOpenChange"
                  />
                </a-col>
              </a-row>
            </a-form-item>
          </a-col>

          <a-col
            :md="6"
            :sm="24"
          >
            <span class="table-page-search-submitButtons">
              <a-button
                type="primary"
                @click="getList"
              >{{ $t('public.query') }}</a-button>
              <a-button
                style="margin-left: 8px"
                @click="reset"
              >{{ $t('public.reset') }}</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <vxe-toolbar custom>
      <template v-slot:buttons>
        <a-dropdown :trigger="['click']">
          <a-button>{{ $t('public.action') }}
            <a-icon type="down" />
          </a-button>
          <a-menu slot="overlay">
            <a-menu-item key="0">
              <a @click="dropdownMenuEvent('approval')">{{ $t('dealt.approval') }}</a>
            </a-menu-item>
          </a-menu>
        </a-dropdown>
        <!-- <a-button
          style="margin-left:10px"
          type="primary"
          icon="plus"
          @click="handleAdd()"
        >{{ $t('public.add') }}</a-button> -->
      </template>
    </vxe-toolbar>

    <!-- border
    resizable
    stripe
    highlight-current-row
    show-overflow
    highlight-hover-row
    export-config
    ref="xTable"
    :loading="loading"
    :data="tableData"

    :keyboard-config="{ isArrow: true }"
    :edit-config="{ trigger: 'click', mode: 'row' }" -->
    <vxe-table
      border
      ref="xTable"
      highlight-hover-row
      highlight-current-row
      @cell-dblclick="cellDBLClickEvent"
      :radio-config="{ trigger: 'row'}"
      :data="tableData"
    >
      <vxe-table-column
        type="radio"
        fixed="left"
        align="center"
        :width="50"
      ></vxe-table-column>

      <vxe-table-column
        field="assignee"
        title="dealt.assignee"
        align="center"
      ></vxe-table-column>
      <vxe-table-column
        field="name"
        title="dealt.name"
        align="center"
      ></vxe-table-column>
      <vxe-table-column
        field="taskType"
        title="dealt.taskType"
        align="center"
      ></vxe-table-column>
      <vxe-table-column
        field="proxy"
        title="dealt.proxy"
        align="center"
      >
        <template v-slot="scope">
          <a-tag
            color='blue'
            type='primary'
            v-if="scope.row.proxy!=null"
          >
            {{$t('dealt.'+scope.row.proxy)}}
          </a-tag>
        </template>
      </vxe-table-column>
      <vxe-table-column
        field="delegate"
        title="dealt.delegate"
        align="center"
      ></vxe-table-column>
      <vxe-table-column
        field="assignTime"
        title="dealt.assignTime"
        align="center"
      ></vxe-table-column>
      <vxe-table-column
        field="dynamicdelegate"
        title="dealt.dynamicdelegate"
        align="center"
      ></vxe-table-column>
      <vxe-table-column
        field="sessions"
        title="dealt.sessions"
        align="center"
      ></vxe-table-column>
    </vxe-table>
    <vxe-pager
      :loading="loading"
      :current-page="tablePage.currentPage"
      :page-size="tablePage.pageSize"
      :total="tablePage.total"
      :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
      @page-change="handlePageChange"
    >
    </vxe-pager>
    <Drawer
      ref="Drawer"
      @getList="getList"
    />
    <modal
      ref="modal"
      @getList="getList"
    />
  </a-card>
</template>

<script>
import { fetchList, findList, findFrom } from '@/api/process/dealt'
import moment from 'moment'
import Drawer from './drawer'
import modal from './modal'

export default {
  components: {
    Drawer, modal
  },
  data () {
    return {
      tableData: [
      ],
      loading: false,
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      endDd: '',
      staDd: '',
      endOpen: false,
      queryParam: {
      },
      formItemLayout: {
        labelCol: {
          md: { span: 24 },
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          md: { span: 24 },
          xs: { span: 24 },
          sm: { span: 15 }
        }
      },
    }
  },
  created () {
    this.getList()
  },
  watch: {
    staDd (val) {
      if (val) {
        this.queryParam.staDd = moment(val).format('YYYY-MM-DD 00:00:00')
      }
    },
    endDd (val) {
      if (val) {
        this.queryParam.endDd = moment(val).format('YYYY-MM-DD 23:59:59')
      }
    }
  },
  methods: {
    disabledStartDate (startValue) {
      const endValue = this.endDd
      if (!startValue || !endValue) {
        return false
      }
      return startValue.valueOf() > endValue.valueOf()
    },
    disabledEndDate (endValue) {
      const startValue = this.startDd
      if (!endValue || !startValue) {
        return false
      }
      return startValue.valueOf() >= endValue.valueOf()
    },
    handleStartOpenChange (open) {
      if (!open) {
        this.endOpen = true
      }
    },
    handleEndOpenChange (open) {
      this.endOpen = open
    },
    // 查询列表
    getList () {
      this.loading = true
      // eslint-disable-next-line no-undef
      fetchList(
        Object.assign(
          {
            current: this.tablePage.currentPage,
            size: this.tablePage.pageSize
          }, this.queryParam
        )
      )
        .then(res => {
          this.tableData = res.data.records
          this.tablePage.total = res.data.total
          this.tablePage.currentPage = res.data.current
          this.loading = false
        })
        .catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    reset () {
      this.queryParam = {}
      this.endDd = ''
      this.staDd = ''
    },
    // 分页
    handlePageChange ({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getList()
    },
    cellDBLClickEvent ({ row }) {
      let data = []
      findList(
        Object.assign(
          {
            taskId: row.id
          }
        )
      )
        .then(res => {
          data = res.data
          this.$refs.Drawer.edit({ title: this.$t('launch.Detailed') }, data)
        })
        .catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    dropdownMenuEvent (name) {
      switch (name) {
        case 'approval': {
          const selectRecords = this.$refs.xTable.getRadioRecord()
          if (selectRecords) {
            findFrom(
              Object.assign(
                {
                  taskId: selectRecords.id
                }
              )
            )
              .then(res => {
                let id = selectRecords.id
                let sessionsId = selectRecords.sessionsId
                let data = res.data
                this.$refs.modal.create({ title: '审批' }, data, id, sessionsId)
              })
              .catch(err => this.requestFailed(err))
              .finally(() => {
                this.loading = false
              })

          } else {
            this.loading = false
            this.$message.warning(this.$t('dealt.list'))
          }
          break
        }
      }
    }
  }

}
</script>
<style lang="less">
</style>
