<template>
<div class="consrm">
<div  style="text-align:right;margin-right:16px;position: absolute;top: 95px;right:0px;z-index:999">
   
     <a-button @click="handleReset()"  style="margin-left: 8px">{{ $t('public.reset') }}</a-button>
                  <a-button type="primary" @click="exportExcel()" style="margin-left: 8px">{{
                    $t('public.export')
                  }}</a-button>
  </div>
<el-tabs type="border-card">
      <el-tab-pane label="在制(详情)">
  <a-card :bordered="false">
    <a-row :gutter="8">
      <a-col :span="24">
        <div class="table-page-search-wrapper">
          <a-form layout="inline">
            <a-row :gutter="48">
              <a-col :md="6" :sm="24">
                <a-form-item label="筛选条件:">
                  <a-select style="width: 100%" v-model="searchValue">
                    <a-select-option
                      v-for="(item, index) in searchList"
                      :key="index"
                      :label="item.label"
                      :value="item.id"
                      >{{ item.label }}</a-select-option
                    >
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :md="6" :sm="24" v-if="showmoNo">
                <a-form-item label="制令单号:">
                  <a-input v-model="moNo" style="width: 100%" />
                </a-form-item>
              </a-col>
              <a-col :md="6" :sm="24" v-if="showDep">
                <a-form-item label="车间:">
                  <a-select mode="multiple" style="width: 100%" v-model="depts" @change="depChange" ref="depDom">
                    <a-select-option
                      v-for="(item, index) in options"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                      >{{ item.label }}</a-select-option
                    >
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :md="6" :sm="24" v-if="showmrpNo">
                <a-form-item label="成品料号:">
                  <a-input v-model="mrpNo" style="width: 100%" />
                </a-form-item>
              </a-col>
              <a-col :md="6" :sm="24" v-if="showmoDate">
                <a-form-item label="开始:">
                  <a-date-picker v-model="wstaDd" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" style="width: 100%" />
                </a-form-item>
              </a-col>
              <a-col :md="6" :sm="24" v-if="showmoDate">
                <a-form-item label="结束:">
                  <a-date-picker v-model="wendDd" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" style="width: 100%" />
                </a-form-item>
              </a-col>
              <a-col :md="6" :sm="24">
                <a-form-item label="生产执行进度:">
                  <a-date-picker v-model="planDd" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" style="width: 100%" />
                </a-form-item>
              </a-col>
              <a-col :md="6" :sm="24" :class="{ float_button: searchValue === 4 }">
                <a-form-item>
                  <a-button type="primary" style="margin-left: 8px" @click="handleQuery()">{{
                    $t('public.query')
                  }}</a-button>
                  
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-col>
    </a-row>

    <div v-show="showmoNo" class="tablezz_table">
      <vxe-table
        border
        resizable
        stripe
        highlight-current-row
        show-overflow
        highlight-hover-row
        size="mini"
        ref="xTable"
        max-height="600"
        :loading="loading"
        :data="tableData"
        :keyboard-config="{ isArrow: true }"
        :edit-config="{ trigger: 'click', mode: 'row' }"
        :seq-config="{ startIndex: (tablePage.currentPage - 1) * tablePage.pageSize }"
      >
        <vxe-table-column fixed="left" type="seq" title="序号" align="center" :width="50"></vxe-table-column>
        <vxe-table-column field="moNo" title="制令单号" align="center" :width="150"></vxe-table-column>
        <vxe-table-column field="staDd" title="预计开工日" align="center" :width="150"></vxe-table-column>
        <vxe-table-column field="mlDd" title="第一笔领料日" align="center" :width="150"></vxe-table-column>
        <vxe-table-column field="wrDd" title="实际开工日" align="center" :width="150"></vxe-table-column>
        <vxe-table-column field="mrpNo" title="成品料号" align="center" :width="150"></vxe-table-column>
        <vxe-table-column field="pname" title="成品名称" align="center" :width="150"></vxe-table-column>
        <vxe-table-column field="qty" title="生产数量" align="center" :width="150"></vxe-table-column>
        <vxe-table-column field="endDd" title="预计完工日" align="center" :width="150"></vxe-table-column>
        <vxe-table-column title="目前生产进度" align="center" :width="250">
          <vxe-table-column field="zname" title="制程" align="center" :width="100"></vxe-table-column>
          <vxe-table-column field="timeUsed" title="总用工时" align="center" :width="100"></vxe-table-column>
          <vxe-table-column field="avgTime" title="单位平均工时" align="center" :width="100"> </vxe-table-column>
        </vxe-table-column>
        <vxe-table-column field="needTime" title="未完成预计所需" align="center" :width="150"></vxe-table-column>
        <vxe-table-column title="未完成%" align="center" :width="150">
          <template v-slot="{ row }">
            <span>{{ row.wwc }}</span>
          </template>
        </vxe-table-column>
      </vxe-table>
      <vxe-pager
        :loading="loading"
        :current-page="tablePage.currentPage"
        :page-size="tablePage.pageSize"
        :total="tablePage.total"
        :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
        @page-change="handlePageChange"
      >
      </vxe-pager>
    </div>
    <!--  -->
    <div v-show="showDep" class="tablezz_table" :class="{ table2_height: searchValue === 2 && depDomHeight > 32 }">
      <vxe-table
        border
        resizable
        stripe
        highlight-current-row
        show-overflow
        highlight-hover-row
        size="mini"
        ref="xTable"
        max-height="600"
        :loading="loading"
        :data="tableData"
        :keyboard-config="{ isArrow: true }"
        :edit-config="{ trigger: 'click', mode: 'row' }"
        :seq-config="{ startIndex: (tablePage.currentPage - 1) * tablePage.pageSize }"
      >
        <vxe-table-column fixed="left" type="seq" title="序号" align="center" :width="50"></vxe-table-column>
        <vxe-table-column field="dname" title="车间" align="center" :width="150"></vxe-table-column>
        <!-- <vxe-table-column field="moNo" title="制令单号" align="center" :width="150"></vxe-table-column> -->
        <vxe-table-column field="staDd" title="预计开工日" align="center" :width="150"></vxe-table-column>
        <vxe-table-column field="mlDd" title="第一笔领料日" align="center" :width="150"></vxe-table-column>
        <vxe-table-column field="wrDd" title="实际开工日" align="center" :width="150"></vxe-table-column>
        <vxe-table-column field="mrpNo" title="成品料号" align="center" :width="150"></vxe-table-column>
        <vxe-table-column field="pname" title="成品名称" align="center" :width="150"></vxe-table-column>
        <vxe-table-column field="qty" title="生产数量" align="center" :width="150"></vxe-table-column>
        <vxe-table-column field="endDd" title="预计完工日" align="center" :width="150"></vxe-table-column>
        <vxe-table-column title="目前生产进度" align="center" :width="250">
          <vxe-table-column field="zname" title="制程" align="center" :width="100"></vxe-table-column>
          <vxe-table-column field="timeUsed" title="总用工时" align="center" :width="100"></vxe-table-column>
          <vxe-table-column field="avgTime" title="单位平均工时" align="center" :width="100"> </vxe-table-column>
        </vxe-table-column>
        <vxe-table-column field="needTime" title="未完成预计所需" align="center" :width="150"></vxe-table-column>
        <vxe-table-column title="未完成%" align="center" :width="150">
          <template v-slot="{ row }">
            <span>{{ row.wwc }}</span>
          </template>
        </vxe-table-column>
      </vxe-table>
      <vxe-pager
        :loading="loading"
        :current-page="tablePage.currentPage"
        :page-size="tablePage.pageSize"
        :total="tablePage.total"
        :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
        @page-change="handlePageChange"
      >
      </vxe-pager>
    </div>
    <!--  -->
    <div v-show="showmrpNo" class="tablezz_table">
      <vxe-table
        border
        resizable
        stripe
        highlight-current-row
        show-overflow
        highlight-hover-row
        size="mini"
        ref="xTable"
        max-height="600"
        :loading="loading"
        :data="tableData"
        :keyboard-config="{ isArrow: true }"
        :edit-config="{ trigger: 'click', mode: 'row' }"
        :seq-config="{ startIndex: (tablePage.currentPage - 1) * tablePage.pageSize }"
      >
        <vxe-table-column fixed="left" type="seq" title="序号" align="center" :width="50"></vxe-table-column>
        <vxe-table-column field="mrpNo" title="成品料号" align="center" :width="150"></vxe-table-column>
        <vxe-table-column field="pname" title="成品名称" align="center" :width="150"></vxe-table-column>
        <vxe-table-column field="qty" title="生产数量" align="center" :width="150"></vxe-table-column>
        <vxe-table-column field="moNo" title="制令单号" align="center" :width="150"></vxe-table-column>
        <vxe-table-column field="staDd" title="预计开工日" align="center" :width="150"></vxe-table-column>
        <vxe-table-column field="mlDd" title="第一笔领料日" align="center" :width="150"></vxe-table-column>
        <vxe-table-column field="wrDd" title="实际开工日" align="center" :width="150"></vxe-table-column>
        <vxe-table-column field="endDd" title="预计完工日" align="center" :width="150"></vxe-table-column>
        <vxe-table-column title="目前生产进度" align="center" :width="250">
          <vxe-table-column field="zname" title="制程" align="center" :width="100"></vxe-table-column>
          <vxe-table-column field="timeUsed" title="总用工时" align="center" :width="100"></vxe-table-column>
          <vxe-table-column field="avgTime" title="单位平均工时" align="center" :width="100"> </vxe-table-column>
        </vxe-table-column>
        <vxe-table-column field="needTime" title="未完成预计所需" align="center" :width="150"></vxe-table-column>
        <vxe-table-column title="未完成%" align="center" :width="150">
          <template v-slot="{ row }">
            <span>{{ row.wwc }}</span>
          </template>
        </vxe-table-column>
      </vxe-table>
      <vxe-pager
        :loading="loading"
        :current-page="tablePage.currentPage"
        :page-size="tablePage.pageSize"
        :total="tablePage.total"
        :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
        @page-change="handlePageChange"
      >
      </vxe-pager>
    </div>
    <!--  -->
    <div v-show="showmoDate" class="tablezz_table">
      <vxe-table
        border
        resizable
        stripe
        highlight-current-row
        show-overflow
        highlight-hover-row
        size="mini"
        ref="xTable"
        max-height="600"
        :loading="loading"
        :data="tableData"
        :keyboard-config="{ isArrow: true }"
        :edit-config="{ trigger: 'click', mode: 'row' }"
        :seq-config="{ startIndex: (tablePage.currentPage - 1) * tablePage.pageSize }"
      >
        <vxe-table-column fixed="left" type="seq" title="序号" align="center" :width="50"></vxe-table-column>
        <vxe-table-column field="wrDd" title="实际开工日" align="center" :width="150"></vxe-table-column>
        <vxe-table-column field="mrpNo" title="成品料号" align="center" :width="150"></vxe-table-column>
        <vxe-table-column field="pname" title="成品名称" align="center" :width="150"></vxe-table-column>
        <vxe-table-column field="qty" title="生产数量" align="center" :width="150"></vxe-table-column>
        <vxe-table-column field="moNo" title="制令单号" align="center" :width="150"></vxe-table-column>
        <vxe-table-column field="staDd" title="预计开工日" align="center" :width="150"></vxe-table-column>
        <vxe-table-column field="mlDd" title="第一笔领料日" align="center" :width="150"></vxe-table-column>
        <vxe-table-column field="endDd" title="预计完工日" align="center" :width="150"></vxe-table-column>
        <vxe-table-column title="目前生产进度" align="center" :width="250">
          <vxe-table-column field="zname" title="制程" align="center" :width="100"></vxe-table-column>
          <vxe-table-column field="timeUsed" title="总用工时" align="center" :width="100"></vxe-table-column>
          <vxe-table-column field="avgTime" title="单位平均工时" align="center" :width="100"> </vxe-table-column>
        </vxe-table-column>
        <vxe-table-column field="needTime" title="未完成预计所需" align="center" :width="150"></vxe-table-column>
        <vxe-table-column title="未完成%" align="center" :width="150">
          <template v-slot="{ row }">
            <span>{{ row.wwc }}</span>
          </template>
        </vxe-table-column>
      </vxe-table>
      <vxe-pager
        :loading="loading"
        :current-page="tablePage.currentPage"
        :page-size="tablePage.pageSize"
        :total="tablePage.total"
        :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
        @page-change="handlePageChange"
      >
      </vxe-pager>
    </div>
    <Export ref="Export"></Export>
  </a-card>
      </el-tab-pane>
</el-tabs>
</div>
</template>

<script>
import moment from 'moment'
import { getZZ, getAll } from '@/api/formsbarcode/tablezzxq'
import { tablexckzDep } from '@/api/formsbarcode/tablexckz'
import { tableColumn1, tableColumn2, tableColumn3, tableColumn4 } from '@/api/formsbarcode/tablezzExport'
import Export from '@/components/barcodeExport/barcodeExport'
export default {
  components: {
    Export
  },
  data() {
    return {
      depts: [],
      tableData: [],
      types: [null, undefined, ''],
      loading: false,
      showmoNo: true,
      showDep: false,
      showmrpNo: false,
      showmoDate: false,
      moNo: '', // 制令单号
      dep: '', // 车间
      mrpNo: '', // 成品料号
      wstaDd: null,
      wendDd: null,
      planDd: null, // 生产执行进度
      searchValue: 1,
      depDomHeight: undefined,
      searchList: [
        {
          label: '制令单号',
          id: 1
        },
        {
          label: '车间',
          id: 2
        },
        {
          label: '成品料号',
          id: 3
        },
        {
          label: '开工日期',
          id: 4
        }
      ],
      options: [
        // { label: '高周波车间', value: '0016' },
        // { label: '品包车间', value: '0013' },
        // { label: '热压车间', value: '0015' },
        // { label: '贴合组', value: '00140102' }
      ],
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      activeKey: ['1'],
      customStyle: 'background: white;border:0px;'
    }
  },
  watch: {
    searchValue(newVal) {
      // this.planDd = null
      this.tableData = []
      if (newVal === 1) {
        this.showmoNo = true
        this.showDep = false
        this.showmrpNo = false
        this.showmoDate = false
        this.moNo = ''
      }
      if (newVal === 2) {
        this.showmoNo = false
        this.showDep = true
        this.showmrpNo = false
        this.showmoDate = false
        this.depts = []
        this.dep = ''
      }
      if (newVal === 3) {
        this.showmoNo = false
        this.showDep = false
        this.showmrpNo = true
        this.showmoDate = false
        this.mrpNo = ''
      }
      if (newVal === 4) {
        this.showmoNo = false
        this.showDep = false
        this.showmrpNo = false
        this.showmoDate = true
        this.wstaDd = null
        this.wendDd = null
      }
    }
  },
  mounted() {
    this.getDep()
    this.planDd = moment(new Date()).format('YYYY-MM-DD')
  },
  methods: {
    getList() {
      this.loading = true
      getZZ({
        current: this.tablePage.currentPage,
        size: this.tablePage.pageSize,
        wstaDd: this.wstaDd,
        wendDd: this.wendDd,
        planDd: this.planDd,
        moNo: this.moNo,
        mrpNo: this.mrpNo,
        dep: this.getValue().length === 0 ? this.depts.join(',') : this.getValue().join(',')
      })
        .then(res => {
          this.loading = false
          this.tableData = res.data.records
          this.tableData.forEach(i => {
            let wwc = ''
            i.wwc = wwc.concat(i.wwc, '%')
          })
          this.tablePage.total = res.data.total
        })
        .catch(err => {
          this.loading = false
          this.tableData = []
          this.requestFailed(err)
        })
    },
    handlePageChange({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getList()
    },
    getValue() {
      let resValue = []
      if (this.depts === [] || this.depts.length === 0) {
        resValue = this.options.map(i => i.value)
      }
      return resValue
    },
    getDep() {
      tablexckzDep()
        .then(res => {
          this.options = res.data
        })
        .catch(err => this.requestFailed(err))
    },
    depChange() {
      this.$nextTick(() => {
        this.depDomHeight = this.$refs.depDom.$el.offsetHeight
      })
    },
    handleQuery() {
      if (this.types.includes(this.planDd)) return this.$message.warning('请选择生产执行进度！')
      this.tablePage.currentPage = 1
      this.getList()
    },
    handleReset() {
      this.tableData = []
      this.depts = []
      this.searchValue = 1
      this.moNo = ''
      this.mrpNo = ''
      this.dep = ''
      this.wstaDd = null
      this.wendDd = null
      this.planDd = moment(new Date()).format('YYYY-MM-DD')
    },
    exportExcel() {
      if (this.types.includes(this.planDd)) return this.$message.warning('请选择生产执行进度！')
      const hide = this.$message.loading('导出中..', 0)
      getAll({
        wstaDd: this.wstaDd,
        wendDd: this.wendDd,
        planDd: this.planDd,
        moNo: this.moNo,
        mrpNo: this.mrpNo,
        dep: this.getValue().length === 0 ? this.depts.join(',') : this.getValue().join(',')
      })
        .then(res => {
          setTimeout(hide, 10)
          this.$message.success('导出成功')
          const arr = this.getColumn(this.searchValue)
          if (res.data.length != 0) {
            res.data.forEach(i => i.wwc = i.wwc + '%')
          }
          const obj = {
            data: res.data,
            tableColumnZh: arr[0],
            tableColumnEn: arr[1],
            name: '在制(详情)'
          }
          this.$refs.Export.create(obj)
        })
        .catch(err => {
          setTimeout(hide, 10)
          this.$message.error('导出失败')
        })
    },
    getColumn(val) {
      const Zh = []
      const En = []
      if (val === 1) {
        tableColumn1.forEach(i => {
          Zh.push(i.title)
          En.push(i.field)
        })
      }
      if (val === 2) {
        tableColumn2.forEach(i => {
          Zh.push(i.title)
          En.push(i.field)
        })
      }
      if (val === 3) {
        tableColumn3.forEach(i => {
          Zh.push(i.title)
          En.push(i.field)
        })
      }
      if (val === 4) {
        tableColumn4.forEach(i => {
          Zh.push(i.title)
          En.push(i.field)
        })
      }

      return [Zh, En]
    }
  }
}
</script>

<style lang="less" scoped>
.tablezz_table /deep/ .vxe-header--column {
  padding: 0px 0 !important;
}
.float_button {
  float: right;
  text-align: right;
}
.table2_height {
  margin-top: 20px;
}
</style>
