<template>
<div class="consrm">

<el-tabs type="border-card">
      <el-tab-pane label="现场控制">
  <a-card :bordered="false">
    <a-form layout="inline">
      <a-row>
        <a-col :span="24">
          <a-form-item label="当天生产情况:">
            <a-date-picker
              style="width: 100%"
              v-model="now"
              format="YYYY-MM-DD"
              valueFormat="YYYY-MM-DD"
              placeholder="当天生产情况"
            />
          </a-form-item>
          <a-form-item>
            <a-select style="width: 174px" v-model="depNo">
              <a-select-option v-for="item in options" :key="item.id" :label="item.label" :value="item.value">{{
                item.label
              }}</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item>
            <a-button type="primary" style="margin-left: 8px" @click="handleQuery()">{{ $t('public.query') }}</a-button>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <div style="margin-top: 10px">
      <vxe-table
        border
        resizable
        stripe
        highlight-current-row
        show-overflow
        highlight-hover-row
        size="mini"
        ref="xTable"
        height="350"
        :loading="loading"
        :data="tableData"
        :keyboard-config="{ isArrow: true }"
        :edit-config="{ trigger: 'click', mode: 'row' }"
      >
        <vxe-table-column field="moNo" title="制令单号" align="center"></vxe-table-column>
        <vxe-table-column field="indexName" title="产品大类" align="center"></vxe-table-column>
        <vxe-table-column field="pname" title="成品名称" align="center"></vxe-table-column>
        <vxe-table-column field="dayCount" title="日计划产量" align="center"></vxe-table-column>
        <vxe-table-column field="cumulative" title="累计完成量" align="center"></vxe-table-column>
        <vxe-table-column field="qfAndQ" title="已完工/计划数量" align="center"></vxe-table-column>
        <vxe-table-column field="status" title="生产状态" align="center"></vxe-table-column>
      </vxe-table>
      <vxe-pager
        :loading="loading"
        :current-page="tablePage.currentPage"
        :page-size="tablePage.pageSize"
        :total="tablePage.total"
        :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
        @page-change="handlePageChange"
      >
      </vxe-pager>
    </div>

    <div class="boss">
      <el-table :data="tableData1" stripe style="width: 25%; display: inline-block; left: 200px">
        <el-table-column prop="name" label="设备实时情况"></el-table-column>
        <el-table-column prop="count" label></el-table-column>
      </el-table>

      <el-table :data="top10" :row-style="rowStyle" style="width: 35%; float: left; left: 40px">
        <el-table-column prop="name" label align="center"></el-table-column>
        <el-table-column prop="count" label="当月质量缺陷指数"></el-table-column>
        <el-table-column prop="countDay" label="当日质量缺陷指数"></el-table-column>
      </el-table>
    </div>
  </a-card>
      </el-tab-pane>
</el-tabs>
</div>
</template>

<script>
import { getXCKZ, getQueXian, getSebNo, tablexckzDep } from '@/api/formsbarcode/tablexckz'
import moment from 'moment'
export default {
  data() {
    return {
      depNo: '',
      tableData: [],
      tableData1: [],
      top10: [],
      top1: [],
      top2: [],
      now: null,
      length: null,
      loading: false,
      options: [
         { label: '所有', value: '' }
        // { label: '高周波车间', value: '0016' },
        // { label: '品包车间', value: '0013' },
        // { label: '热压车间', value: '0015' },
        // { label: '贴合组', value: '00140102' }
      ],
      tablePage: {
        currentPage: 1,
        pageSize: 5,
        total: 0
      }
    }
  },
  watch: {
    depNo() {
      this.handleQuery()
    }
  },
  created() {
    this.now = moment(new Date()).format('YYYY-MM-DD')
    this.getDep()
  },
  mounted() {
    this.timer = setInterval(a, 3600000)
    function a() {
      let now = new Date().getHours()
      if (now === 0) {
        window.location.reload()
      }
    }
  },
  beforeDestroy() {
    clearInterval(this.timer)
  },
  methods: {
    getDep() {
      this.options = [{ label: '所有', value: '' }]
      tablexckzDep()
      .then(res => {
        this.options.push(...res.data)
        const resValue = res.data.map(i => i.value)
        this.options[0].value = resValue.join(',')
        this.depNo = this.options[0].value
      })
      .catch(err => this.requestFailed(err))
    },
    getList() {
      this.loading = true
      this.tableData = []
      getXCKZ({
        current: this.tablePage.currentPage,
        size: this.tablePage.pageSize,
        now: this.now,
        depNo: this.depNo
      })
        .then(res => {
          this.loading = false
          this.tableData = res.data.records
          this.tablePage.total = res.data.total
        })
        .catch(err => {
          this.loading = false
          this.tableData = []
          this.requestFailed(err)
        })
    },
    getQue() {
      getQueXian({
        depNo: this.depNo,
        now: this.now
      }).then(res => {
        this.top10 = this.formatQueXian(res.data)
      }).catch(err => this.requestFailed(err))
      getSebNo({
        depNo: this.depNo,
        now: this.now
      }).then(res => {
        this.tableData1 = res.data
        // let seb1
        // let seb2
        // let seb3
        // let seb4
        // let seb5
        // sebData1.forEach((item, index) => {
        //   if (item.name == '总机台数量') {
        //     seb1 = item
        //   } else if (item.name == '使用中机台数') {
        //     seb2 = item
        //   } else if (item.name == '报修中机台数') {
        //     seb3 = item
        //   } else if (item.name == '未使用机台数') {
        //     seb4 = item
        //   } else if (item.name == '超一个月未使用') {
        //     seb5 = item
        //   }
        // })
        // this.tableData1 = [seb1, seb2, seb3, seb4, seb5]
      }).catch(err => this.requestFailed(err))
    },
    handleQuery() {
      this.tablePage.currentPage = 1
      this.getList()
      this.getQue()
    },
    handlePageChange({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getList()
    },
    //符号写入
    formatQueXian(data) {
      let fh = '①'
      for (let i = 0; i < data.length; i++) {
        if (i == 1) {
          fh = '②'
        } else if (i == 2) {
          fh = '③'
        } else if (i == 3) {
          fh = '④'
        } else if (i == 4) {
          fh = '⑤'
        } else if (i == 5) {
          fh = '⑥'
        } else if (i == 6) {
          fh = '⑦'
        } else if (i == 7) {
          fh = '⑧'
        } else if (i == 8) {
          fh = '⑨'
        } else if (i == 9) {
          fh = '⑩'
        }
        data[i].name = fh +' '+ data[i].name
      }
      return data
    },
    //top3背景色
    rowStyle({ row, rowIndex }) {
      let styleObj = {}
      if (rowIndex == 0) {
        styleObj.color = '#FF0000'
      } else if (rowIndex == 1) {
        styleObj.color = '#8B0000'
      } else if (rowIndex == 2) {
        styleObj.color = '#FFA07A'
      } else {
        return null
      }
      return styleObj
    }
  }
}
</script>

<style></style>
