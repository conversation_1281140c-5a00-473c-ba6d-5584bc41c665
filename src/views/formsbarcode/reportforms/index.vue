<template>
	<div class="layout">
		<vTable ref="vTable" v-bind="vTableProps" @toolbarClick="handleToolbarClick">
		</vTable>
		<el-dialog :title="$t('reForm.Auth')" destroy-on-close width="40%" :visible.sync="visible" @close="handleCancel" class="JustMake-dialog">
			<span>已选择：</span>
			<el-row :gutter="16">
				<el-col>
					<template v-for="(tag, index) in tags">
						<el-tooltip v-if="tag.codeName.length > 10" :key="tag" :title="tag">
							<el-tag :key="tag" :closable="index !== -1" @close="() => handleClose(tag)">
								{{ `${tag.codeName.slice(0, 10)}...` }}
							</el-tag>
						</el-tooltip>
						<el-tag v-else :closable="index !== -1" @close="() => handleClose(tag)">
							{{ tag.codeName }}
						</el-tag>
					</template>
				</el-col>
			</el-row>
			<template slot="footer">
				<el-button @click="choosetwo">选择角色</el-button>
				<el-button @click="choose">选择人员</el-button>
			</template>
			<!-- 选择人员模态框 -->
			<set-List ref="setList" @getTags="getTags" :newarr="newarr" />
			<role-Chose ref="setListtwo" @getTagstwo="getTagstwo" :newarrtwo="newarrtwo" />
		</el-dialog>
	</div>
</template>
<script>
import vTable from '@/components/amtxts/vTable/vGrid.vue'
import { reportpage, reportprem, reportPremiss } from '@/api/barcode/propertySettings'
import setList from './setList'
import roleChose from './roleChose'
export default {
	name: 'reFormsList',
	components: {
		vTable, setList, roleChose
	},
	data() {
		return {
			row: {},
			newarr: [],
			newarrtwo: [],
			totalCount: -1,
			currentPage: 1,
			multipleSelection: [],
			tableLoading: false,
			tags: [],
			visible: false,
			confirmLoading: false,
			title: '',
			barCode: '',
			list: [],
			listtwo: [],
			reportId: '',
			deltags: [],
			keyword: '',
			formData: [],
			vTableProps: {
				api_find: reportpage,
				api_delete: null,
				toolbarItems: [
					{ label: '分析报表', value: 'title' },
					{ value: 'create', visible: false },
					{ value: 'remove', visible: false },
				],
				formDataRaw: [
					{ field: 'reForm.name', type: 'input', },
				],
				tableColumn: [
					{ field: 'name', title: 'reForm.name', },
					{
						field: 'type', title: 'reForm.name',
						slots: {
							default: ({ row }) => {
								return this.$createElement('el-tag', {
									props: {
										type: 'primary',
									},
								}, this.getLabelForType(row.type));
							}
						},
					},
					{
						field: 'operate', title: 'reForm.operate',
						slots: {
							default: ({ row }) => {
								return this.$createElement('div', [
									this.$createElement('el-button', {
										props: {
											type: 'text',
											size: 'small',
										},
										domProps: {
											innerHTML: this.$t('reForm.view')
										},
										on: {
											click: () => this.handleView(row),
										},
									}),
									this.$createElement('el-button', {
										props: {
											type: 'text',
											size: 'small',
										},
										domProps: {
											innerHTML: this.$t('reForm.Auth')
										},
										on: {
											click: () => this.handleAuth(row),
										},
									})
								]);
							}
						},
					},
				],

			}
		}
	},
	methods: {
		handleToolbarClick(params) {
		},
		handleSubmit() {
			this.$refs.vTable.handleGet();
		},
		handleView(row) {
			let nametype = row.name + '-' + this.getLabelForType(row.type)
			localStorage.setItem('nametype', nametype);
			this.$router.push({
				path: '/formsbarcode/reportforms/preview',
				query: {
					id: row.id,
				}
			})
		},
		handleAuth(row) {
			this.reportId = row.id
			this.visible = true
			this.tags = []
			this.getUsers(row)
		},
		getUsers(row) {
			delete row.dutOtD
			reportprem(row.id).then(res => {
				if (res.data.length > 0) {
					this.tags = res.data
				}
			}).catch(err => this.requestFailed(err))
		},
		getLabelForType(type) {
			const labels = {
				datainfo: this.$t('reForm.typeData'),
				chartinfo: this.$t('reForm.typeChart'),
				printinfo: this.$t('reForm.typePrint'),
			};
			return labels[type] || type;
		},
		getTagstwo(arr, list) {
			this.listtwo = list
			if (arr.length > 0) {
				if (this.tags) {
					let set = new Set([...this.tags, ...list])
					this.tags = [...set]
				} else {
					let set = new Set([...list])
					this.tags = [...set]
				}
			}
		},
		getTags(arr, list) {
			this.list = list
			if (arr.length > 0) {
				if (this.tags) {
					let set = new Set([...this.tags, ...list])
					this.tags = [...set]
				} else {
					let set = new Set([...list])
					this.tags = [...set]
				}
			}
		},
		choose() {
			this.$refs.setList.open(this.row, this.reportId)
		},
		choosetwo() {
			this.$refs.setListtwo.open(this.row, this.reportId)
		},
		handleCancel() {
			this.tags = []
			this.visible = false
		},
		// 保存
		onOk() {
			this.visible = false
			this.row.users = this.tags
		},
		addOk() {
			this.visible = false
		},
		handleClose(removedTag) {
			reportPremiss(removedTag.id)
				.then(res => {
					if (res.code == 0) {
						this.$message.success('删除成功')
					}
				})
				.catch(err => {
					this.loading = false
					this.tableData = []
					this.requestFailed(err)
				})
			const tags = this.tags.filter(tag => tag !== removedTag)
			this.tags = tags
		},
		handleCancel() {
			this.tags = []
			this.visible = false
		},
		// 设置人员
		set_salm(row) {
			this.reportId = row.id
			this.visible = true
			this.tags = []
			this.getUsers(row)
		},
	},
}
</script>
