<template>
  <div>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="10">
            <a-col :md="6" :sm="24">
              <a-form-item label="报表名称:">
                <el-input v-model="keyword" size="mini" style="width:200px;" placeholder="搜索报表名称" clearable>
                </el-input>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-button style="margin-top: 4px" type="primary" size="small" @click="handleQuery">{{ $t('public.query')
                }}</a-button>
            </a-col>
            <!-- <el-button slot="append" icon="el-icon-search" @click="handleQuery" />
              </el-input> -->
          </a-row>
        </a-form>
        <!-- <el-button
      size="mini"
      type="primary"
      style="margin-top:10px;"
          @click="choose"
        >新建报表</el-button> -->
        <el-table :data="tableData" v-loading="tableLoading" style="margin-top:10px;" size="mini"
          :cell-style="{ verticalAlign: 'top' }" height="500"
          :header-cell-style="{ verticalAlign: 'top',backgroundColor: '#F4F5F9', fontWeight: '400' }">

          <!-- <el-table-column
                    type="selection"
                    align="center"
                    width="35">
                  </el-table-column> -->
          <el-table-column prop="name" align="left" label="报表名称"></el-table-column>
          <el-table-column prop="type" align="left" label="报表类型">
            <template slot-scope="scope">
              <div v-if="scope.row.type=='datainfo'">数据报表</div>
              <div v-if="scope.row.type=='chartinfo'">图形报表</div>
              <div v-if="scope.row.type=='printinfo'">打印设计</div>
            </template>
          </el-table-column>
          <el-table-column align="center" label="操作" width="200">
            <template slot-scope="scope">

              <el-button style="padding: 5px 10px 5px 10px;" size="mini" @click="preview(scope.row)">查看
              </el-button>
              <!-- v-if="permissions['jimu_prem']" -->
              <el-button v-if="permissions['jimu_prem']" style="padding: 5px 10px 5px 10px;" size="mini"
                icon="el-icon-edit" @click="set_salm(scope.row)">权限
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div style="display: flex;justify-content: space-between;margin: 2px">
          <el-pagination background :current-page="tablePage.currentPage" :page-sizes="[10,20, 50, 100]" :page-size="20"
            layout="total, sizes, prev, pager, next, jumper" :total="tablePage.total" @size-change="handleSizeChange"
            @current-change="handleCurrentChange" />

          <!-- <el-pagination background :page-size="10" :current-page="currentPage" @current-change="currentChange"
            layout="prev, pager, next" :total="tablePage.total">
          </el-pagination> -->
        </div>
        <a-modal :title="title" destroyOnClose width="40%" :visible.sync="visible" :confirmLoading="confirmLoading"
          @cancel="handleCancel">
          <a-row :gutter="16">
            <a-col>
              <a-form-item label="已选择:">
                <div>
                  <template v-for="(tag, index) in tags">
                    <a-tooltip v-if="tag.length > 10" :key="tag" :title="tag">
                      <a-tag :key="tag" :closable="index !== -1" @close="() => handleClose(tag)">
                        {{ `${tag.codeName.slice(0, 10)}...` }}
                      </a-tag>
                    </a-tooltip>
                    <a-tag v-else :key="tag" :closable="index !== -1" @close="() => handleClose(tag)">
                      {{ tag.codeName}}
                    </a-tag>
                  </template>
                </div>
              </a-form-item>
            </a-col>
          </a-row>
          <template slot="footer">
            <a-button key="choose" @click="choosetwo">选择角色</a-button>
            <a-button key="choose" @click="choose">选择人员</a-button>
            <a-button key="cancel" @click="handleCancel">{{ $t('public.cancel') }} </a-button>
          </template>
          <!-- 选择人员模态框 -->
          <set-List ref="setList" @getTags="getTags" :newarr="newarr" />
          <role-Chose ref="setListtwo" @getTagstwo="getTagstwo" :newarrtwo="newarrtwo" />
        </a-modal>
      </div>
    </a-card>
  </div>
</template>

<script>
  import { reportpage, reportprem, reportPremiss } from '@/api/barcode/propertySettings'
  import setList from './setList'
  import roleChose from './roleChose'
  import Export from '@/components/barcodeExport/barcodeExport'
  import { mapGetters } from 'vuex'
  export default {
    components: {
      Export,
      setList,
      roleChose
    },
    data() {
      return {
        row: {},
        newarr: [],
        totalCount: -1,
        currentPage: 1,
        multipleSelection: [],
        tableLoading: false,
        tags: [],
        visible: false,
        confirmLoading: false,
        title: '',
        barCode: '',
        tableData: [],
        loading: false,
        tablePage: {
          total: 0,
          currentPage: 1,
          pageSize: 20,
          pageSizes: [10, 20, 50, 100],
          layouts: ['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']
        },
        queryParam: {},
        dataPrd: '',
        tableToolbar: {
          refresh: true,
          import: true,
          export: true,
          print: true,
          zoom: true,
          custom: true
        },
        list: [],
        listtwo: [],
        reportId: '',
        deltags: [],
        keyword: ''
      }
    },
    computed: {
      ...mapGetters(['permissions'])
    },
    watch: {
    },
    created() {
      this.getList()
    },
    methods: {
      handleCurrentChange(currentPage) {
        this.tablePage.currentPage = currentPage
        this.getList()
      },
      handleSizeChange(pageSize) {
        this.tablePage.pageSize = pageSize
        this.getList()
      },

      createiframe(src) {
        const tenantId = this.$store.state.user.userInfo.tenantId
        const token = this.$store.state.user.token
        const iframe = document.createElement('iframe')
        iframe.src = `/jimu/jmreport/view/{code}/?tenantId=${tenantId}&token=${token}`
        iframe.id = 'ifr'
        document.getElementById('layout').appendChild(iframe)
      },
      preview(row) {


        let nametype = row.name + '-' + row.type
        localStorage.setItem('nametype', nametype);
        this.$router.push({
          path: '/formsbarcode/reportforms/preview',
          query: {
            id: row.id,
          }
        })
      },
      // 获取人员列表
      getTags(arr, list) {
        this.list = list
        if (arr.length > 0) {
          if (this.tags) {
            let set = new Set([...this.tags, ...list])
            this.tags = [...set]
          } else {
            //  this.tags =  arr
            let set = new Set([...list])
            this.tags = [...set]
          }
        }
      },
      getTagstwo(arr, list) {
        this.listtwo = list
        if (arr.length > 0) {
          if (this.tags) {
            let set = new Set([...this.tags, ...list])
            this.tags = [...set]
          } else {
            let set = new Set([...list])
            this.tags = [...set]
            //  this.tags =  arr
          }
        }
      },
      getUsers(row) {

        // let newObj = JSON.parse(JSON.stringify(row))
        delete row.dutOtD

        reportprem(row.id).then(res => {

          if (res.data.length > 0) {
            // this.deltags = res.data
            // const array = []
            // for (let index = 0; index < res.data.length; index++) {
            //   const vart = res.data[index].codeName
            //   array.push(vart)
            // }
            // this.tags = array
            this.tags = res.data
            // const arr = this.tags
            // debugger
            // console.log(arr, 'nhhhhhhhh')
            // this.newarr = arr.map(i => { return i.split(':')[0] })
          }
        }).catch(err => this.requestFailed(err))
      },
      // 已选择人员按钮
      choose() {
        this.$refs.setList.open(this.row, this.reportId)
      },
      choosetwo() {
        this.$refs.setListtwo.open(this.row, this.reportId)
      },
      handleCancel() {
        this.tags = []
        this.visible = false
      },
      // 保存
      onOk() {
        this.visible = false
        this.row.users = this.tags
      },
      addOk() {
        this.visible = false
      },
      handleClose(removedTag) {
        reportPremiss(removedTag.id)
          .then(res => {
            if (res.code == 0) {
              this.$message.success('删除成功')
            }
          })
          .catch(err => {
            this.loading = false
            this.tableData = []
            this.requestFailed(err)
          })
        const tags = this.tags.filter(tag => tag !== removedTag)
        this.tags = tags
      },
      handleCancel() {
        this.tags = []
        this.visible = false
      },
      // 设置人员
      set_salm(row) {
        this.reportId = row.id
        this.visible = true
        this.tags = []
        this.getUsers(row)
      },
      handleQuery() {
        this.tablePage.currentPage = 1
        this.getList()
      },
      // 查询列表
      getList() {
        this.loading = true
        let permissflag
        if (this.permissions.jimu_prem) {
          permissflag = true
        } else {
          permissflag = false
        }
        reportpage({
          current: this.tablePage.currentPage,
          size: this.tablePage.pageSize,
          name: this.keyword,
          isPermission: permissflag,
          //type:'',
        })
          .then(res => {
            this.loading = false
            this.tableData = res.data.records
            this.tablePage.total = res.data.total
          })
          .catch(err => {
            this.loading = false
            this.tableData = []
            this.requestFailed(err)
          })
      },
      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.tablePage.currentPage = currentPage
        this.tablePage.pageSize = pageSize
        this.getList()
      },

    }
  }
</script>
<style lang="less" scoped>
  .float_button {
    float: right;
    text-align: right;
    margin-bottom: 10px;
    /* // position: relative;
    // position: absolute; */
  }

  .ant-dropdown {
    top: 213px;
  }

  /* // .ant-checkbox-wrapper {
  //   padding: 3px 12px;
  // } */
  .ant-dropdown-menu-item {
    padding: 3px 12px;
  }
</style>