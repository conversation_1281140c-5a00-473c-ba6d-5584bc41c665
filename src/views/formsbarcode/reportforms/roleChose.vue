<template>
  <div>
    <!-- 选择人员模态框 -->
    <el-dialog title="角色选择" :visible.sync="visible" :before-close="handleCancel" width="40%" append-to-body class="JustMake-dialog">
      <el-form :model="queryParam" layout="inline">
        <el-form-item label="角色代号">
          <el-input v-model="queryParam.roleCode" placeholder="请选择角色代号"></el-input>
        </el-form-item>
        <el-button type="primary" @click="getList">{{ $t('public.query') }}</el-button>
        <el-button @click="reset" style="margin-left: 8px">{{ $t('public.reset') }}</el-button>
      </el-form>
      <!-- row-id="salNo" -->
      <vxe-table size="small" border show-overflow highlight-hover-row ref="xTable" class="radio-table"
        @checkbox-change="selectChangeEvent" :checkbox-config="{trigger: 'row', range: true}"
        :radio-config="{labelField: '', trigger: 'row'}" :data="tableData">
        <vxe-table-column type="checkbox" width="50"></vxe-table-column>
        <vxe-table-column field="roleCode" title="角色标识" align="center" width="100"></vxe-table-column>
        <vxe-table-column field="roleName" title="角色名称" align="center"></vxe-table-column>
      </vxe-table>
      <vxe-pager :current-page="tablePage.currentPage" :page-size="tablePage.pageSize" :total="tablePage.total"
        :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']" @page-change="handlePageChange">
      </vxe-pager>
      <template #footer>
        <el-button @click="save">{{ $t('public.sure') }}</el-button>
        <el-button @click="handleCancel">{{ $t('public.cancel') }}</el-button>
      </template>
    </el-dialog>
  </div>
</template>
<script>
  import { getUserList, reportPreadd } from '@/api/barcode/propertySettings'
  import { findList } from '@/api/admin/user'
  import {
    fetchList,
  } from '@/api/admin/role'
  import {
    mapState
  } from 'vuex'
  export default {
    name: 'SetList',
    props: {
      // newarr: {
      //   required: true,
      //   type: Array
      // }
    },
    data() {
      return {
        title: '',
        tableData: [],
        row: {},
        queryParam: {},
        selectRecords: [],
        visible: false,
        visible1: false,
        confirmLoading: false,
        treeData: [],
        currentPage: 1,
        form: this.$form.createForm(this),
        tablePage: {
          currentPage: 1,
          pageSize: 10,
          total: 0
        },
        text: [],
        idlist: ''
      }
    },
    watch: {
      // currentPage: {
      //   handler (val) {
      //     this.arr = this.newarr
      //   },
      //   // 监听到数据变化时立即调用
      //   immediate: true,
      //   deep: true
      // }
    },
    computed: {
      ...mapState({
        propertyList: state => state.approval.propertyList
      })
    },
    created() {
    },
    methods: {
      // 分页触发事件
      click() {
        let arr = this.propertyList.filter(i => {
          return i.page === this.tablePage.currentPage
        })
        let showList = []
        arr.forEach(e => {
          showList.push(this.tableData[e.rowIndex])
        });
        this.$refs.xTable.setCheckboxRow([...showList], true)
        const selectRecords = this.$refs.xTable.getCheckboxRecords()
        this.text.push(...selectRecords)
      },
      open(model, id) {
        this.idlist = id
        this.row = model
        this.getList()
        this.visible = true
      },
      getList(data) {
        this.dep = data
        this.loading = true
        fetchList(
          Object.assign(
            {
              current: this.tablePage.currentPage,
              size: this.tablePage.pageSize
            }, this.queryParam
          )
        )
          .then(res => {
            this.tableData = res.data.records
            this.tablePage.total = res.data.total
            this.tablePage.currentPage = res.data.current
            this.loading = false
          })
          .catch(err => this.requestFailed(err))
          .finally(() => {
            this.loading = false
          })
      },
      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.tablePage.currentPage = currentPage
        this.tablePage.pageSize = pageSize
        this.getList()
      },
      // 重置搜索内容
      reset() {
        this.queryParam = {}
      },
      selectChangeEvent({ checked, records, rowIndex }) {
        // let obj = {
        //   page: this.tablePage.currentPage,
        //   rowIndex: rowIndex
        // }
        // if (checked) {
        //   this.$store.commit("SET_PROPER", obj);
        // } else {
        //   this.$store.commit("DEL_PROPER", obj);
        // }
      },
      save() {
        const list = this.$refs.xTable.getCheckboxRecords()
        list.forEach(item => {
          item.codeId = item.roleId
          item.codeName = item.roleName
          item.reportId = this.idlist
        })
        reportPreadd(
          Object.assign(
            list
          )).then(response => {
            this.$message.success(this.$t('public.success'))
          }).catch(err => this.requestFailed(err))
        var selectRecords = []
        selectRecords = [...list, ...this.text]
        const arr = []
        for (let index = 0; index < selectRecords.length; index++) {
          // const vart =selectRecords[index].roleCode +':' +selectRecords[index].roleName
          const vart = selectRecords[index].roleName
          for (let index1 = 0; index1 < arr.length; index1++) {
            const vart1 = arr[index1]
            if (vart === vart1) {
              arr.splice(index1, 1)
            }
          }
          arr.push(vart)
        }
        this.$emit('getTagstwo', arr, list)
        this.$nextTick(() => {
          selectRecords = []
          this.handleCancel()
        })
      },

      handleCancel() {
        this.visible = false
        // this.text = []
      }
    }
  }
</script>