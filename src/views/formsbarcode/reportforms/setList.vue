<template>
  <div>
    <!-- 选择人员模态框 -->
    <el-dialog title="人员选择" :visible.sync="visible" width="40%" :before-close="handleCancel" append-to-body class="JustMake-dialog">
      <el-form layout="inline" :model="queryParam">
        <el-form-item :label="$t('salm.salNo')">
          <el-input v-model="queryParam.username"></el-input>
        </el-form-item>
        <el-button type="primary" @click="search">{{ $t('public.query') }}</el-button>
        <el-button @click="reset" style="margin-left: 8px">{{ $t('public.reset') }}</el-button>
      </el-form>
      <!-- row-id="salNo" -->
      <vxe-table size="small" border show-overflow highlight-hover-row ref="xTable" class="radio-table"
        @checkbox-change="selectChangeEvent" :checkbox-config="{trigger: 'row', range: true}"
        :radio-config="{labelField: '', trigger: 'row'}" :data="tableData">
        <vxe-table-column type="checkbox" width="50"></vxe-table-column>
        <vxe-table-column field="username" title="账号" align="center" width="100"></vxe-table-column>
        <vxe-table-column field="name" title="用户名" align="center"></vxe-table-column>
      </vxe-table>
      <vxe-pager :current-page="tablePage.currentPage" :page-size="tablePage.pageSize" :total="tablePage.total"
        :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']" @page-change="handlePageChange">
      </vxe-pager>
      <span slot="footer">
        <el-button @click="save">{{ $t('public.sure') }}</el-button>
        <el-button @click="handleCancel">{{ $t('public.cancel') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
  import { getUserList, reportPreadd } from '@/api/barcode/propertySettings'
  import setList from './setList'
  import { findList } from '@/api/admin/user'
  import {
    mapState
  } from 'vuex'
  export default {
    name: 'SetList',
    props: {
      // newarr: {
      //   required: true,
      //   type: Array
      // }
    },
    data() {
      return {
        title: '',
        tableData: [],
        row: {},
        queryParam: {},
        selectRecords: [],
        visible: false,
        visible1: false,
        confirmLoading: false,
        treeData: [],
        currentPage: 1,
        form: this.$form.createForm(this),
        tablePage: {
          currentPage: 1,
          pageSize: 10,
          total: 0
        },
        text: [],
        idlist: ''
      }
    },
    watch: {
      // currentPage: {
      //   handler (val) {
      //     this.arr = this.newarr
      //   },
      //   // 监听到数据变化时立即调用
      //   immediate: true,
      //   deep: true
      // }
    },
    computed: {
      ...mapState({
        propertyList: state => state.approval.propertyList
      })
    },
    created() {

    },

    methods: {
      // 分页触发事件
      click() {
        let arr = this.propertyList.filter(i => {
          return i.page === this.tablePage.currentPage
        })
        let showList = []
        arr.forEach(e => {
          showList.push(this.tableData[e.rowIndex])
        });
        this.$refs.xTable.setCheckboxRow([...showList], true)
        const selectRecords = this.$refs.xTable.getCheckboxRecords()
        this.text.push(...selectRecords)
      },
      open(model, id) {
        this.idlist = id
        this.row = model
        this.getList()
        this.visible = true
      },
      search() {
        this.tablePage.currentPage = 1
        this.getList()
      },
      getList(data) {
        this.dep = data
        this.loading = true
        // getUserList(
        //   Object.assign(
        //     {
        //       roleno: this.row.roleno,
        //       current: this.tablePage.currentPage,
        //       size: this.tablePage.pageSize
        //     }, this.queryParam
        //   )
        // )
        //   .then(res => {
        //     this.tableData = res.data.records
        //     this.tablePage.total = res.data.total
        //     this.tablePage.currentPage = res.data.current
        //     this.loading = false
        //   })
        //   .catch(e => {
        //     this.loading = false
        //   })
        findList(
          Object.assign(
            {
              current: this.tablePage.currentPage,
              size: this.tablePage.pageSize
            }, this.queryParam
          )
        )
          .then(res => {
            this.tableData = res.data.records
            this.tablePage.total = res.data.total
            this.tablePage.currentPage = res.data.current
            this.loading = false
          })
          .catch(err => this.requestFailed(err))
          .finally(() => {
            this.loading = false
          })
      },
      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.tablePage.currentPage = currentPage
        this.tablePage.pageSize = pageSize
        this.getList()
      },
      // 重置搜索内容
      reset() {
        this.queryParam = {}
      },
      selectChangeEvent({ checked, records, rowIndex }) {
        // let obj = {
        //   page: this.tablePage.currentPage,
        //   rowIndex: rowIndex
        // }
        // if (checked) {
        //   this.$store.commit("SET_PROPER", obj);
        // } else {
        //   this.$store.commit("DEL_PROPER", obj);
        // }
      },
      save() {
        const list = this.$refs.xTable.getCheckboxRecords()
        list.forEach(item => {
          item.codeId = item.userId
          item.codeName = item.username
          item.reportId = this.idlist
        })
        reportPreadd(
          Object.assign(
            list
          )).then(response => {
            this.$message.success(this.$t('public.success'))
          }).catch(err => this.requestFailed(err))
        var selectRecords = []
        selectRecords = [...list, ...this.text]
        const arr = []
        for (let index = 0; index < selectRecords.length; index++) {
          // const vart =selectRecords[index].userId + ':' + selectRecords[index].username
          const vart = selectRecords[index].name
          for (let index1 = 0; index1 < arr.length; index1++) {
            const vart1 = arr[index1]
            if (vart === vart1) {
              arr.splice(index1, 1)
            }
          }
          arr.push(vart)
        }
        this.$emit('getTags', arr, list)
        this.$nextTick(() => {
          selectRecords = []
          this.handleCancel()
        })
      },

      handleCancel() {
        this.visible = false
        // this.text = []
      }
    }
  }
</script>