<template>
  <a-card :bordered="false">
    <iframe :src="src" class="iframe" />
  </a-card>
</template>
<script>
  export default {
    data() {
      return {
        src: ''
      };
    },
    mounted() {
      const tenantId = this.$store.state.user.userInfo.tenantId
      const token = this.$store.state.user.token
      const id = this.$route.query.id
      this.src = `/jimu/jmreport/view/` + id + `?tenantId=${tenantId}&token=${token}`
      // this.src = `/jimu/jmreport/list?tenantId=${tenantId}&token=${token}`;
      //this.src = `/jimu/jmreport/view/575163965000249344?tenantId=36&token=735cedc1-66be-4190-9f90-5ed64b9a17aa`;
    },
  }
</script>
<style lang="less" scoped>
  .iframe {
    width: 100%;
    height: 80vh;
    border: 0;
    overflow: hidden;
    box-sizing: border-box;
  }
</style>