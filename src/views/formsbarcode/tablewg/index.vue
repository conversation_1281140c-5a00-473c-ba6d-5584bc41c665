<template>
<div class="consrm">
<div  style="text-align:right;margin-right:16px;position: absolute;top: 95px;right:0px;z-index:999">
   
      <a-button @click="handleReset()" style="margin-left: 8px">{{ $t('public.reset') }}</a-button>
              <a-button type="primary" @click="exportExcel()" style="margin-left: 8px">{{
                $t('public.export')
              }}</a-button>
  </div>
<el-tabs type="border-card">
      <el-tab-pane label="完工">
  <a-card :bordered="false">
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="48">
          <a-col :md="6" :sm="24">
            <a-form-item label="筛选条件:">
              <a-select style="width: 100%" v-model="searchValue">
                <a-select-option
                  v-for="(item, index) in searchList"
                  :key="index"
                  :label="item.label"
                  :value="item.id"
                  >{{ item.label }}</a-select-option
                >
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24">
            <a-form-item label="完工状态:">
              <a-select style="width: 100%" v-model="closeId">
                <a-select-option
                  v-for="(item, index) in statusList"
                  :key="index"
                  :label="item.label"
                  :value="item.closeId"
                  >{{ item.label }}</a-select-option
                >
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24">
            <a-form-item label="开始时间:">
              <a-date-picker v-model="staDd" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24">
            <a-form-item label="结束时间:">
              <a-date-picker v-model="endDd" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" style="width: 100%" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="48">
          <a-col :md="6" :sm="24" v-if="showmoNo">
            <a-form-item label="制令单号:">
              <a-input v-model="moNo" />
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24" v-if="showmrpNo">
            <a-form-item label="成品料号:">
              <a-input v-model="mrpNo" />
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24" v-if="showDep">
            <a-form-item label="车间:">
              <a-select mode="multiple" v-model="depts" @change="depChange" ref="depDom">
                <a-select-option
                  v-for="(item, index) in options"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                  >{{ item.label }}</a-select-option
                >
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24"></a-col>
          <a-col :md="6" :sm="24"></a-col>
          <a-col :md="6" :sm="24" style="float:left;text-align: right;">
            <a-form-item label="">
              <a-button type="primary" @click="handleQuery()">{{ $t('public.query') }}</a-button>
             
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <div :class="{'table_height' : searchValue === 3 && depDomHeight > 32}">
      <vxe-grid
        border
        resizable
        show-overflow
        keep-source
        size="mini"
        max-height="600"
        :loading="loading"
        :data="tableData"
        :columns="tableOption"
        :pager-config="tablePage"
        :seq-config="{ startIndex: (tablePage.currentPage - 1) * tablePage.pageSize }"
        @page-change="handlePageChange"
      ></vxe-grid>
    </div>
    <Export ref="Export"></Export>
  </a-card>
      </el-tab-pane>
</el-tabs>
</div>
</template>

<script>
import { getWG, getAll } from '@/api/formsbarcode/tablewg'
import { tablexckzDep } from '@/api/formsbarcode/tablexckz'
import Export from '@/components/barcodeExport/barcodeExport'
export default {
  components: {
    Export
  },
  data() {
    return {
      depts: [],
      names: [],
      tableData: [],
      depDomHeight: undefined,
      types: [null, undefined, ''],
      loading: false,
      showmoNo: true,
      showmrpNo: false,
      showDep: false,

      closeId: '',
      searchValue: 1,
      moNo: '',
      staDd: null,
      endDd: null,
      mrpNo: '',
      dep: '',
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
        pageSizes: [10, 15, 20, 50],
        layouts: ['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']
      },
      tableOption: [],
      tableColumn: [
        {
          type: 'seq',
          title: '序号',
          field: 'seq',
          width: 50,
          align: 'center',
          showOverflow: false
        },
        {
          title: '制令单号',
          field: 'moNo',
          width: 150,
          align: 'center'
        },
        {
          title: '制令单日期',
          field: 'moDd',
          width: 150,
          align: 'center'
        },
        {
          title: '成品料号',
          field: 'mrpNo',
          width: 150,
          align: 'center'
        },
        {
          title: '成品名称',
          field: 'name',
          width: 150,
          align: 'center'
        },
        {
          title: '应生产量',
          field: 'qty',
          width: 150,
          align: 'center'
        },
        {
          title: '实际生产数量',
          field: 'qtyFin',
          width: 150,
          align: 'center'
        },
        {
          title: '未完工量',
          field: 'qtyLast',
          width: 150,
          align: 'center'
        },
        {
          title: '预计出货日',
          field: 'endDd',
          width: 150,
          align: 'center'
        },
        {
          title: '材料损耗金额',
          field: 'loss',
          width: 150,
          align: 'center'
        },
        {
          title: '损耗%',
          field: 'rate',
          width: 150,
          align: 'center'
        },
        {
          title: '实际报工工时',
          field: 'allWorkingHours',
          width: 150,
          align: 'center'
        },
        {
          title: '补工工时',
          field: 'forHours',
          width: 150,
          align: 'center'
        },
        {
          title: '总用工时',
          field: 'tiamworking',
          width: 150,
          align: 'center'
        },
        {
          title: '未完工量剩余最多工序',
          field: 'unfinished',
          width: 150,
          align: 'center'
        },
        {
          title: '标准工时',
          field: 'workingHours',
          width: 150,
          align: 'center'
        },
        {
          title: '差异工时',
          field: 'differences',
          width: 150,
          align: 'center'
        },
        {
          title: '单位工时',
          field: 'unitTime',
          width: 150,
          align: 'center'
        },
        {
          title: '生产效率(含补工时)',
          field: 'efficiency',
          width: 200,
          align: 'center'
        },
        {
          title: '净生产效率(不含补工时)',
          field: 'net',
          width: 200,
          align: 'center'
        }
      ],
      tableColumn2: [
        {
          type: 'seq',
          title: '序号',
          field: 'seq',
          width: 50,
          align: 'center',
          showOverflow: false
        },
        {
          title: '成品料号',
          field: 'mrpNo',
          width: 150,
          align: 'center'
        },
        {
          title: '成品名称',
          field: 'name',
          width: 150,
          align: 'center'
        },
        {
          title: '制令单号',
          field: 'moNo',
          width: 150
        },
        {
          title: '制令单日期',
          field: 'moDd',
          width: 150
        },

        {
          title: '应生产量',
          field: 'qty',
          width: 150
        },
        {
          title: '实际生产数量',
          field: 'qtyFin',
          width: 150,
          align: 'center'
        },
        {
          title: '未完工量',
          field: 'qtyLast',
          width: 150,
          align: 'center'
        },
        {
          title: '预计出货日',
          field: 'endDd',
          width: 150,
          align: 'center'
        },
        {
          title: '材料损耗金额',
          field: 'loss',
          width: 150,
          align: 'center'
        },
        {
          title: '损耗%',
          field: 'rate',
          width: 150,
          align: 'center'
        },
        {
          title: '实际报工工时',
          field: 'allWorkingHours',
          width: 150,
          align: 'center'
        },
        {
          title: '补工工时',
          field: 'forHours',
          width: 150,
          align: 'center'
        },
        {
          title: '总用工时',
          field: 'tiamworking',
          width: 150,
          align: 'center'
        },
        {
          title: '未完工量剩余最多工序',
          field: 'unfinished',
          width: 150,
          align: 'center'
        },
        {
          title: '标准工时',
          field: 'workingHours',
          width: 150,
          align: 'center'
        },
        {
          title: '差异工时',
          field: 'differences',
          width: 150,
          align: 'center'
        },
        {
          title: '单位工时',
          field: 'unitTime',
          width: 150,
          align: 'center'
        },
        {
          title: '生产效率(含补工时)',
          field: 'efficiency',
          width: 200,
          align: 'center'
        },
        {
          title: '净生产效率(不含补工时)',
          field: 'net',
          width: 200,
          align: 'center'
        }
      ],
      tableColumn3: [
        {
          type: 'seq',
          title: '序号',
          field: 'seq',
          width: 50,
          align: 'center',
          showOverflow: false
        },
        {
          title: '车间',
          field: 'dep',
          width: 150,
          align: 'center'
        },
        {
          title: '制令单号',
          field: 'moNo',
          width: 150,
          align: 'center'
        },
        {
          title: '制令单日期',
          field: 'moDd',
          width: 150,
          align: 'center'
        },
        {
          title: '成品料号',
          field: 'mrpNo',
          width: 150,
          align: 'center'
        },
        {
          title: '成品名称',
          field: 'name',
          width: 150,
          align: 'center'
        },
        {
          title: '应生产量',
          field: 'qty',
          width: 150,
          align: 'center'
        },
        {
          title: '实际生产数量',
          field: 'qtyFin',
          width: 150,
          align: 'center'
        },
        {
          title: '未完工量',
          field: 'qtyLast',
          width: 150,
          align: 'center'
        },
        {
          title: '预计出货日',
          field: 'endDd',
          width: 150,
          align: 'center'
        },
        {
          title: '材料损耗金额',
          field: 'loss',
          width: 150,
          align: 'center'
        },
        {
          title: '损耗%',
          field: 'rate',
          width: 150,
          align: 'center'
        },
        {
          title: '实际报工工时',
          field: 'allWorkingHours',
          width: 150,
          align: 'center'
        },
        {
          title: '补工工时',
          field: 'forHours',
          width: 150,
          align: 'center'
        },
        {
          title: '总用工时',
          field: 'tiamworking',
          width: 150,
          align: 'center'
        },
        {
          title: '未完工量剩余最多工序',
          field: 'unfinished',
          width: 150,
          align: 'center'
        },
        {
          title: '标准工时',
          field: 'workingHours',
          width: 150,
          align: 'center'
        },
        {
          title: '差异工时',
          field: 'differences',
          width: 150,
          align: 'center'
        },
        {
          title: '单位工时',
          field: 'unitTime',
          width: 150,
          align: 'center'
        },
        {
          title: '生产效率(含补工时)',
          field: 'efficiency',
          width: 200,
          align: 'center'
        },
        {
          title: '净生产效率(不含补工时)',
          field: 'net',
          width: 200,
          align: 'center'
        }
      ],
      statusList: [
        {
          label: '所有',
          closeId: ''
        },
        {
          label: '未完工',
          closeId: 'F'
        },
        {
          label: '已完工',
          closeId: 'T'
        }
      ],
      searchList: [
        {
          label: '制令单号',
          id: 1
        },
        {
          label: '成品料号',
          id: 2
        },
        {
          label: '车间',
          id: 3
        }
      ],
      options: [
        // {
        //   label: '高周波车间',
        //   value: '0016'
        // },
        // {
        //   label: '品包车间',
        //   value: '0013'
        // },
        // {
        //   label: '热压车间',
        //   value: '0015'
        // },
        // {
        //   label: '贴合组',
        //   value: '00140102'
        // }
      ],
      activeKey: ['1'],
      customStyle: 'background: white;border:0px;'
    }
  },
  watch: {
    searchValue(newValue) {
      this.tableData = []
      if (newValue === 1) {
        this.tableOption = this.tableColumn
        this.showmoNo = true
        this.showmrpNo = false
        this.showDep = false
        this.moNo = ''
      }
      if (newValue === 2) {
        this.tableOption = this.tableColumn2
        this.showmoNo = false
        this.showmrpNo = true
        this.showDep = false
        this.mrpNo = ''
      }
      if (newValue === 3) {
        this.tableOption = this.tableColumn3
        this.showmoNo = false
        this.showmrpNo = false
        this.showDep = true
        this.depts = []
        this.dep = ''
      }
    }
  },
  created() {
    this.tableOption = this.tableColumn
  },
  mounted() {
    this.getTime()
    this.getDep()
  },
  methods: {
    getList() {
      this.loading = true
      getWG({
        current: this.tablePage.currentPage,
        size: this.tablePage.pageSize,
        closeId: this.closeId,
        moNo: this.moNo,
        staDd: this.staDd,
        endDd: this.endDd,
        mrpNo: this.mrpNo,
        dep: this.getValue().length === 0 ? this.depts.join(',') : this.getValue().join(',')
      })
        .then(res => {
          this.loading = false
          this.tableData = res.data.records
          this.tablePage.total = res.data.total
          this.tableData.forEach(i => {
            if (i.rate != null) {
              i.rate += '%'
            }
            if (i.efficiency != null) {
              i.efficiency += '%'
            }
            if (i.net != null) {
              i.net += '%'
            }
          })
        })
        .catch(err => {
          this.loading = false
          this.tableData = []
          this.requestFailed(err)
        })
    },
    handlePageChange({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getList()
    },
    handleQuery() {
      if (!this.isDate()) return
      this.tablePage.currentPage = 1
      this.getList()
    },
    getValue() {
      let resValue = []
      if (this.depts === [] || this.depts.length === 0) {
        resValue = this.options.map(i => i.value)
      }
      return resValue
    },
    getDep() {
      tablexckzDep()
        .then(res => {
          this.options = res.data
        })
        .catch(err => this.requestFailed(err))
    },
    depChange() {
      this.$nextTick(() => {
        this.depDomHeight = this.$refs.depDom.$el.offsetHeight
      })
    },
    isDate() {
      if (this.types.includes(this.staDd) || this.types.includes(this.endDd)) {
        this.$message.warning('请选择开始日期或结束日期！')
        return false
      }
      if (moment(this.endDd).diff(moment(this.staDd), 'days') < 0) {
        this.$message.warning('结束日期不能小于开始日期！')
        return false
      }
      if (moment(this.endDd).diff(moment(this.staDd), 'days') >= this.$setDays) {
        this.$message.warning(`开始日期和结束日期不能大于${this.$setDays}天！`)
        return false
      }
      return true
    },
    getTime() {
      this.staDd = moment()
        .startOf('month')
        .format('YYYY-MM-DD')
      this.endDd = moment()
        .endOf('month')
        .format('YYYY-MM-DD')
    },
    handleReset() {
      this.getTime()
      this.tableData = []
      this.depts = []
      this.searchValue = 1
      this.closeId = ''
      this.moNo = ''
      this.mrpNo = ''
      this.dep = ''
    },
    exportExcel() {
      if (!this.isDate()) return
      const hide = this.$message.loading('导出中..', 0)
      getAll({
        closeId: this.closeId,
        moNo: this.moNo,
        staDd: this.staDd,
        endDd: this.endDd,
        mrpNo: this.mrpNo,
        dep: this.getValue().length === 0 ? this.depts.join(',') : this.getValue().join(',')
      })
        .then(res => {
          setTimeout(hide, 10)
          this.$message.success('导出成功')
          const arr = this.getColumn(this.searchValue)
          res.data.forEach(i => {
            if (i.rate != null) {
              i.rate += '%'
            }
            if (i.efficiency != null) {
              i.efficiency += '%'
            }
            if (i.net != null) {
              i.net += '%'
            }
          })
          const obj = {
            data: res.data,
            tableColumnZh: arr[0],
            tableColumnEn: arr[1],
            name: '完工'
          }
          this.$refs.Export.create(obj)
        })
        .catch(err => {
          setTimeout(hide, 10)
          this.$message.error('导出失败')
        })
    },
    getColumn(val) {
      const Zh = []
      const En = []
      if (val === 1) {
        this.tableColumn.forEach(i => {
          if (i.showOverflow !== false) {
            Zh.push(i.title)
            En.push(i.field)
          }
        })
      }
      if (val === 2) {
        this.tableColumn2.forEach(i => {
          if (i.showOverflow !== false) {
            Zh.push(i.title)
            En.push(i.field)
          }
        })
      }
      if (val === 3) {
        this.tableColumn3.forEach(i => {
          if (i.showOverflow !== false) {
            Zh.push(i.title)
            En.push(i.field)
          }
        })
      }

      return [Zh, En]
    }
  }
}
</script>

<style lang="less" scoped>
.table_height {
  margin-top: 20px;
}
</style>
