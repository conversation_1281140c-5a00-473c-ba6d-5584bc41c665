<template>
<div class="consrm">
<div  style="text-align:right;margin-right:16px;position: absolute;top: 95px;right:0px;z-index:999">
   
      <a-button @click="handleReset()" style="margin-left: 8px">{{ $t('public.reset') }}</a-button>
              <a-button type="primary" @click="exportExcel()" style="margin-left: 8px">{{
                $t('public.export')
              }}</a-button>
  </div>
<el-tabs type="border-card">
      <el-tab-pane label="人员报工状况明细表">
  <a-card :bordered="false">
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="48">
          <a-col :md="6" :sm="24">
            <a-form-item label="开始时间:">
              <a-date-picker
                style="width: 100%"
                v-model="queryParam.staWrDd"
                format="YYYY-MM-DD"
                valueFormat="YYYY-MM-DD"
              />
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24">
            <a-form-item label="结束时间:">
              <a-date-picker
                v-model="queryParam.endWrDd"
                format="YYYY-MM-DD"
                valueFormat="YYYY-MM-DD"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24">
            <a-form-item label="入职日期:">
              <a-date-picker
                v-model="queryParam.staDd"
                format="YYYY-MM-DD"
                valueFormat="YYYY-MM-DD"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24">
            <a-form-item label="离职日期:">
              <a-date-picker
                v-model="queryParam.endDd"
                format="YYYY-MM-DD"
                valueFormat="YYYY-MM-DD"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="48">
          <a-col :md="6" :sm="24">
            <a-form-item label="员工工号:">
              <a-input v-model="queryParam.salNo" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24">
            <a-form-item label="员工姓名:">
              <a-input v-model="queryParam.salName" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24">
            <a-form-item label="部门代号:">
              <a-select mode="multiple" style="width: 100%" v-model="depts">
                <a-select-option
                  v-for="(item, index) in dataList"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                  >{{ item.label }}</a-select-option
                >
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24">
            <a-form-item label="制单部门:">
              <a-select mode="multiple" style="width: 100%" v-model="names">
                <a-select-option
                  v-for="(item, index) in dataList"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                  >{{ item.label }}</a-select-option
                >
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24" style="float:left;text-align: right;">
            <a-form-item label="">
              <a-button type="primary" @click="handleQuery()">{{ $t('public.query') }}</a-button>
             
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <div>
      <vxe-table
        border
        resizable
        stripe
        highlight-current-row
        show-overflow
        highlight-hover-row
        size="mini"
        ref="xTable"
        max-height="580"
        :loading="loading"
        :data="tableData"
        :keyboard-config="{ isArrow: true }"
        :edit-config="{ trigger: 'click', mode: 'row' }"
        :seq-config="{ startIndex: (tablePage.currentPage - 1) * tablePage.pageSize }"
      >
        <vxe-table-column fixed="left" type="seq" title="序号" align="center" :width="50"></vxe-table-column>
        <vxe-table-column field="wrDd" title="报工单单据日期" align="center" :width="150"></vxe-table-column>
        <vxe-table-column field="salNo" title="员工工号" align="center" :width="150"></vxe-table-column>
        <vxe-table-column field="salName" title="员工姓名" align="center" :width="150"></vxe-table-column>
        <vxe-table-column field="dep" title="部门代号" align="center" :width="150"></vxe-table-column>
        <vxe-table-column field="depName" title="部门名称" align="center" :width="150"></vxe-table-column>
        <vxe-table-column field="usedTime" title="报工工时" align="center" :width="150"></vxe-table-column>
        <vxe-table-column field="myUse" title="补工工时" align="center" :width="150"></vxe-table-column>
        <vxe-table-column field="sqNo" title="补工工时单号" align="center" :width="150"></vxe-table-column>
        <vxe-table-column field="staTime" title="报工开始时间" align="center" :width="150"></vxe-table-column>
        <vxe-table-column field="endTime" title="报工结束时间" align="center" :width="150"></vxe-table-column>
        <vxe-table-column field="wrNo" title="报工单号" align="center" :width="150"></vxe-table-column>
        <vxe-table-column field="wname" title="报工单制单人名称" align="center" :width="150"></vxe-table-column>
        <vxe-table-column field="wdepName" title="制单部门" align="center" :width="150"></vxe-table-column>
        <vxe-table-column field="moNo" title="制令单号" align="center" :width="150"></vxe-table-column>
      </vxe-table>
      <vxe-pager
        :loading="loading"
        :current-page="tablePage.currentPage"
        :page-size="tablePage.pageSize"
        :total="tablePage.total"
        :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
        @page-change="handlePageChange"
      >
      </vxe-pager>
    </div>
    <Export ref="Export"></Export>
  </a-card>
      </el-tab-pane>
</el-tabs>
</div>
</template>

<script>
import { tablexckzDep } from '@/api/formsbarcode/tablexckz'
import { fetchList, fetchListAll } from '@/api/formsbarcode/workReport'
import Export from '@/components/barcodeExport/barcodeExport'
export default {
  components: {
    Export
  },
  data() {
    return {
      depts: [],
      names: [],
      tableData: [],
      types: [null, undefined, ''],
      loading: false,
      queryParam: {
        staWrDd: '',
        endWrDd: '',
        staDd: '',
        endDd: '',
        salNo: '',
        salName: '',
        dep: '',
        wdepName: ''
      },
      tablePage: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      },
      dataList: [
        // {
        //   label: '高周波',
        //   value: '0016'
        // },
        // {
        //   label: '热压',
        //   value: '0015'
        // },
        // {
        //   label: '一车间',
        //   value: '0014'
        // },
        // {
        //   label: '品包',
        //   value: '0013'
        // }
      ],
      activeKey: ['1'],
      customStyle: 'background: white;border:0px;'
    }
  },
  mounted() {
    this.getTime()
    this.getDep()
  },
  methods: {
    getList() {
      this.loading = true
      this.queryParam.dep = this.getDepValue().length === 0 ? this.depts.join(',') : this.getDepValue().join(',')
      this.queryParam.wdepName = this.getNameValue().length === 0 ? this.names.join(',') : this.getNameValue().join(',')
      fetchList(
        Object.assign(
          {
            current: this.tablePage.currentPage,
            size: this.tablePage.pageSize
          },
          this.queryParam
        )
      )
        .then(res => {
          this.loading = false
          this.tableData = res.data.records
          this.tablePage.total = res.data.total
        })
        .catch(err => {
          this.loading = false
          this.tableData = []
          this.requestFailed(err)
        })
    },
    handlePageChange({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getList()
    },
    handleQuery() {
      if (!this.isDate()) return
      this.tablePage.currentPage = 1
      this.getList()
    },
    getDep() {
      tablexckzDep()
        .then(res => {
          this.dataList = res.data
        })
        .catch(err => this.requestFailed(err))
    },
    getDepValue() {
      let depValue = []
      if (this.depts === [] || this.depts.length === 0) {
        depValue = this.dataList.map(i => i.value)
      }
      return depValue
    },
    getNameValue() {
      let namesValue = []
      if (this.names === [] || this.names.length === 0) {
        namesValue = this.dataList.map(i => i.value)
      }
      return namesValue
    },
    getTime () {
      this.queryParam.staWrDd = moment().startOf('month').format("YYYY-MM-DD")
      this.queryParam.endWrDd = moment().endOf('month').format("YYYY-MM-DD")
    },
    isDate() {
      if (this.types.includes(this.queryParam.staWrDd) || this.types.includes(this.queryParam.endWrDd)) {
        this.$message.warning('请选择开始日期或结束日期！')
        return false
      }
      if (moment(this.queryParam.endWrDd).diff(moment(this.queryParam.staWrDd), 'days') < 0) {
        this.$message.warning('结束日期不能小于开始日期！')
        return false
      }
      if (moment(this.queryParam.endWrDd).diff(moment(this.queryParam.staWrDd), 'days') >= this.$setDays) {
        this.$message.warning(`开始日期和结束日期不能大于${this.$setDays}天！`)
        return false
      }
      return true
    },
    handleReset() {
      this.tableData = []
      this.depts = []
      this.names = []
      this.getTime()
      // this.queryParam.staWrDd = ''
      // this.queryParam.endWrDd = ''
      this.queryParam.staDd = ''
      this.queryParam.endDd = ''
      this.queryParam.salNo = ''
      this.queryParam.salName = ''
      this.queryParam.dep = ''
      this.queryParam.wdepName = ''
    },
    exportExcel() {
      if (!this.isDate()) return
      const hide = this.$message.loading('导出中..', 0)
      this.queryParam.dep = this.getDepValue().length === 0 ? this.depts.join(',') : this.getDepValue().join(',')
      this.queryParam.wdepName = this.getNameValue().length === 0 ? this.names.join(',') : this.getNameValue().join(',')
      fetchListAll(this.queryParam)
        .then(res => {
          setTimeout(hide, 10)
          this.$message.success('导出成功')
          const arr = this.getColumn()
          const obj = {
            data: res.data,
            tableColumnZh: arr[0].filter(i => i !== '序号'),
            tableColumnEn: arr[1].filter(i => i !== undefined),
            name: '人员报工状况明细表'
          }
          this.$refs.Export.create(obj)
        })
        .catch(err => {
          setTimeout(hide, 10)
          this.$message.error('导出失败')
        })
    },
    getColumn() {
      const { collectColumn } = this.$refs.xTable.getTableColumn()
      return [
        collectColumn.map(i => i.title),
        collectColumn.map(i => i.property)
      ]
    }
  }
}
</script>

<style></style>
