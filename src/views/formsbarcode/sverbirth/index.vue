<template>
<div class="consrm">
<div  style="text-align:right;margin-right:16px;position: absolute;top: 95px;right:0px;z-index:999">
     <a-button style="margin-left: 8px" @click="reset">{{ $t('public.reset') }}</a-button>
             
  </div>
<el-tabs type="border-card">
      <el-tab-pane label="超产统计">
  <a-card :bordered="false">
    <div class="table-page-search-wrapper">
      <!-- <a-collapse :bordered="false" v-model="activeKey"> -->
        <!-- <a-collapse-panel key="1" :style="customStyle"> -->
          <a-form layout="inline" labelAlign="left">
            <a-row :gutter="48">
              <a-col :md="6" :sm="24">
                <a-form-item label="单据日期开始">
                  <a-date-picker
                    style="width: 100%"
                    v-model="queryParams.sysStart"
                    format="YYYY-MM-DD"
                    valueFormat="YYYY-MM-DD"
                  />
                </a-form-item>
              </a-col>
              <a-col :md="6" :sm="24">
                <a-form-item label="单据日期结束">
                  <a-date-picker
                    style="width: 100%"
                    v-model="queryParams.sysEnd"
                    format="YYYY-MM-DD"
                    valueFormat="YYYY-MM-DD"
                  />
                </a-form-item>
              </a-col>
              <a-col :md="6" :sm="24">
                <a-form-item label="报工开始日期">
                  <a-date-picker
                    style="width: 100%"
                    v-model="queryParams.bgStart"
                    format="YYYY-MM-DD"
                    valueFormat="YYYY-MM-DD"
                  />
                </a-form-item>
              </a-col>
              <a-col :md="6" :sm="24">
                <a-form-item label="报工结束日期">
                  <a-date-picker
                    style="width: 100%"
                    v-model="queryParams.bgEnd"
                    format="YYYY-MM-DD"
                    valueFormat="YYYY-MM-DD"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="48">
              <a-col :md="6" :sm="24">
                <a-form-item label="员工代号">
                  <a-select
                    show-search
                    allowClear
                    @search="fetchUser"
                    @focus="ygNoData"
                    :filter-option="false"
                    :notFoundContent="null"
                    v-model="queryParams.ygNo"
                  >
                    <a-select-option
                      v-for="(item, index) in ygNoList"
                      :key="index"
                      :label="item"
                      :value="item"
                      >{{ item }}</a-select-option
                    >
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :md="6" :sm="24">
                <a-form-item label="员工名称">
                  <a-select
                    show-search
                    allowClear
                    @search="fetchYgDepName"
                    @focus="ygNameData"
                    :filter-option="false"
                    :notFoundContent="null"
                    v-model="queryParams.ygName"
                  >
                    <a-select-option
                      v-for="(item, index) in ygNameList"
                      :key="index"
                      :label="item"
                      :value="item"
                      >{{ item }}</a-select-option
                    >
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :md="6" :sm="24">
                <a-form-item label="干部代号">
                  <a-select
                    show-search
                    allowClear
                    @search="fetchUser"
                    @focus="ygNoData"
                    :filter-option="false"
                    :notFoundContent="null"
                    v-model="queryParams.upYgNo"
                  >
                    <a-select-option
                      v-for="(item, index) in ygNoList"
                      :key="index"
                      :label="item"
                      :value="item"
                      >{{ item }}</a-select-option
                    >
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :md="6" :sm="24">
                <a-form-item label="干部名称">
                  <a-select
                    show-search
                    allowClear
                    @search="fetchYgDepName"
                    @focus="ygNameData"
                    :filter-option="false"
                    :notFoundContent="null"
                    v-model="queryParams.upYgName"
                  >
                    <a-select-option
                      v-for="(item, index) in ygNameList"
                      :key="index"
                      :label="item"
                      :value="item"
                      >{{ item }}</a-select-option
                    >
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="48">
              <a-col :md="6" :sm="24">
                <a-form-item label="员工所属部门代号">
                  <a-select
                    show-search
                    allowClear
                    @search="fetchygNo"
                    @focus="ygDepData"
                    :filter-option="false"
                    v-model="queryParams.ygDep"
                  >
                    <a-select-option
                      v-for="(item, index) in ygDepList"
                      :key="index"
                      :label="item.deptCode"
                      :value="item.deptCode"
                      >{{ item.deptCode }}</a-select-option
                    >
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :md="6" :sm="24">
                <a-form-item label="员工所属部门名称">
                  <a-select
                    show-search
                    allowClear
                    @search="fetchYgName"
                    @focus="ygDepData"
                    :filter-option="false"
                    v-model="queryParams.ygDepName"
                  >
                    <a-select-option
                      v-for="(item, index) in ygDepList"
                      :key="index"
                      :label="item.name"
                      :value="item.name"
                      >{{ item.name }}</a-select-option
                    >
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :md="6" :sm="24">
                <a-form-item label="干部所属部门代号">
                  <a-select
                    show-search
                    allowClear
                    @search="fetchygNo"
                    @focus="ygDepData"
                    :filter-option="false"
                    :notFoundContent="null"
                    v-model="queryParams.upYgDep"
                  >
                    <a-select-option
                      v-for="(item, index) in ygDepList"
                      :key="index"
                      :label="item.deptCode"
                      :value="item.deptCode"
                      >{{ item.deptCode }}</a-select-option
                    >
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :md="6" :sm="24">
                <a-form-item label="干部所属部门名称">
                  <a-select
                    show-search
                    allowClear
                    @search="fetchYgName"
                    @focus="ygDepData"
                    :filter-option="false"
                    :notFoundContent="null"
                    v-model="queryParams.upYgDepName"
                  >
                    <a-select-option
                      v-for="(item, index) in ygDepList"
                      :key="index"
                      :label="item.name"
                      :value="item.name"
                      >{{ item.name }}</a-select-option
                    >
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="48">
              <a-col style="text-align: right">
                <a-button type="primary" @click="handleQuery">{{ $t('public.query') }}</a-button>
                
              </a-col>
            </a-row>
          </a-form>
        <!-- </a-collapse-panel> -->
      <!-- </a-collapse> -->
    </div>
    <div>
          <vxe-toolbar custom>
         
        </vxe-toolbar>
      <vxe-table
        border
        resizable
        stripe
        highlight-current-row
        show-overflow
        highlight-hover-row
        export-config
        ref="xTable"
        size="mini"
        :loading="loading"
        :data="tableData"
        :keyboard-config="{ isArrow: true }"
        :edit-config="{ trigger: 'click', mode: 'row' }"
      >
        <vxe-table-column field="sysDate" title="单据日期" align="center" width="150"></vxe-table-column>
        <vxe-table-column field="endTime" title="报工日期" align="center" width="150"></vxe-table-column>
        <vxe-table-column field="ygNo" title="员工代号" align="center" width="150"></vxe-table-column>
        <vxe-table-column field="ygName" title="员工名称" align="center" width="150"></vxe-table-column>
        <vxe-table-column field="ygDep" title="员工所属部门代号" align="center" width="150"></vxe-table-column>
        <vxe-table-column field="ygDepName" width="150" title="员工所属部门名称" align="center"></vxe-table-column>
        <vxe-table-column field="qty" title="数量" align="center" width="150"></vxe-table-column>
        <vxe-table-column field="useTime" title="工时" align="center" width="150"></vxe-table-column>
        <vxe-table-column field="standardHours" title="标准工时" align="center" width="150"></vxe-table-column>
        <vxe-table-column field="differencesHours" title="差异工时" align="center" width="150"></vxe-table-column>
        <vxe-table-column field="aboveRewardStandard" title="超产补额标准" align="center" width="150"></vxe-table-column>
        <vxe-table-column field="aboveReward" title="超产奖励" align="center" width="150"></vxe-table-column>
        <vxe-table-column field="hours" title="补工时" align="center" width="150"></vxe-table-column>
        <vxe-table-column field="fillAmountStandard" title="工时补额标准" align="center" width="150"></vxe-table-column>
        <vxe-table-column field="fillamount" title="补工时金额" align="center" width="150"></vxe-table-column>
        <vxe-table-column field="amount" title="总可得金额" align="center" width="150"></vxe-table-column>
        <vxe-table-column field="upYgNo" title="所属干部代号" align="center" width="150"></vxe-table-column>
        <vxe-table-column field="upYgName" title="所属干部名称" align="center" width="150"></vxe-table-column>
        <vxe-table-column field="upYgDep" title="干部所属部门代号" align="center" width="150"></vxe-table-column>
        <vxe-table-column field="upYgDepName" title="干部所属部门名称" align="center" width="150"></vxe-table-column>
        <vxe-table-column field="zcNo" title="制程号" align="center" width="150"></vxe-table-column>
        <vxe-table-column field="zcName" title="制程名称" align="center" width="150"></vxe-table-column>
        <vxe-table-column field="prdNo" title="货品号" align="center" width="150"></vxe-table-column>
        <vxe-table-column field="prdName" title="货品名" align="center" width="150"></vxe-table-column>
        <vxe-table-column field="pfNo" title="版本号" align="center" width="150"></vxe-table-column>
      </vxe-table>
      <vxe-pager
        :loading="loading"
        :current-page="tablePage.currentPage"
        :page-size="tablePage.pageSize"
        :total="tablePage.total"
        :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
        @page-change="handlePageChange"
      >
      </vxe-pager>
    </div>
  </a-card>
      </el-tab-pane>
</el-tabs>
</div>
</template>

<script>
import axios from 'axios'
import { findList } from '@/api/admin/user'
import { getFetchList } from '@/api/formsbarcode/tablefcim'
export default {
  data() {
    return {
      customStyle: 'background: white;border:0px;',
      activeKey: ['1'],
      types: [null, undefined, ''],
      queryParams: {
        sysStart: '',
        sysEnd: '',
        ygDep: '',
        ygNo: '',
        ygName: '',
        upYgNo: '',
        upYgName: '',
        bgStart: '',
        bgEnd: '',
        upYgDep: '',
        upYgDepName: '',
      },
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
      loading: false,
      ygDepPage: 1,
      ygDepSize: 10,
      ygNoTotal: 0,
      tableData: [],
      ygDepList: [],
      ygNoList: [],
      ygNameList: []
    }
  },
  created() {
    this.queryParams.sysStart = moment().startOf('month').format("YYYY-MM-DD")
    this.queryParams.sysEnd = moment().endOf('month').format("YYYY-MM-DD")
  },
  mounted() {
    this.getList()
  },
  methods: {
    getList() {
      this.loading = true
      getFetchList(
        Object.assign(
          {
            current: this.tablePage.currentPage,
            size: this.tablePage.pageSize,
          },
          this.queryParams
        )
      )
        .then((res) => {
          this.loading = false
          this.tableData = res.data.records
          this.tablePage.total = res.data.total
        })
        .catch((err) => {
          this.loading = false
          this.tableData = []
          this.requestFailed(err)
        })
    },
    handlePageChange({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getList()
    },
    handleQuery() {
      if (this.types.includes(this.queryParams.sysStart) || this.types.includes(this.queryParams.sysEnd)) return this.$message.warning('请选择开始日期或结束日期！')
      if (moment(this.queryParams.sysEnd).diff(moment(this.queryParams.sysStart), 'days') < 0) return this.$message.warning('结束日期不能小于开始日期！')
      if (moment(this.queryParams.sysEnd).diff(moment(this.queryParams.sysStart), 'days') >= this.$setDays) return this.$message.warning(`开始日期和结束日期不能大于${this.$setDays}天！`)
      this.tablePage.currentPage = 1
      this.getList()
    },
    reset() {
      this.queryParams = {
        sysStart: '',
        sysEnd: '',
        ygDep: '',
        ygNo: '',
        ygName: '',
        upYgNo: '',
        upYgName: '',
        bgStart: '',
        bgEnd: '',
        upYgDep: '',
        upYgDepName: '',
      }
    },
    // 获取所属部门代号，名称
    ygDepData(page = 1, deptCode = '', name = '') {
      axios
        .get('/admin/dept/page', {
          params: {
            current: page,
            size: 10,
            deptCode: deptCode,
            name: name,
          },
        })
        .then((res) => {
          this.ygDepList = res.data.records
        })
        .catch((err) => {
          this.requestFailed(err)
        })
    },

    // 获取员工代号
    ygNoData(page = 1, salNo = '') {
      axios
        .get('/basic/salm/queryNo', {
          params: {
            current: page,
            size: 10,
            salNo: salNo,
          },
        })
        .then((res) => {
          this.ygNoList = res.data.records
          this.ygNoTotal = res.data.total
        })
        .catch((err) => {
          this.requestFailed(err)
        })
    },
    // 获取员工姓名
    ygNameData(page = 1, salName = '') {
      axios
        .get('/basic/salm/queryName', {
          params: {
            current: page,
            size: 10,
            salName: salName,
          },
        })
        .then((res) => {
          this.ygNameList = res.data.records
        })
        .catch((err) => {
          this.requestFailed(err)
        })
    },
    fetchygNo(value) {
      this.ygDepData(1, value)
    },
    fetchYgName(value) {
      this.ygDepData(1, '', value)
    },
    fetchUser(value) {
      this.ygNoData(1, value)
    },
    fetchYgDepName(value) {
      this.ygNameData(1, value)
    },
  },
}
</script>

<style>
</style>