<template>
  <div>
    <iframe
      ref="iframe"
      :src="ivxPath"
      width="100%"
      frameborder="0"
      @load="onLoad"
      :style="{ height: 'calc(100vh - 80px)' }"
    ></iframe>
  </div>
</template>
<script>
import Cookies from 'js-cookie'
import axios from 'axios'
import { loginIvx } from '@/api/interfaceList'
export default {
  props: {
    ivxPath: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      url: '', // 替换为实际第三方链接
    };
  },
  methods: {
    async fetchUsers() {
      try{
        // let s = { userName: 'admin', password: '123456'} ;
        // await axios({
        //   method:'post',
        //   data:s,
        //   url:'http://127.0.0.1:8999/api/11759249/cqswccs2ntpg000en2d0'
        // }).then(res=>{
        //     debugger
        //   console.log(res.data)
        // })


        // loginIvx({ userName: 'admin', password: '123456'}).then(response => {
        //   debugger
        //   const code = response.code;
        //   if (code === 200) {
        //     Cookies.set('ucenterAdministrator_11759249',response.reason,{ expires: 1, domain: '127.0.0.1' })
        //   }
        // }).catch(error => {
        //   console.error('获取用户列表失败:', error);
        // })

      } catch (error) {
        console.error('获取用户列表失败:', error);
      }
    },
    onLoad() {
      //首先获取cookie
      this.fetchUsers()
    },
  },
};
</script>

<style scoped>
iframe {
  display: block;
}
</style>
