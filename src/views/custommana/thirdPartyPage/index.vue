<template>
  <ThirdPartyPage ref='ivx' :ivxPath="ivxPath"/>
</template>

<script>
import ThirdPartyPage from './openPage.vue';
import Cookies from 'js-cookie'
export default {
  components: {
    ThirdPartyPage,
  },
  data() {
    return {
      prefix: '',
      ivxPath: '',
    };
  },
  mounted() {
    console.log('mounted')
  },
  created: function() {
    console.log(this.$route)
    this.prefix = Cookies.get('ivxAddress')+'/route/'
    this.ivxPath = this.prefix + this.$route.meta.ivxPath
  }
};
</script>

<style>
/* 你的样式 */
</style>
