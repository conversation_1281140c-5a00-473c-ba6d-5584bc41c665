<template>
  <div>
    <!-- 新增弹窗 -->
    <el-drawer :title="$t(title)" :visible.sync="drawer" size="40%" ref="drawerRef" class="JustMake-drawer">
      <div class="drawer-content">
        <el-form ref="drawerFormRef" :model="formData" label-position="right" label-width="120px" class="drawer-form">
          <el-row>
            <el-col v-for="(item, index) in formDataRaw" :key="index" :span="item.span || 12">
              <el-form-item :label="$t(item.field)" :title="$t(item.field)" :prop="getPropName(item.field)"
                            :rules="item.rules">
                <Forms :data="item" v-model="formData[getPropName(item.field)]" @iconClick="handelCompEvent"
                       @change="handleFormsChange" @selectListEvent="handleSelectListEvent"></Forms>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="drawer-footer">
        <el-button @click="handleCancel">{{ $t('public.cancel') }}</el-button>
        <el-button type="primary" @click="handleCreate" :loading="drawerLoading">{{ drawerLoading ? 'Loading ...' :
          $t('public.sure')
          }}</el-button>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import Forms from '@/components/Forms/index.vue'
import {add, edit} from '@/api/custommana/templateSettings'
import Vue from 'vue'
export default {
  components: {
    Forms,
  },
  data() {
    return {
      api_save: add,
      drawer: false,
      drawerLoading: false,
      title: 'public.add',
      status: '0', // 0 新增 1 编辑
      roleTypeList: [],
      formData: {},
      currentRow: {},
      formDataRaw: [
        {
          field: "tempSetting.prdNo", type: 'selectList',
          props:{
            url: '/basic/prdt/page',
            tableColumn: this.$Column.apsPrd,
            form: this.$Form.apsPrd,
          },
          rules: [{ required: true, message:this.$t('tempSetting.placeholder.prdNo') }]
        },
        { field: 'tempSetting.prdName', type: 'input',disabled:true },
        {
          field: 'tempSetting.cusNo', type: 'selectList',
          props: {
            url: '/mes/basicData/custPage2',
            tableColumn: this.$Column.cusNo,
            form: this.$Form.cusNo,
          }
        },
        { field: 'tempSetting.spc', type: 'input',disabled:true },
        {
          field: 'tempSetting.prttype', type: 'selectList',
          props: {
            url: '/mes/mesPrdtPrtid/page/prttype',
            tableColumn: this.$Column.prttype,
            form: this.$Form.prttype,
          },
          rules: [{ required: true, message:this.$t('tempSetting.placeholder.prttype') }]
        },
        { field: 'tempSetting.prttypeName', type: 'input',disabled:true},
        {
          field: 'tempSetting.prttepNo', type: 'selectList',
          props: {
            url: '/admin/report_config/page',
            tableColumn: this.$Column.printTpl,
            form: this.$Form.printTpl,
          }
        },
        { field: 'tempSetting.prttepName', type: 'input',disabled:true },
        {
          field: 'tempSetting.instructionType', type: 'select',
          values: [
            { label: 'tspl', value: 'tspl' },
            { label: 'cpcl', value: 'cpcl' },
            { label: 'esc',  value: 'esc'},
            { label: 'zpl', value: 'zpl'},
          ]
        },
        {
          field: 'tempSetting.dpi', type: 'select',
          values: [
            { label: 203, value: 203 },
            { label: 300, value: 300 },
            { label: 600,  value: 600},
          ]
        },
      ],
    }
  },
  created() {

  },
  watch: {
    status: function (newVal) {
      if (newVal === '0') {
        this.title = 'public.add'
        this.api_save = add
      } else {
        this.title = 'public.edit'
        this.api_save = edit
      }
    }
  },
  methods: {
    handleVisible() {
      this.status = '0'
      this.formData = {}
      this.$nextTick(() => {
        this.$refs.drawerFormRef.clearValidate()
      })
      this.drawer = !this.drawer
    },
    getPropName(field) {
      return field.includes('.') ? field.split('.').pop() : field;
    },
    handleFormsChange(params) {
      this.$emit('formDataChange', params)
    },
    handleSelectListEvent(param) {
      if (param.field === "tempSetting.prdNo") {
        Vue.set(this.formData, 'prdNo', param.obj.id);
        Vue.set(this.formData, 'prdName', param.obj.name);
        Vue.set(this.formData, 'spc', param.obj.data.spc);
      }else if (param.field === "tempSetting.prttepNo") {
        Vue.set(this.formData, 'prttepNo', param.obj.id);
        Vue.set(this.formData, 'prttepName', param.obj.name);
        Vue.set(this.formData, 'billType', param.obj.data.bill_type);
      }else if (param.field === "tempSetting.prttype") {
        Vue.set(this.formData, 'prttype', param.obj.data.prttype);
        Vue.set(this.formData, 'prttypeName', param.obj.data.prttypeName);
      }
    },
    handelCompEvent(params) {
      this.$emit('formItemIconClick', params)
    },
    handleCancel() {
      this.formData = {}
      this.$nextTick(() => {
        this.$refs.drawerFormRef.clearValidate()
      })
      this.drawer = false
    },
    async handleCreate() {
      if (this.drawerLoading) {
        return;
      }
      if (!this.api_save || typeof this.api_save !== 'function') {
        return this.$message.error('请配置 api_save 参数');
      }
      // 表单校验
      try {
        await this.$refs.drawerFormRef.validate();
      } catch (error) {
        return;
      }
      let result = null;
      try {
        this.drawerLoading = true;
        result = await this.api_save(this.formData)
        if (result.code == 0) {
          this.$message.success(this.$t('public.success'))
          this.$emit('refresh')
          this.drawer = false;
        }
      } catch (err) {
        console.error(err)
        this.$message.error(err || this.$t('public.error'));
      } finally {
        this.drawerLoading = false;
        // this.$emit('toolbarClick', { code: 'create', result });
      }
    },
    handleEdit(row) {
      this.currentRow = row
      this.status = '1'
      this.drawer = true
      this.$nextTick(() => {
        this.formData = Object.assign({}, row) // 拷贝数据
      })
    },
  },
}
</script>

<style scoped>

</style>