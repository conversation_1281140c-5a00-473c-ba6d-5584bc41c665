<template>
  <div class="layout">
    <vToolbar :data="toolbarItems" @toolbarClick="handleToolbarClick"></vToolbar>
    <div style="margin-top: 25px">
      <el-form ref="ruleForm" label-position="left" label-width="auto" size="mini" :model="form" :rules="rules"
        :show-message="false">
        <el-row :gutter="20">
          <el-col :span="6" :xs="24">
            <el-form-item :label="$t('arc.newList.menuNo')" prop="deliveryType">
              <el-input :disabled="this.edit" v-model="form.bill_type" size="small">
                <template #suffix>
                  <i class="el-icon-search" @click="handleIconClick"></i>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6" :xs="24">
            <el-form-item :label="$t('arc.newList.isPublic')">
              <el-select v-model="form.is_default" size="small" class="selectInputWidth">
                <el-option v-for="item in option" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6" :xs="24">
            <el-form-item :label="$t('arc.newList.datasetType')" prop="commandText">
              <el-select :disabled="!edit" v-model="form.commandText" :placeholder="$t('arc.newList.select')" size="small"
                class="selectInputWidth">
                <el-option v-for="item in options" :key="item.value" :label="$t(item.label)" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6" :xs="24">
            <el-form-item>
              <template #label>
                <span>{{ $t('arc.newList.switchDatasource') }}</span>
                <el-tooltip effect="dark" :content="$t('arc.newList.iconTooltip0')" placement="top">
                  <i class="el-icon-question"></i>
                </el-tooltip>
              </template>
              <el-switch v-model="form.switch_data_source"></el-switch>
            </el-form-item>
<!--            <el-form-item :label="$t('arc.newList.switchDatasource')">-->
<!--              <el-switch v-model="form.switch_data_source"></el-switch>-->
<!--            </el-form-item>-->
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6" :xs="24">
            <el-form-item :label="$t('arc.newList.interfaceName')" prop="name">
              <el-input v-model="form.name" clearable />
            </el-form-item>
          </el-col>

          <el-col :span="9" :xs="24">
            <el-form-item label="URL" prop="connectString">
              <el-row>
                <el-col :span="20">
                  <el-input :disabled="form.custom_sql" placeholder="/admin/user/info" v-model="form.connectString"
                    class="eli">
                  </el-input>
                </el-col>
                <el-col :span="4">
                  <el-select :disabled="form.custom_sql" v-model="form.suffix" :placeholder="$t('arc.newList.select')"
                    class="els">
                    <el-option :label="$t('arc.newList.noParameter')" value=";"></el-option>
                    <el-option :label="$t('arc.newList.haveParameter')" value="/?@PARAMS@;"></el-option>
                  </el-select>
                </el-col>
              </el-row>
            </el-form-item>
          </el-col>
          <el-col :span="3" :xs="24">
            <el-form-item :label="$t('arc.newList.customSql')" prop="name">
              <el-switch  v-model="form.custom_sql" @change="handleSwitchChange"></el-switch>
<!--              <el-switch :inactive-text="$t('arc.newList.customSql')" v-model="form.custom_sql" @change="handleSwitchChange"></el-switch>-->
            </el-form-item>
          </el-col>
          <el-col :span="3" :xs="24">
            <el-form-item>
              <template #label>
                <span>{{ $t('arc.newList.reportFiled') }}</span>
                <el-tooltip effect="dark" :content="$t('arc.newList.iconTooltip1')" placement="top">
                  <i class="el-icon-question"></i>
                </el-tooltip>
              </template>
              <el-switch v-model="form.report_filed" @change="handleReportType"></el-switch>
            </el-form-item>
<!--            <el-form-item :label="$t('arc.newList.reportFiled')">-->
<!--              <el-switch v-model="form.report_filed" @change="handleReportType"></el-switch>-->
<!--            </el-form-item>-->
          </el-col>
          <el-col :span="20" :xs="24" v-show="form.custom_sql && !form.report_filed">
            <el-form-item :disabled="form.custom_sql" :label="$t('arc.newList.sqlStatement')">
              <el-input type="textarea" :rows="20" v-model="form.sql_script"
                :placeholder="$t('arc.newList.pleaseEnterSQL')"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div v-show="!form.custom_sql || form.report_filed">
      <el-divider content-position="left"></el-divider>
      <el-table v-loading="loading" :data="tableData" stripe
        :header-cell-style="{ backgroundColor: '#F4F5F9', fontWeight: '400', }" highlight-current-row
        style="width: 100%;margint-top:30px;">
        <el-table-column  :label="$t('arc.newList.filedName')" prop="name">
          <template v-slot="scope">
            <div>
              <el-input v-if="scope.row.edit" v-model="scope.row.dataField" size="mini" />
              <span v-else>{{ scope.row.dataField }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column :label="$t('arc.newList.name')" prop="dataField">
          <template v-slot="scope">
            <div>
              <el-input v-if="scope.row.edit" v-model="scope.row.name" size="mini" />
              <span v-else>{{ scope.row.name }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column :label="$t('arc.newList.filedType')" prop="dataType">
          <template v-slot="{ row }">
            <div style="margin-bottom:5px;">
              <el-select :disabled="!row.edit" v-model="row.dataType" size="mini" style="max-width: 360px;width:100%;"
                placeholder="">
                <el-option v-for="item in fileType" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </div>
          </template>
        </el-table-column>
        <el-table-column :label="$t('public.action')">
          <template v-slot="scope">
            <el-button v-if="!scope.row.edit" type="text" @click="handleChildrenEdit(scope)">{{ $t('public.edit') }}</el-button>
            <el-button v-if="scope.row.edit" type="text" @click="handleChildrenSave(scope)">{{ $t('public.save') }}</el-button>
            <el-button type="text" @click="handleChildrenDel(scope)">{{ $t('public.delete') }}</el-button>
            <el-button type="text" @click="handleAdd()">{{ $t('public.add') }}</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-dialog height="400px" width="800px" :title="$t('arc.overprint.printTemplate.select')" :visible.sync='selectVisible' :close-on-click-modal='false' top="15vh"
               append-to-body>
      <div class="sync-dialog__div">
        <PrintTemplate ref="printTemplateRef" @selectId="handleSelectClick"></PrintTemplate>
      </div>
    </el-dialog>
  </div>
</template>
<script>

import vToolbar from '@/components/amtxts/vTable/vToolbar.vue'
import {
  getTypeList,
  registeringDataFieldAdd,
  registeringDataFieldDel,
  registeringDataFieldList,
  registeringDataSourceAdd
} from '@/api/interfaceList'
import { fetchReportTestSql } from '@/api/printTemplateDesign'
import Vue from 'vue'
import PrintTemplate from '@/views/custommana/overprint/printTemplate.vue'
export default {
  name: 'PrintInfo',
  props: {
    fromDataset: {
      type: Object,
      default: () => { }
    },
    isEdit: Boolean
  },
  components: {
    PrintTemplate,
    vToolbar
  },
  data() {
    return {
      selectVisible: false,
      tablePage: {
        currentPage: 1,
        pageSize: 1000,
        total: 0
      },
      loading: false,
      activeName: 'first',
      parentId: '',
      // prefix: '',
      suffix: '',
      tableData: [],
      tableData2: [],
      expireTime: '',
      edit: false,
      form: {
        id: null,
        bill_type: '',
        commandText: '',
        is_default: false,
        dataSourceName: '',
        name: '',
        connectString: '',
        suffix: ';',
        prefix: 'jsondoc=',
        deliveryType: '',
        custom_sql: true,
        switch_data_source: true,
        report_filed:false,
        sql_script: ''
      },
      option: [
        {
          label: this.$t('public.T'),
          value: true
        },
        {
          label: this.$t('public.F'),
          value: false
        }
      ],
      routerobj: {},
      rules: {
        name: [{ required: true, message: this.$t('arc.index.rulesName'), trigger: 'blur' }],
        connectString: [{ required: true,  message:this.$t('arc.index.rulesConnectString'), trigger: 'blur' }],
        commandText: [{ required: true, message: this.$t('arc.index.rulesDatasetType'), trigger: 'blur' }]
      },
      fileType: [{ value: 'String', label: 'String' }, { value: 'Integer', label: 'Integer' }, {
        value: 'Date',
        label: 'Date'
      }, { value: 'DateTime', label: 'DateTime' }],
      options: [
        // { label: '$', value: '$' },
        // { label: '$.[*]', value: '$.[*]' },
        { label: 'arc.newList.singleItem', value: '$.data' },
        { label: 'arc.newList.list', value: '$.data.[*]' },
        // { label: '$.data.records.[*]', value: '$.data.records.[*]' }
      ],
      toolbarItems: [
        { label: 'arc.newList.titleName', value: 'title' },
        { label: 'public.save', value: 'save',icon:'icon-add' },
        { label: 'arc.newList.sqlTest', value: 'sqlTest' },
        { label: 'public.query', value: 'query',visible:false },
        // { label: 'public.return', value: 'return' },
      ]
    }
  },
  watch: {
    isEdit(newVal) {
      this.isEdit = newVal;
    },
    fromDataset(newVal) {
      this.form = newVal
      this.getList()
    }
  },
  mounted() {
    if (this.$route.params.obj) {
      this.routerobj = JSON.parse(decodeURIComponent(this.$route.params.obj))
      this.edit = true
      this.form = this.routerobj
      if (this.form.is_default == true) {
        Vue.set(this.form, 'is_default', true);
      } else {
        Vue.set(this.form, 'is_default', false);
      }
      this.getList()
    } else {
      this.edit = false
      this.form.commandText = this.options[0].value
      Vue.set(this.form, 'is_default', false);
      this.form.prefix = 'jsondoc='
      this.form.suffix = ';'
    }
  },
  methods: {
    handleToolbarClick(params) {
      switch (params) {
        case 'created':
          this.handleAdd();
          break
        case 'edit':
          this.handleEdit();
          break
        case 'save':
          this.saveDateSource('ruleForm');
          break
        case 'sqlTest':
          this.handleTestSql();
          break
        case 'return':
          this.$router.push({ path: '/custommana/interfaceList/index' })
          break
        default:
      }
    },
    handleIconClick() {
      this.selectVisible = true
    },
    handleSelectClick(prams) {
      this.displayedName = prams.label
      this.displayedType = prams.parent_id
      Vue.set(this.form, 'bill_type', prams.parent_id);
      Vue.set(this.form, 'bill_type_name', prams.label);
      this.selectVisible = false
    },
    init(params) {
      this.form = params
      this.
      this.getList()
    },
    handleDblclick(param) {
      let obj = JSON.stringify(param)
      this.$router.push('/custommana/interfaceList/newList?obj=' + encodeURIComponent(obj))
    },
    handleSubmit() {
      this.$refs.vTable.handleGet()
    },
    handleEdit() {
      this.update = 'none'
      this.save = ''
      this.edit = true
    },
    handleAdd() {
      this.tableData.unshift({
        dataField: '',
        dataType: '',
        name: '',
        id: '',
        edit: true
      })
    },
    handleChildrenEdit(scope) {
      this.tableData.forEach(j => {
        j.edit = false
      })
      scope.row.edit = true
      this.$delete(scope.row, 'edit')
      this.$set(scope.row, 'edit', true)
      this.update = true
      this.save = false
    },
    handleChildrenDel(scope) {
      if (scope.row.id) {
        let that = this
        this.$confirm(this.$t('public.del.content'), this.$t('public.del.title'), {
          confirmButtonText: this.$t('public.sure'),
          cancelButtonText: this.$t('public.cancel'),
          type: 'warning'
        }).then(() => {
          registeringDataFieldDel({
            id: scope.row.id
          }).then(res => {
            that.$message.success(res.msg)
            that.getList()
          }).catch(err => {
            that.requestFailed(err)
          })
        }).catch(() => {
        });
      } else {
        this.tableData.splice(scope.$index, 1)
      }
    },
    handleChildrenSave(scope) {
      if (!scope.row.dataType) return this.$message.error(this.$t('arc.newList.errorMsgFiledType'));
      if (!scope.row.dataField) return this.$message.error(this.$t('arc.newList.errorMsgFiledName'))
      if (!scope.row.name) return this.$message.error(this.$t('arc.newList.errorMsgFiledName1'))
      if (this.form.dataSourceName === undefined || this.form.dataSourceName == '') return this.$message.error(this.$t('arc.newList.errorMsgSave'))
      registeringDataFieldAdd({
        id: scope.row.id,
        dataType: scope.row.dataType,
        dataField: scope.row.dataField,
        name: scope.row.name,
        version: scope.row.version,
        dataSourceName: this.form.dataSourceName
      })
        .then(res => {
          this.$message.success(res.msg)
          this.getList()
        })
        .catch(err => {
          // this.requestFailed(err)
          this.$message.error(err)
        })
    },
    getList() {
      let _that = this;
      if (this.form.dataSourceName) {
        registeringDataFieldList({
          current: this.tablePage.currentPage,
          size: this.tablePage.pageSize,
          dataSourceName: this.form.dataSourceName
        }).then(res => {
          if (res.data.records && res.data.records.length === 0 && _that.tableData.length === 0) {
            this.handleAdd()
          }else {
            this.tableData = res.data.records
          }
        }).catch(err => {
          this.requestFailed(err)
        })
      }else {
        if ( _that.tableData.length === 0) {
          this.handleAdd()
        }
      }
    },
    saveDateSource(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.form.dataProvider = 'JSON'
          let obj = {
            id: this.form.id,
            bill_type: this.form.bill_type,
            version: this.form.version,
            commandText: this.form.commandText,
            is_default: this.form.is_default,
            dataSourceName: this.form.dataSourceName,
            name: this.form.name,
            connectString: this.form.connectString,
            dataProvider: this.form.dataProvider,
            prefix: this.form.prefix,
            suffix: this.form.suffix,
            sql_script: this.form.report_filed ? '' : this.form.sql_script,
            switch_data_source: this.form.switch_data_source,
            custom_sql: this.form.custom_sql,
            report_filed: this.form.report_filed
          }
          registeringDataSourceAdd(obj).then(res => {
            this.$message.success(res.msg)
            // this.edit = false
            this.form = res.data
            this.update = ''
          })
            .catch(err => {
              this.requestFailed(err)
            })
        } else {
          return false
        }
      })
    },
    handleCurrentChange(currentPage) {
      this.tablePage.currentPage = currentPage
      this.getList()
    },
    handleSizeChange(pageSize) {
      this.tablePage.pageSize = pageSize
      this.getList()
    },
    /**
     * 自定义SQL
     * @param newValue
     */
    handleSwitchChange(newValue) {
      if (newValue || this.form.report_filed) {
        this.form.connectString = '/api/sys/report_sql'
      } else {
        this.form.connectString = ''
        this.handleAdd()
      }
    },
    /**
     * 报表字段
     * @param newValue
     */
    handleReportType(newValue) {
      if (newValue) {
        this.handleAdd()
        this.form.connectString = '/api/sys/report_sql'
        if (newValue) {
          Vue.set(this.form, 'commandText', '$.data.[*]')
        }
      } else {
        this.form.connectString = ''
      }
    },
    handleTestSql() {
      fetchReportTestSql({ 'sql_script': this.form.sql_script, 'switch_data_source': this.form.switch_data_source }).then(res => {
        if (res.data === 1) {
          this.$message.success(res.msg)
        } else {
          this.$message.error(res.data)
        }
      })
    }

  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-input--suffix .el-input__inner {
  padding-right: 25px;
}
::v-deep .eli input {
  border-radius: 4px 0px 0px 4px;
}

::v-deep .els input {
  background: rgb(245, 247, 250);
  border-radius: 0px 4px 4px 0px;
  border-left: none;
}

.selectInputWidth {
  width: 100%;
}
::v-deep .el-form-item__label {
  white-space:nowrap
}
.dialog-footer {
  padding: 10px 10px;
}

::v-deep .el-dialog {
  border-radius: 10px;
}

::v-deep .el-dialog__title {
  border-radius: 10px;
  line-height: 30px;
  font-size: 14px;
  color: #303133;
}

::v-deep .el-dialog__header {
  padding: 2px 10px;
  height: 30px;
  border-radius: 10px 10px 0 0;
  background-color: #dce6e8 !important;
  color: #ffffff !important;
}

::v-deep .el-dialog__body {
  padding: 0px 10px;
}

::v-deep .el-dialog__footer {
  padding: 0px;
}

::v-deep .el-dialog__headerbtn {
  position: absolute;
  top: 5px;
  right: 20px;
  padding: 0;
  background: 0 0;
  border: none;
  outline: 0;
  cursor: pointer;
  font-size: 16px;
}

</style>
