<template>
	<div class="layout">
		<vTable ref="vTable" v-bind="vTableProps" @toolbarClick="handleToolbarClick" @formItemIconClick="handleIconClick"
			@formDataChange="handleFormDataChange" @cellDblclick="handleDblclick" @selectListEvent="handleSelectListEvent">
		</vTable>
	</div>
</template>
<script>
import vTable from '@/components/amtxts/vTable/vGrid.vue'
import { findList, delAll } from '@/api/admin/user'
import { registeringDataSourcePage, registeringDataSourceDel } from '@/api/interfaceList.js'
import Vue from 'vue'
export default {
	name: 'UserList',
	components: {
		vTable
	},
	data() {
		return {
			formData: [],
			vTableProps: {
				delete_key: '$id:$id',
				api_find: registeringDataSourcePage,
				api_delete: registeringDataSourceDel,
				toolbarItems: [
					{ label: 'arc.interfaceList.titleName', value: 'title' },
				],
        formDataRaw: [
          { field: 'arc.interfaceList.name', type: 'input',placeholder: this.$t('public.placeholder.name')},
          { field: 'arc.interfaceList.bill_type', type: 'selectList',
            props:{
              url: '/api/sys/report_print/list',
              tableColumn: this.$Column.menuNo,
              form: this.$Form.menuNo,
              placeholder: this.$t('public.placeholder.numb')
            }
          },
        ],
				tableColumn: [
					{ type: "seq", width: '50', fixed: "left", },
					{ type: "checkbox", width: '50', fixed: "left", },
					{ field: "bill_type", title: "arc.interfaceList.bill_type", },
					{ field: "name", title: "arc.interfaceList.interfaceName", },
					{ field: "connectString", title: "arc.interfaceList.interfaceAddress", },
					{ field: 'dataProvider', title: 'arc.interfaceList.interfaceType', },
					{
						field: 'is_default', title: 'arc.interfaceList.isPublic',
						slots: {
							default: ({ row }) => {
								return this.$createElement('el-tag', {
									props: {
										type: row.is_default === true ? 'primary' : 'danger'
									},
									domProps: {
										innerHTML: row.is_default === true ? this.$t(`public.T`) : this.$t(`public.F`)
									}
								})
							}
						}
					},
				],
			}
		}
	},
	methods: {
		handleToolbarClick(params) {
			switch (params.code) {
				case 'create':
					this.$router.push({ name: 'interfaceDetailList',params: { obj:null,state:false } })
					break;
				default:
			}
		},
		handleIconClick(params) {

		},
		handleFormDataChange(params) {
		},
		handleChoose(params) {
		},
		handleSelectListEvent(params) {
      this.$refs.vTable.formData.bill_type = params.obj.data.parent_id
		},
		handleDblclick(param) {
			let obj = JSON.stringify(param);
			this.$router.push({
				name: 'interfaceDetailList',
				params: { obj: encodeURIComponent(obj),state:true }
			});
			// this.$router.push("/custommana/interfaceList/newList?obj=" + encodeURIComponent(obj));
		},
		handleSubmit() {
			this.$refs.vTable.handleGet();
		}
	},
}
</script>
