<template>
  <div class="tab-container">
    <div class="card-container">
      <a-tabs v-model="activeName" type="card">
        <a-tab-pane key="1" tab="接口列表">
          <el-form ref="ruleForm" label-position="left" label-width="auto" size="mini" :model="form"  :show-message="false">
            <el-row :gutter="20">
              <el-col :span="6" :xs="24">
                <el-form-item label="URL:">
                  <el-input v-if="edit" v-model="form.usrName" />
                  <span v-else>{{ form.usrName }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="6" :xs="24">
                <el-form-item label="接口名称:">
                  <el-select v-if="edit"  v-model="form.deliveryType" size="mini" style="max-width: 360px;width:100%;" placeholder="" @change="selectChange">
                      <el-option
                        v-for="item in datechoice"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  <span v-else>{{ form.usrName }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="6" :xs="24">
                <el-form-item label="类型:">
                  <el-input v-if="edit" v-model="form.usrName" />
                  <span v-else>{{ form.usrName }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="6" :xs="24">
                <el-button type="primary" @click="handleEdit">编辑</el-button>
                <el-button type="primary" @click="handleQuery">保存</el-button>
              </el-col>
            </el-row>
          </el-form>
          <div style="text-align:right;margin-right:20px;"><el-button  type="primary" icon="plus" @click="handleAdd()">新增</el-button>
          <el-button type="primary" @click="handleQuery">保存</el-button>
          </div>

          <el-table v-loading="loading" :data="tableData" stripe
            :header-cell-style="{ backgroundColor: '#F4F5F9', fontWeight: '400', }"
            highlight-current-row style="width: 100%;margint-top:30px;"
            @selection-change="handleSelectionChange"
            @row-click="handleRowClick"
            >
              <el-table-column type="selection" width="60" align="center"  />

              <el-table-column label="字段名称" prop="bill_type_name">
                <template v-slot="scope">
                  <div>

                    <el-input v-if="scope.row.edit" v-model="scope.row.bill_type_name" readonly size="mini" />
                    <span v-else>{{ scope.row.bill_type_name }}</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="字段类型" prop="code">
                <template v-slot="{row}">
                  <div  style="margin-bottom:5px;">
                    <el-select :disabled="!row.edit" v-model="row.deliveryType" size="mini" style="max-width: 360px;width:100%;" placeholder="" @change="selectChange">
                      <el-option
                        v-for="item in datechoice"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>

                  </div>
                </template>
              </el-table-column>
              <el-table-column label="显示名称" prop="name">
                <template v-slot="scope">
                  <div>

                    <el-input v-if="scope.row.edit" v-model="scope.row.name" size="mini" />
                    <span v-else>{{ scope.row.name }}</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column :label="$t('table.actions')">

                <template v-slot="scope">
                <el-button v-if="!scope.row.edit"  type="text" @click="handleChildrenEdit(scope)">编辑</el-button>
                <el-button v-if="scope.row.edit" type="text" @click="handleChildrenSave(scope)">保存</el-button>
                <el-button type="text" @click="handleChildrenDel(scope)">删除</el-button>
              </template>

              </el-table-column>
            </el-table>
            <div style="margin-top: 20px;">
              <el-pagination
                background
                :current-page="tablePage.currentPage"
                :page-sizes="[5, 10, 50, 100]"
                :page-size="100"
                layout="total, sizes, prev, pager, next, jumper"
                :total="tablePage.total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </div>
        </a-tab-pane>
      </a-tabs>
    </div>
  </div>
</template>

<script>
// import { delCombine } from '@/api/features'
// import checkPermission from '@/utils/permission'
export default {
  data() {
    return {
      tablePage: {
        currentPage: 1,
        pageSize: 100,
        total: 0
      },
      loading: false,
      isEdit: false,
      activeName: '1',
      parentId: '',
      tableData: [
      ],
      tableData2: [],
      expireTime: '',
      edit:false,
      form: {
        usrName:'2'
      },
      datechoice: [
        { label: '长期', value: '0' },
        { label: '一年', value: '1' },
        { label: '三年', value: '2' },
      ],
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    // checkPermission,
    handleEdit(){
      this.edit = true
    },
    handleChildrenEdit(row) {
      this.tableData.forEach(j =>{
        j.edit = false
      })
      row.row.edit = true
      this.$forceUpdate()
    },
    handleChildrenDel(props, scope) {
      if(scope.row.id){
        mfBgCfmDiffDel({
        poState: this.form.poState,
        diffId: scope.row.id
      })
        .then(res => {
          this.$message.success(res.msg)
          this.getList()
          this.$refs.xTable.clearSelection()
        })
        .catch(err => {
          this.requestFailed(err)
        })
      }else{
       props.row.prdtDiffVoList.splice(scope.$index, 1)
      }
    },
    handleChildrenSave(props, scope) {
      if (!scope.row.estDd) return this.$message.error('请选择交货日期')
      if (!scope.row.qty) return this.$message.error('请填写数量')
      if (!scope.row.up) return this.$message.error('请填写单价')
      if (scope.row.estDd.indexOf(':') == -1) {
        scope.row.estDd = scope.row.estDd + ' 00:00:00'
      }
      supDiffSave({
        typeId: this.form.typeId,
        childId: props.row.id,
        id: scope.row.id,
        masterId: props.row.id,
        estDd: scope.row.estDd,
        qty: scope.row.qty,
        up: scope.row.up,
        amtn: scope.row.amtn,
        supRem: scope.row.supRem,
        comfireRem: scope.row.comfireRem
      })
        .then(res => {
          this.$message.success(res.msg)
          this.getList()
        })
        .catch(err => {
          this.requestFailed(err)
        })
    },
    getList() {
      this.tableData =[
        {bill_type_name:1,name:'ww',id:1,deliveryType:'2',},
        {bill_type_name:2,name:'fgdf',id:1,deliveryType:'1',}
      ],
      this.tableData.forEach((i, index) => {
        i.edit = false
      })
      fetchList({
        current: 1,
        size: 500
      })
        .then(res => {
          res.data.records.forEach(i => { i.edit = false })
          this.tableData = res.data.records
        })
        .catch(err => {
          this.requestFailed(err)
        })
    },
    handleAdd() {
      this.tableData.push({
        bill_type_name:3,
        name:'qqqq',
        id: null,
        version: null,
        edit: true
      })
    },
    handleDel({ id }) {
      delCombine({ id })
        .then(res => {
          this.$message.success(res.msg)
          this.getList()
          if (this.parentId === id) {
            this.tableData2 = []
            this.parentId = null
          }
        })
        .catch(err => {
          this.requestFailed(err)
        })
    },
    handleCurrentChange(currentPage) {
      this.tablePage.currentPage = currentPage
      this.getList()
    },
    handleSizeChange(pageSize) {
      this.tablePage.pageSize = pageSize
      this.getList()
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
