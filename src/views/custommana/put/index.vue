<template>
  <div class="consrm">
  <div class="tab-container">
    <div class="card-container">
      <el-table v-loading="loading"  :data="tableData"
                :header-cell-style="{ backgroundColor: '#F4F5F9', fontWeight: '400', verticalAlign: 'top' }"
                highlight-current-row style="width: 100%;"
      >
        <el-table-column type="selection" width="60" align="center"  />
        <el-table-column label="页面" prop="bill_type_name" />
        <el-table-column label="套版代码" prop="code" />
        <el-table-column label="套版名称" prop="name" />
        <el-table-column label="是否默认" prop="is_default" align="center">
          <template slot-scope="scope">
            <el-tag  v-if="scope.row.is_default === true">是</el-tag>
            <el-tag  v-if="scope.row.is_default === false">否</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template v-slot="{row}">
            <el-button type="text" @click="query(row)">查看</el-button>
            <div style="height: 23px;width: 100%;" />
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
      <el-dialog
        title='预览'
        :visible.sync='visible'
        :close-on-click-modal='false'
        width='55%'
      >
        <div class='viewer-container'>
          <ArViewer ref='arViewer' :report-id='reportId'  />
        </div>
      </el-dialog>
</div>

</template>
<script>
import { reportconPage } from '@/api/interfaceList'
import ArViewer from '@/components/printDialog/ArViewer.vue'
export default {
  components: { ArViewer},
  data() {
    return {
      tableData:[],
      loading:false,
      multipleSelection:[],
      visible: false,
      reportId: '',
      isFullScreen:true,
    }
  },
  mounted() {
    this.handleNodeClick()
  },
  methods: {
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    query(row){
      //this.entity = JSON.parse(JSON.stringify(row))
      this.visible = true
      var _this = this
      _this.reportId =row.id
      setTimeout(() => {
        this.$refs.arViewer.openReportEvent(_this.reportId)
      }, 300)

    },
    handleNodeClick() {
      reportconPage(
        {
          bill_type:'sql'
        }
      )
        .then(res => {
          this.loading = false
          this.tableData = res.data.records
        })
        .catch(err => {
          this.loading = false
          this.tableData = []
          this.requestFailed(err)
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.viewer-container {
  height: calc(100vh - 40px );
}
</style>
