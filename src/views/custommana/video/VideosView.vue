<template>
  <div class="videoWrap">
    <video-player
      class="video-player vjs-custom-skin"
      ref="videoPlayer"
      :playsinline="true"
      :options="playerOptions"
      @play="onPlayerPlay($event)"
      @pause="onPlayerPause($event)"
      @ended="onPlayerEnded($event)"
      @waiting="onPlayerWaiting($event)"
      @playing="onPlayerPlaying($event)"
      @loadeddata="onPlayerLoadeddata($event)"
      @timeupdate="onPlayerTimeupdate($event)"
      @canplay="onPlayerCanplay($event)"
      @canplaythrough="onPlayerCanplaythrough($event)"
      @statechanged="playerStateChanged($event)"
      @ready="playerReadied">
    </video-player>
  </div>
</template>

<script>

import VideoPlayer from 'vue-video-player/src/player.vue';
import 'video.js/dist/video-js.css';
import 'vue-video-player/src/custom-theme.css'

export default {
  components: {
    'video-player': VideoPlayer
  },
  data() {
    return {
      playerOptions: {
        loop:true,
        muted: true,
        language: 'zh-CN',
        playbackRates: [0.7, 1.0, 1.5, 2.0],
        sources: [{
          type: "video/mp4",
          src: ""
        }],
        height:'800'
      }
    }
  },
  mounted() {
    let videoSrc = this.$route.params.videoSrc;
    this.playerOptions.sources[0].src = videoSrc;
    const video = {
      src: videoSrc,  // 路径
      type: 'video/mp4'  // 类型
    };
    this.playerOptions.sources.push(video);
    // console.log(test)
  },
  computed: {
    player() {
      return this.$refs.videoPlayer.player;
    },
  },
  methods: {
    /*
     其实多数是列出的一些回调函数而已，不需要使用的话忽略掉即可
     */
    // 播放回调
    onPlayerPlay(player) {
      console.log("player play!", player);
    },
    // 暂停回调
    onPlayerPause(player) {
      console.log("player pause!", player);
    },
    // 视频播完回调
    onPlayerEnded(player) {
      console.log("player end!", player);
    },
    // DOM元素上的readyState更改导致播放停止
    onPlayerWaiting(player) {
      console.log("Player Waiting",player);
    },
    // 已开始播放回调
    onPlayerPlaying(player) {
      console.log("Player Playing",player);
    },
    // 当播放器在当前播放位置下载数据时触发
    onPlayerLoadeddata(player) {
      console.log("Player Loadeddata",player);
    },
    // 当前播放位置发生变化时触发。
    onPlayerTimeupdate(player) {
      console.log("Player Timeupdate",player);
    },
    // 媒体的readyState为HAVE_FUTURE_DATA或更高
    onPlayerCanplay(player) {
      console.log('player Canplay!', player)
    },
    // 媒体的readyState为HAVE_ENOUGH_DATA或更高。这意味着可以在不缓冲的情况下播放整个媒体文件。
    onPlayerCanplaythrough(player) {
      console.log('player Canplaythrough!', player)
    },
    //播放状态改变回调
    playerStateChanged(playerCurrentState) {
      console.log("player current update state", playerCurrentState);
    },
    //将侦听器绑定到组件的就绪状态。与事件监听器的不同之处在于，如果ready事件已经发生，它将立即触发该函数。。
    playerReadied(player) {
      console.log("example player 1 readied", player);
    },
  }
}
</script>
<style lang="less" scoped>
body {
  position: relative;
  min-height: 100vh;
  margin: 0;
}
.videoWrap{
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  //height: 100%;
  video{
    height: 100%; /* 使视频高度充满父元素 */
    object-fit: fill; // 消除播放器两边的黑色留白 （object-fit 知识详见下文拓展部分）
  }
  /* 视频开始的按钮样式 */
  .video-js .vjs-big-play-button{
    border-width: 3px;
    border-color: rgb(255, 255, 255);
    border-style: solid;
    border-radius: 50%;
    width: 56px;
    height: 56px;
    line-height: 50px;
    background-color: rgba(0,0,0,0);
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
  }
  /* 滚动条的样式 */
  .video-js .vjs-control-bar{
    background-color: rgba(43, 51, 63, 0);
  }
  .video-js .vjs-play-progress{
    background-color: rgb(238,191,0);
  }
  .video-js .vjs-load-progress div{
    background-color: rgb(255, 255, 255);
  }
}
</style>
