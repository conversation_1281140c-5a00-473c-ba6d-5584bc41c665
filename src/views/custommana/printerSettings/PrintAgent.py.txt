import datetime
import subprocess
import sys
import os
import logging
from flask import Flask, request, jsonify
from flask_cors import CORS
from PyPDF2 import PdfWriter, PdfReader
import win32print
import tempfile
import time
# 初始化 Flask 应用
api = Flask(__name__)
CORS(api, resources={r"/*": {"origins": "*"}})

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 获取所有可用打印机列表
def get_printers():
    printers = []
    try:
        # 获取所有打印机名称
        printers = win32print.EnumPrinters(win32print.PRINTER_ENUM_LOCAL | win32print.PRINTER_ENUM_CONNECTIONS)
        # 只返回打印机名称
        printer_names = [printer[2] for printer in printers]
    except Exception as e:
        logger.error(f"Error fetching printers: {str(e)}")
        printer_names = []

    return printer_names

# 获取所有打印机的API
@api.route('/printers', methods=['GET'])
def get_printers_api():
    printers = get_printers()
    if printers:
        return jsonify({"code": 0, "data": printers}), 200
    else:
        return jsonify({"code": 1, "res": "No printers found."}), 500

# 打印PDF的API
@api.route('/print', methods=['POST'])
def pdf_printer():
    if request.method == 'POST':
        # 获取上传的 PDF 文件
        pdf_file = request.files.get("file")
        if not pdf_file:
            return {"code": 1, "res": "No file uploaded!"}, 400

        # 验证文件类型
        if not pdf_file.filename.lower().endswith('.pdf'):
            return {"code": 1, "res": "Uploaded file is not a PDF!"}, 400

        # 获取传递的打印机名称参数（如果没有提供，使用默认打印机）
        printer_name = request.args.get('printer', default="Microsoft Print to PDF", type=str)
        logger.info(f"printer_name {len(printer_name)} printer_name.")

        # 获取打印份数（默认为 1）
        copies = request.args.get('copies', default=1, type=int)

        # 生成文件名
        dt = datetime.datetime.now().strftime('%Y-%m-%d %H-%M-%S')
        filename = f"arjs-pdf-{dt}.pdf"

        # 读取和写入 PDF 文件
        pdf_writer = PdfWriter()
        pdf_reader = PdfReader(pdf_file)

        for page in range(len(pdf_reader.pages)):
            pdf_writer.add_page(pdf_reader.pages[page])

        # 保存新生成的 PDF 文件
        output_pdf_path = os.path.join(os.getcwd(), filename)
        with open(output_pdf_path, 'wb') as output_pdf:
            pdf_writer.write(output_pdf)

        # 获取当前工作目录路径
        app_path = os.path.dirname(__file__)
        pdf_to_printer_path = os.path.join(app_path, 'PDFtoPrinter.exe')
        logger.info(f"PDF to Printer path: {pdf_to_printer_path}")
        logger.info(f"Output PDF path: {output_pdf_path}")

        # 打印
        if sys.platform == 'win32':
            try:
                # 循环打印指定的份数
                for _ in range(copies):
                    args = [
                        pdf_to_printer_path,  # 确保 PDFtoPrinter.exe 路径正确
                        output_pdf_path,      # 输入 PDF 文件路径
                        printer_name          # 打印机名称
                    ]
                    logger.info(f"Executing print command with args: {args}")
                    subprocess.run(args, encoding="utf-8", shell=True, check=True)

                # 返回打印成功的响应
                res = {"code": 0, "res": f"Printed {copies} copies to {printer_name} successfully."}
            except subprocess.CalledProcessError as e:
                logger.error(f"Error printing to {printer_name}: {str(e)}")
                res = {"code": 1, "res": f"Error printing to {printer_name}: {str(e)}"}
        else:
            res = {"code": 1, "res": "Printing is supported only on Windows."}

        # 删除临时生成的文件
        if os.path.exists(output_pdf_path):
            os.remove(output_pdf_path)

        return res
    else:
        return {"code": 1, "res": "Only POST method is allowed!"}, 405

# 转换二维数组为字节数据
def get_print_bytes(number_array) -> bytearray:
    byte_list = bytearray()  # 创建一个空的字节数组
    for int_array in number_array:
        for num in int_array:
            byte_list.append(num & 0xFF)  # 将整数转换为字节，并添加到字节数组中
    return bytes(byte_list)  # 返回不可变的字节数组

# 打印方法，将二维数组打印到指定打印机
def print_raw_bytes(number_array, printer_name):
    try:
        # 获取打印机句柄
        printer_handle = win32print.OpenPrinter(printer_name)

        # 检查是否成功获取句柄
        if not printer_handle:
            raise RuntimeError(f"Failed to open printer: {printer_name}")

        print_bytes = get_print_bytes(number_array)

        # 启动打印作业，RAW 数据打印
        doc_info = ("Raw Byte Data Print Job", None, "RAW")
        job_handle = win32print.StartDocPrinter(printer_handle, 1, doc_info)
         # 启动页面
        win32print.StartPagePrinter(printer_handle)

        # 写入字节数据
        win32print.WritePrinter(printer_handle, print_bytes)

        # 结束页面和打印作业
        win32print.EndPagePrinter(printer_handle)
        win32print.EndDocPrinter(printer_handle)
        print("Raw byte data sent to printer successfully!")
    except Exception as e:
        logging.error(f"Error during printing: {e}")
        raise RuntimeError(f"Printing failed: {e}")

    finally:
        if 'printer_handle' in locals():
            win32print.ClosePrinter(printer_handle)

# 打印指令 API
@api.route('/printInstructions', methods=['POST'])
def print_instructions():
    try:
        # 获取请求数据
        print_request = request.get_json()
        number_array = print_request.get("numberArray", [])
        printer_name = print_request.get("printerName", "Microsoft Print to PDF")
        copies = print_request.get("copies", 1)

        # 验证打印数据
        if not isinstance(number_array, list) or not all(isinstance(i, list) for i in number_array):
            return jsonify({"code": 1, "res": "Invalid numberArray format."}), 400

        if copies < 1:
            return jsonify({"code": 1, "res": "Copies must be at least 1!"}), 400

        # 打印请求处理
        logger.info(f"Executing print command copies: {copies}")
        for _ in range(copies):
            print_raw_bytes(number_array, printer_name)
            time.sleep(0.5)
        return jsonify({"code": 0, "res": f"Printed {copies} copies successfully to {printer_name}."}), 200

    except Exception as e:
        logger.error(f"Error in printing instructions: {str(e)}")
        return jsonify({"code": 1, "res": f"Error: {str(e)}"}), 500

if __name__ == '__main__':
    # 启动 Flask 服务
    api.run(port=8899, debug=True, host='0.0.0.0')

# 编译命令 pyinstaller --onefile --add-data "E:\softwar\webmesenv\JustMake\pdfPrintAgent\PDFtoPrinter.exe;." PrintAgent.py
# 请求地址 http://localhost:8899/print?printer=Microsoft Print to PDF&copies=2