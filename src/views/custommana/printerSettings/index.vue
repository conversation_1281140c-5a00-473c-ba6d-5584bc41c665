<template>
  <div class="layout">
    <div style="display: flex;margin-top: 50px;">
      <el-form ref="form" label-position="left" label-width="auto" size="small" style="width: 100%;">
        <el-row :gutter="20">
          <el-col :span="24" :xs="24" style="height: 40px;">
            <el-form-item prop="connectString">
              <el-row>
                <el-col :span="6" :xs="24" style="margin-right: 30px">
                  <el-form-item :label="this.$t('arc.printSettings.name')">
                    <el-select v-model="name" size="small" class="selectInputWidth">
                      <el-option v-for="item in option" :key="item" :label="item" :value="item" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="4" :xs="24" style="margin-right: 30px">
                  <el-form-item :label="this.$t('arc.printSettings.lage')">
                    <el-select v-model="lage" size="small" class="selectInputWidth">
                      <el-option v-for="item in language" :key="item" :label="item" :value="item" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="4" :xs="24" style="margin-right: 30px">
                  <el-form-item :label="this.$t('arc.printSettings.dpi')">
                    <el-select v-model="dpi" size="small" class="selectInputWidth">
                      <el-option v-for="item in dpis" :key="item" :label="item" :value="item" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8" :xs="24">
                  <el-form-item>
                    <el-button  type="primary" @click="save()">{{ $t('public.save') }}</el-button>
                    <el-button  @click="downloadFile">{{ this.$t('arc.printSettings.btnDwn') }}</el-button>
                    <!-- 下载链接 -->
                    <a ref="downloadLink" style="display: none" :href="fileUrl"></a>
<!--                    <el-button @click="print2" style="margin-left: 20px">测试打印</el-button>-->
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form-item>
          </el-col>
        </el-row>

      </el-form>
    </div>
  </div>
</template>
<script>
export default {
  name: 'ivxAddress',
  data() {
    return {
      fileUrl: '', // 文件的URL
      name: '',
      lage:'',
      dpi:'',
      option:[],
      language:['tspl','cpcl','esc','zpl'],
      dpis:[203,300,600]
    }
  },
  mounted() {
    this.fileUrl =  `http://${window.location.host}/app/printAgent.zip`;
    //获取打印机列表
    this.name = localStorage.getItem("printer");
    this.dpi = localStorage.getItem("printerDpi");
    const s  = localStorage.getItem("printerLanguage");
    if (s !== "undefined") {
      this.lage = s;
    }
    fetch("http://localhost:8899/printers",{
      method: 'GET',
      mode: 'cors',
    }).then(res => res.json())  // 解析 JSON
      .then(data => {
        if (data.code === 0) {  // 如果返回的状态码为 0
          if (data.data != null) {
            this.option = data.data;  // 更新打印机列表
          }
        }else {
          this.$message.error(this.$t('arc.printSettings.tipsMsg0'));
        }
      })
      .catch(err => this.requestFailed(this.$t('arc.printSettings.tipsMsg1')));  // 错误处理
  },
  methods: {
    save(){
      if (this.name === undefined || this.name.length < 1 ) {
        this.$message.error(this.$t('arc.printSettings.tipsMsg2'));
        return;
      }
      if (this.lage === undefined || this.lage.length < 1) {
        this.$message.error(this.$t('arc.printSettings.tipsMsg3'));
        return;
      }
      if (this.dpi === undefined || this.dpi < 203) {
        this.dpi = 203;
      }
      localStorage.setItem('printer',this.name)
      localStorage.setItem('printerLanguage',this.lage)
      localStorage.setItem('printerDpi',this.dpi)
      this.$message.success(this.$t('public.success'))
    },
    downloadFile() {
      // 通过触发 <a> 标签的点击事件来启动下载
      this.$refs.downloadLink.click();
    },
    print1 () {
      fetch("/api/export/1864581434899959810|timeKey%3D1%26bill_type%3D1100%26condition%3Did%3A43?file=abc.pdf",
        {
          method: "GET",
        }
      ).then(res => {
        return res.blob()
      }).then(pdf => {
        if (pdf.type !== "application/pdf") {
          console.error("The fetched file is not a PDF.");
          return;
        }
        let formData = new FormData()
        formData.append("file", pdf, "print.pdf");
        // let printerName = "TSC TTP-244 Pro";
        // printerName = "Microsoft Print to PDF"
        fetch("http://localhost:8899/print?copies=2&printer=ZDesigner GT800 (EPL)", {
          method: 'POST',
          mode: 'cors',
          body: formData
        }).then(res => res.json())  // 解析 JSON
          .then(data => {
            if (data.code === 0) {  // 如果返回的状态码为 0
              this.$message.success("打印成功")
            }else {
              this.$message.error("打印失败");
            }
          })
          .catch(err => this.requestFailed("打印失败"));  // 错误处理
      })
    },
    print2() {
      // 1. 首先，获取二维数组（numberArray）
      // fetch("/api/printins/ExportReport/1864581434899959810|timeKey%3D1%26bill_type%3D1100%26condition%3Did%3A43/tspl", {
      fetch("/api/printins/ExportReport/1878686710846504962|timeKey%3D1%26bill_type%3D1100%26condition%3Did%3A43/zpl", {
        method: "GET",
      })
        .then(res => res.json())  // 假设接口返回 JSON 格式的二维数组
        .then(numberArray => {
          // 2. 创建要发送到打印接口的数据
          // let printerName = "TSC TTP-244 Pro"; // 打印机名称
          // let printerName = "ZDesigner GT800 (EPL)"; // 打印机名称
          let printerName = "Microsoft Print to PDF"; // 打印机名称
          let copies = 1; // 打印份数

          // 3. 构造请求体数据
          let requestData = {
            numberArray: numberArray,  // 直接传递二维数组，无需转换为字符串
            printerName: printerName,
            copies: copies
          };

          // 4. 向后端的打印接口发送请求
          fetch("http://localhost:8899/printInstructions", {
            method: "POST",
            headers: {
              "Content-Type": "application/json"  // 请求体为 JSON 格式
            },
            body: JSON.stringify(requestData)  // 发送 JSON 格式的数据
          })
            .then(res => res.json())  // 解析返回的 JSON
            .then(data => {
              if (data.code === 0) {  // 如果打印成功，code 为 0
                this.$message.success("打印成功");
              } else {
                this.$message.error("打印失败");
              }
            })
            .catch(err => {
              console.error(err);
              this.$message.error("打印失败");
            });
        })
        .catch(err => {
          console.error(err);
          this.$message.error("获取报表失败");
        });
    },
  }
}
</script>
<style lang="scss">
.selectInputWidth {
  width: 100%;
}
</style>
