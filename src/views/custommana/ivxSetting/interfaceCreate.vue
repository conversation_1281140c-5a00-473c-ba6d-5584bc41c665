<template>
  <el-dialog :title="title" style="padding: 0px;" width="540px" :before-close="cancelEidt" :close-on-click-modal="false"
             :visible.sync="createVisible"
             top="25vh"
  >
    <div style="center:'center';margin-top: 15px;">
      <el-form inline-message label-width="85px" :model="entity" :rules="rules"
               ref="entity" style="margin: 0px;padding: 0px;">
        <el-form-item :label="$t('ivx.type')" prop="type">
          <el-select v-model="entity.type" size="medium" style="width: 100%" placeholder="">
            <el-option v-for="item in interfaceType" :key="item.value" :label="$t(item.label)" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('ivx.address')" prop="address">
          <el-input prefix-icon="el-icon-edit" v-model="entity.address" size="medium" style="width: 100%"
                    placeholder=""></el-input>
        </el-form-item>
        <el-form-item :label="$t('ivx.rem')" prop="rem">
          <el-input prefix-icon="el-icon-edit" v-model="entity.rem" size="medium" style="width: 100%"
                    placeholder=""></el-input>
        </el-form-item>
      </el-form>
    </div>
    <div slot="footer" class="dialog-footer">
      <div class="footer-buttons">
        <el-button size="mini" type="primary" @click="addEntity()">{{ $t('public.sure') }}</el-button>
      </div>
    </div>
  </el-dialog>
</template>
<script>
import Vue from 'vue';
import { saveAddress, getAddress} from '@/api/ivx'
export default {
  name: 'CreateIvxInterface',
  data() {
    return {
      createVisible: false,
      title: '',
      isSave:'',
      entity: {
        type: '',
        address : '',
        rem: '',
      },
      interfaceType: [
        { label: 'ivx.interfaceCreateVue.interfaceType1', value: 1 },
        { label: 'ivx.interfaceCreateVue.interfaceType2', value: 2 },
        { label: 'ivx.interfaceCreateVue.interfaceType3', value: 3 },
      ],
      rules: {
        type: [{ required: true, trigger: 'blur', message: this.$t('ivx.interfaceCreateVue.rulesMsg1')}],
        address: [{ required: true, trigger: 'blur', message: this.$t('ivx.interfaceCreateVue.rulesMsg2') }],
      },
    }
  },
  mounted() {
  },
  created() {
  },
  methods: {
    addEntity() {
      this.isSubmit = true
      const _that = this
      this.$refs.entity.validate((valid) => {
        if (valid){
          if(this.isSave){
            getAddress(this.entity.type).then(res => {
              if(res.code == 0){
                if (res.data != null){
                  this.$message.error(this.$t('ivx.interfaceCreateVue.errorMsg'))
                }else {
                  saveAddress(this.entity).then(res => {
                    if(res.code == 0){
                      this.$message.success(this.$t('public.success'))
                    }else{
                      this.$message.error(this.$t('public.err'))
                    }
                  }).catch(err => this.requestFailed(err))
                }
              }
            }).catch(err => {
              console.error(err)
              this.requestFailed(err)
            })
          }else {
            saveAddress(this.entity).then(res => {
              if(res.code == 0){
                this.$message.success(this.$t('public.success'))
              }else{
                this.$message.error(this.$t('public.error'))
              }
            }).catch(err => this.requestFailed(err))
          }
        }
      })
    },
    cancelEidt() {
      this.pagedisable = false
      this.createVisible = false
    },
    async setCreateVisible(val,flge,row) {
      if (flge){
        this.title = this.$t('ivx.interfaceCreateVue.titleSave')
      }else {
        this.title = this.$t('ivx.interfaceCreateVue.titleEdit')
      }
      this.createVisible =val
      this.isSave = flge
      this.entity = row
    },
  }
}
</script>
<style lang='scss' scoped>
.dialog-footer {
  padding: 10px 10px;
}

::v-deep .el-dialog {
  border-radius: 10px;
}

::v-deep .el-dialog__title {
  border-radius: 10px;
  line-height: 30px;
  font-size: 14px;
  color: #303133;
}

::v-deep .el-dialog__header {
  padding: 2px 10px;
  height: 30px;
  border-radius: 10px 10px 0 0;
  background-color: #dce6e8 !important;
  color: #ffffff !important;
}

::v-deep .el-dialog__body {
  padding: 0px 10px;
}

::v-deep .el-dialog__footer {
  padding: 0px;
}

::v-deep .el-dialog__headerbtn {
  position: absolute;
  top: 5px;
  right: 20px;
  padding: 0;
  background: 0 0;
  border: none;
  outline: 0;
  cursor: pointer;
  font-size: 16px;
}

::v-deep .el-form-item {
  margin-bottom: 10px;
}
</style>