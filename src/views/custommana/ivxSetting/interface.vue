<template>
  <div class="layout">
    <vTable ref="vTable" v-bind="vTableProps" @toolbarClick="handleToolbarClick" @formItemIconClick="handleIconClick"
            @formDataChange="handleFormDataChange" @cellDblclick="handleDblclick" :print-type="0">
    </vTable>
    <CreateIvxInterface ref="createInterfaceRef"></CreateIvxInterface>
  </div>
</template>

<script>
import { fetchList } from '@/api/ivx'

import vTable from '@/components/amtxts/vTable/vGrid.vue'
import CreateIvxInterface from '@/views/custommana/ivxSetting/interfaceCreate.vue'
export default {
  name: 'ivxInterfaceList',
  components: {
    CreateIvxInterface,
    vTable
  },
  data() {
    return {
      vTableProps: {
        delete_key: 'id',
        array_key: true,
        api_find: fetchList,
        toolbarItems: [
        ],
        formDataRaw: [
          { field: 'ivx.type', type: 'input' },
          { field: 'ivx.address', type: 'input' },
          { field: 'ivx.rem', type: 'input' },
        ],
        tableColumn: [
          { field: "type", title: 'ivx.type', },
          { field: "address", title: 'ivx.address', },
          { field: 'rem', title: 'ivx.rem', },
        ],

      }
    }
  },
  methods: {
    handleToolbarClick(params) {
      switch (params.code) {
        case 'create':
          this.$refs.createInterfaceRef.setCreateVisible(true,true, { type: 1 });
          break;
        default:
      }
    },
    handleIconClick(params) {
    },
    handleFormDataChange(params) {
    },
    handleChoose(params) {
    },
    handleSelectListEvent(params) {
    },
    handleDblclick(param) {
      this.$refs.createInterfaceRef.setCreateVisible(true,false,param);
    },
    handleSubmit(){
      this.$refs.vTable.handleGet();
    }
  },
}
</script>
<style lang="less">
</style>
