<template>
  <div class="layout">
    <div style="display: flex;margin-top: 50px;">
      <el-form ref="form" label-position="left" label-width="auto" size="small" style="width: 100%;" :rules="formRules" :model="formData">
        <el-row :gutter="20">
          <el-col :span="24" :xs="24" style="height: 40px;">
            <el-form-item prop="connectString">
              <el-row>
                <el-col :span="4" :xs="24" style="margin-right: 30px">
                  <el-form-item :label="$t('ivx.userSettingVue.userName')"  prop="userName">
                    <el-input v-model="formData.userName"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="4" :xs="24" style="margin-right: 30px">
                  <el-form-item :label="$t('ivx.userSettingVue.password')"  prop="password">
                    <el-input v-model="formData.password"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="4" :xs="24" style="margin-right: 30px">
                  <el-form-item style="display: flex; align-items: center;">
                    <template #label>
                      <div style="display: flex; align-items: center;width: 70px;">
                        <span style="margin-right: 5px;">{{ $t('ivx.userSettingVue.secretKey') }}</span>
                        <el-tooltip class="item" effect="dark" :content="$t('ivx.userSettingVue.content')" placement="top">
                          <i class="el-icon-question" style="font-size: 16px;"></i>
                        </el-tooltip>
                      </div>
                    </template>
                    <el-input v-model="formData.secretKey"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="2" :xs="24">
                  <el-form-item>
                    <el-button  type="primary" @click="save()">{{ $t('public.save') }}</el-button>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form-item>
          </el-col>
        </el-row>

      </el-form>
    </div>
  </div>
</template>
<script>
import { saveIvxUser, getIvxUser } from '@/api/ivx'
export default {
  name: 'ivxAddress',
  data() {
    return {
      formData: {
        userName: '',
        password: '',
        secretKey: '',
      },
      formRules: {
        userName: [
          { required: true, message: this.$t('ivx.userSettingVue.rulesMsg1'), trigger: 'blur' },
        ],
        password: [
          { required: true, message: this.$t('ivx.userSettingVue.rulesMsg2'), trigger: 'blur' },
        ],
        secretKey: [
          { required: true, message: this.$t('ivx.userSettingVue.rulesMsg3'), trigger: 'blur' },
        ]
      }
    }
  },
  watch: {

  },
  mounted() {
    getIvxUser().then(res => {
      if(res.code == 0){
        if (res.data != null){
          this.formData = res.data
        }
      }
    }).catch(err => this.requestFailed(err))
  },
  methods: {
    save(){
      this.$refs.form.validate(valid => {
        if (valid) {
          saveIvxUser(this.formData).then(res => {
            if(res.code == 0){
              this.$message.success(this.$t('public.success'))
            }else{
              this.$message.error(this.$t('ivx.userSettingVue.errorMsg'))
            }
          }).catch(err => this.requestFailed(err))
        } else {
          // 表单验证失败
          console.log('表单验证失败');
        }
      });
    }
  }
}
</script>
<style lang="scss">
.el-tooltip {
  z-index: 9999 !important;
}

</style>
