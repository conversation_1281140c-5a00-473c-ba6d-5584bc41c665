<template>
  <div class="layout">
    <div style="display: flex;margin-top: 50px;">
      <el-form ref="form" label-position="left" label-width="auto" size="small" style="width: 100%;" :rules="formRules" :model="formData">
        <el-row :gutter="20">
          <el-col :span="24" :xs="24" style="height: 40px;">
            <el-form-item prop="connectString">
              <el-row>
                <el-col :span="8" :xs="24" style="margin-right: 30px">
                  <el-form-item :label="$t('ivx.addressVue.address')"  prop="address">
                    <el-input v-model="formData.address"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8" :xs="24">
                  <el-form-item>
                    <el-button  @click="validateForm()">{{ $t('ivx.addressVue.test') }}</el-button>
                    <el-button  type="primary" @click="save()">{{ $t('ivx.addressVue.save') }}</el-button>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form-item>
          </el-col>
        </el-row>

      </el-form>
    </div>
  </div>
</template>
<script>
import { saveAddress, testAddress, getAddress } from '@/api/ivx'
export default {
  name: 'ivxAddress',
  data() {
    return {
      formData: {
        type: 0,
        address: '',
        tenantId: '',
        projectAddr: '0'
      },
      formRules: {
        address: [
          { required: true, message: this.$t('ivx.addressVue.addressTestFail'), trigger: 'blur' },
        ]
      }
    }
  },
  watch: {

  },
  mounted() {
    getAddress(this.formData.type).then(res => {
      if(res.code == 0){
        if (res.data != null){
          this.formData = res.data
        }
      }
    }).catch(err => this.requestFailed(err))
  },
  methods: {
    validateForm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          // 表单验证通过，可以进行提交等操作
          testAddress({ address:this.formData.address }).then(res => {
            if(res.code == 0){
              this.$message.success(this.$t('ivx.addressVue.addressTestSuccess'))
            }else{
              this.$message.error(this.$t('ivx.addressVue.addressTestFail'))
            }
          })
        } else {
          // 表单验证失败
          console.log('表单验证失败');
        }
      });
    },
    save(){
      saveAddress(this.formData).then(res => {
        if(res.code == 0){
          this.$message.success(this.$t('public.success'))
        }else{
          this.$message.error(this.$t('public.error'))
        }
      }).catch(err => this.requestFailed(err))
    }
  }
}
</script>
<style lang="scss">

</style>
