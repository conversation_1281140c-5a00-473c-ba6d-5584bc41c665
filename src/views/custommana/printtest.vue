<template>
  <div class="consrm">
    <div>
      <a-button type="primary" @click="print">打印</a-button>
    </div>
    <report-print-dialog ref="printDialog" />
  </div>
</template>

<script>
  import printDialog from '@/components/printDialog/index'
  import ReportPrintDialog from '@/components/printDialog/ReportPrintDialog.vue'
  export default {
    name: 'printtest',
    components: {
      ReportPrintDialog,
      printDialog
    },
    data() {
      return {
        timeKey: '',
        tenantId: ''

      }
    },
    computed: {

    },
    created() {
      let tenant = this.$ls.get('SET_TENANT')
      this.tenantId = this.$store.state.user.userInfo.tenantId
    },
    mounted() {

    },
    updated() {
    },
    destroyed() {

    },
    methods: {
      print() {
        this.timeKey = new Date().getTime()
        console.log(this.timeKey, 'fffffffffff')
        let printData = { 'timeKey': this.timeKey, bill_type: 'statement', tenantId: this.tenantId, }
        this.$refs.printDialog.create(printData)
      },

    }
  }
</script>

<style lang="less" scoped>
  .project-list {
    .card-title {
      font-size: 0;

      a {
        color: rgba(0, 0, 0, 0.85);
        margin-left: 12px;
        line-height: 24px;
        height: 24px;
        display: inline-block;
        vertical-align: top;
        font-size: 14px;

        &:hover {
          color: #1890ff;
        }
      }
    }

    .card-description {
      color: rgba(0, 0, 0, 0.45);
      height: 44px;
      line-height: 22px;
      overflow: hidden;
    }

    .project-item {
      display: flex;
      margin-top: 8px;
      overflow: hidden;
      font-size: 12px;
      height: 20px;
      line-height: 20px;

      a {
        color: rgba(0, 0, 0, 0.45);
        display: inline-block;
        flex: 1 1 0;

        &:hover {
          color: #1890ff;
        }
      }

      .datetime {
        color: rgba(0, 0, 0, 0.25);
        flex: 0 0 auto;
        float: right;
      }
    }

    .ant-card-meta-description {
      color: rgba(0, 0, 0, 0.45);
      height: 44px;
      line-height: 22px;
      overflow: hidden;
    }
  }

  .item-group {
    padding: 20px 0 8px 24px;
    font-size: 0;

    a {
      color: rgba(0, 0, 0, 0.65);
      display: inline-block;
      font-size: 14px;
      margin-bottom: 13px;
      width: 25%;
    }
  }

  .members {
    a {
      display: block;
      margin: 12px 0;
      line-height: 24px;
      height: 24px;

      .member {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
        line-height: 24px;
        max-width: 100px;
        vertical-align: top;
        margin-left: 12px;
        transition: all 0.3s;
        display: inline-block;
      }

      &:hover {
        span {
          color: #1890ff;
        }
      }
    }
  }

  .mobile {
    .project-list {
      .project-card-grid {
        width: 100%;
      }
    }

    .more-info {
      border: 0;
      padding-top: 16px;
      margin: 16px 0 16px;
    }

    .headerContent .title .welcome-text {
      display: none;
    }
  }
</style>