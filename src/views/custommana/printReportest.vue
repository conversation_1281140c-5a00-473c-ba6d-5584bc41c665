<template>
  <div class="layout">
    <vToolbar :data="toolbarItems" @toolbarClick="handleToolbarClick"></vToolbar>
    <div style="margin: 20px 0">
      <el-form ref="ruleForm" label-position="left" label-width="auto" size="small" :model="ruleForm">
        <el-row :gutter="20">
          <el-col :span="24" :xs="24">
            <el-form-item prop="connectString" label="制单日期">
              <el-row>
                <el-col :span="4" :xs="24" class="find">
                  <el-form-item>
                    <el-date-picker
                      v-model="ruleForm.moDd"
                      align="right"
                      type="date"
                      placeholder="制单日期"
                      format="yyyy 年 MM 月 dd 日"
                      value-format="yyyy-MM-dd"
                      >
                    </el-date-picker>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div>
      <vxe-grid ref="gridRef" border stripe resizable show-overflow show-header-overflow
                :loading="loading" :columns="tableColumn" :data="tableData" :custom-config="{ mode: 'popup' }"
                :radio-config="{ trigger: 'row', highlight: true }" @cell-dblclick="handleDblclick"  @radio-change="radioChangeEvent">
      </vxe-grid>
    </div>
    <report-print-dialog ref="printDialog" />
  </div>
</template>
<script>
import vTable from '@/components/amtxts/vTable/vGrid.vue'
import { getMFMO,savePrintData } from '@/api/interfaceList'
import { getMneuByPath } from '@/api/admin/menu'
import vToolbar from '@/components/amtxts/vTable/vToolbar.vue'
import ReportPrintDialog from '@/components/printDialog/ReportPrintDialog.vue'
export default {
  name: 'ReportPrint',
  components: {
    vToolbar,
    vTable,ReportPrintDialog
  },
  data() {
    return {
      selectRow: null,
      tableHeight: '800px',
      loading:false,
      ruleForm: {
        moDd: '2020-01-01',
      },
      path: '',
      tableData: [],
      tablePage:{
        layouts: ['Total', 'Sizes', 'PrevPage', 'JumpNumber', 'NextPage', 'FullJump'],
        total: 0,
        currentPage: 1,
        pageSize: 20
      },
      toolbarItems: [
        { label: '查找', value: 'find' },
        { label: '打印', value: 'printed' },
      ],
      tableColumn: [
        { title: "选择",type:"radio", width:"60"},
        { field: "moNo", title: "制令单号", },
        { field: "moDd", title: "制单日期", },
        { field: "staDd", title: "预开工日", },
        { field: 'mrpNo', title: '预完工日', },
        { field: 'supName', title: '制造成品', },
        { field: 'prdMark', title: '货品特征', },
        { field: 'wh', title: '库位', },
        { field: 'unit', title: '单位', },
        { field: 'qty', title: '数量', },
        { field: 'dep', title: '部门代号', },
        { field: 'usr', title: '制单人', },
        { field: 'batNo', title: '批号', },
        { field: 'closeId', title: '结案', },
      ],
    }
  },
  mounted() {
    this.path = this.$route.path
    console.log(this.path)
  },
  methods: {
    handleToolbarClick(params) {
      switch (params) {
        case 'find':
          this.getMoNo();
          break;
        case 'printed':
          this.printed();
          break;
        default:
      }
    },
    radioChangeEvent ({ row }) {
      this.selectRow = row
    },
    handleIconClick(params) {

    },
    handleFormDataChange(params) {
    },
    handleChoose(params) {
    },
    handleSelectListEvent(params) {
    },
    handleDblclick(param) {
      this.$refs.createRef.handleEdit(param);
    },
    handleSubmit(){
      this.$refs.vTable.handleGet();
    },
    handlePageChange() {

    },
    getMoNo() {
      getMFMO({moDd: this.ruleForm.moDd}).then(res => {
        this.tableData = res.data;
      })
    },
    printed() {
      let _that = this;
      //根据路由查找菜单ID
      let menuId,menuName
      getMneuByPath({ path:  _that.path }).then(res => {
        menuId = '1100'
        menuName ='测试报表'
        savePrintData({
          printData:this.selectRow,
          billType: menuId
        }).then(res => {
          console.log(res)
          let printData = {printType:2,menuId: menuId,menuName: menuName};
          this.$refs.printDialog.create(printData)
        })
      }).catch(err => {
        _that.requestFailed(err)
      })
    }
  }

}
</script>

