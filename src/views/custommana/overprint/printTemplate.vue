<template>
  <div class="layout">
    <div style="display: flex;margin-top: 15px;margin-bottom: 15px">
      <div class="title">{{ $t('arc.overprint.printTemplate.title') }}</div>
      <el-form ref="ruleForm" label-position="left" label-width="auto" size="small" :model="ruleForm">
        <el-row :gutter="20">
          <el-col :span="24" :xs="24" style="height: 40px;">
            <el-form-item prop="connectString">
              <el-row>
                <el-col :span="4" :xs="24" class="find">
                  <el-select v-model="ruleForm.menuId" placeholder="">
                    <el-option v-for="item in nameOptions" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="4" :xs="24" class="find">
                  <el-select v-model="ruleForm.menuIdFind" placeholder="">
                    <el-option v-for="item in conditionOptions" :key="item.value" :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="8" :xs="24" class="find" style="margin-right: 30px">
                  <el-form-item>
                    <el-input v-model="ruleForm.id"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="4" :xs="24" class="find">
                  <el-form-item>
                    <el-button icon="el-icon-search" type="primary" @click="findList()">{{ $t('public.query') }}</el-button>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form-item>
          </el-col>
          <el-col :span="24" :xs="24">
            <el-form-item prop="connectString">
              <el-row>
                <el-col :span="4" :xs="24" class="find">
                  <el-select v-model="ruleForm.menuName" placeholder="">
                    <el-option v-for="item in nameOptions" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="4" :xs="24" class="find">
                  <el-select v-model="ruleForm.menuNameFind" placeholder="">
                    <el-option v-for="item in conditionOptions" :key="item.value" :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="8" :xs="24" class="find">
                  <el-form-item>
                    <el-input v-model="ruleForm.name"></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form-item>
          </el-col>
        </el-row>

      </el-form>
    </div>
    <div class="">
      <vxe-grid ref="gridRef" border stripe resizable show-overflow show-header-overflow :height="tableHeight"
        :loading="loading" :columns="tableColumn" :data="tableData" :custom-config="{ mode: 'popup' }"
        :radio-config="{ trigger: 'row', highlight: true }" :pager-config="tablePage" @page-change="handlePageChange"
        @cell-dblclick="handleDblclick">
        <!-- 操作栏位的按钮模板 -->
        <template v-slot:actions="scope">
          <el-button @click="handleSelectClick(scope.row)"
            style="color: #14A380;border-color: rgb(185, 227, 217);background-color: rgb(232, 246, 242);" size="small">
            {{ $t('arc.overprint.printTemplate.select') }}
          </el-button>
        </template>
      </vxe-grid>
    </div>
  </div>
</template>
<script>

import { getTypeList } from '@/api/interfaceList'
import Vue from 'vue';
export default {
  name: 'PrintTemplate',
  props: {
    fromDataset: {
      type: Object,
      default: () => { }
    },
    isEdit: Boolean
  },
  data() {
    return {
      loading: false,
      ruleForm: {
        id: '',
        name: '',
        menuId: '',
        menuName: '',
        menuIdFind: '',
        menuNameFind: '',
      },
      nameOptions: [
        {
          value: 'menu_id',
          label: this.$t('arc.overprint.printTemplate.menuLabel'),
        }, {
          value: 'name',
          label: this.$t('arc.overprint.printTemplate.menuLabel1')
        }
      ],
      conditionOptions: [
        {
          value: 0,
          label: this.$t('arc.overprint.printTemplate.conditionLabel')
        }, {
          value: 1,
          label: this.$t('arc.overprint.printTemplate.conditionLabel1')
        }
      ],
      tablePage: {
        layouts: ['Total', 'Sizes', 'PrevPage', 'JumpNumber', 'NextPage', 'FullJump'],
        total: 0,
        currentPage: 1,
        pageSize: 20
      },
      tableData: [],
      tableHeight: '480px',
      tableColumn: [
        {
          field: 'is_default',
          title: this.$t('arc.overprint.printTemplate.table.title'),
          align: "center",
          width: 90,
          slots: {
            default: 'actions' // 指定自定义插槽
          }
        },
        { type: "seq", width: '60', title: this.$t('arc.overprint.printTemplate.table.item'), fixed: "center", align: "center", },
        { field: "bill_type", title: this.$t('arc.overprint.printTemplate.table.menuNo'), },
        { field: "label", title: this.$t('arc.overprint.printTemplate.table.name'), },
      ],
    }
  },
  watch: {
    isEdit(newVal) {
      this.isEdit = newVal;
      this.edittwo = newVal;
    },
    fromDataset(newVal) {
      this.form = newVal
      this.getList()
    }
  },
  mounted() {
    Vue.set(this.ruleForm, 'menuId', 'menu_id');
    Vue.set(this.ruleForm, 'menuName', 'name');
    Vue.set(this.ruleForm, 'menuIdFind', 1);
    Vue.set(this.ruleForm, 'menuNameFind', 1);
    this.findList()
  },
  methods: {
    setForm() {
      this.form = {}
      this.getList()
    },
    findList() {
      if (this.ruleForm.menuId === 'menu_id' ) {
        if(this.ruleForm.id.length > 0 && isNaN(this.ruleForm.id)) {
          this.$message.error(this.$t('arc.overprint.printTemplate.idError'))
          return
        }
      }
      if (this.ruleForm.menuName === 'menu_id' ) {
        if(this.ruleForm.name.length > 0 && isNaN(this.ruleForm.name)) {
          this.$message.error(this.$t('arc.overprint.printTemplate.idError'))
          return
        }
      }
      let query = {
        current: this.tablePage.currentPage,
        size: this.tablePage.pageSize,
        bill_type: this.ruleForm.id,
        label: this.ruleForm.name,
        bill_type_condition: this.ruleForm.menuIdFind,
        label_condition: this.ruleForm.menuNameFind
      }
      if (this.ruleForm.menuId == 'name' && this.ruleForm.id != '') {//用户切换查询字段
        query = {
          current: this.tablePage.currentPage,
          size: this.tablePage.pageSize,
          label: this.ruleForm.id,
          label_condition: this.ruleForm.menuIdFind
        }
      } else if (this.ruleForm.menuName == 'menu_id' && this.ruleForm.id != '') {
        query = {
          current: this.tablePage.currentPage,
          size: this.tablePage.pageSize,
          bill_type: this.ruleForm.name,
          bill_type_condition: this.ruleForm.menuNameFind
        }
      }
      getTypeList(query).then(res => {
        this.tableData = res.data.records
        this.tablePage.total = res.data.total
        this.tablePage.currentPage = res.data.current
      }).catch(err => {
        this.requestFailed(err)
      })
    },
    handleSelectClick(param) {
      this.$emit('selectId', param)
    },
    handleDblclick({ row }) {
      this.handleSelectClick(row)
    },
    handlePageChange(obj) {
      this.tablePage.currentPage = obj.currentPage;
      this.findList()
    }
  }
}
</script>
<style lang="scss" scoped>
.layout {
  //height: calc(100vh - 300px);
}

.title {
  font-weight: bold;
  width: 120px;
  padding-top: 8px;
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.find {
  margin-left: 10px;
}

::v-deep .el-form-item {
  margin-bottom: 0px;
}
</style>
