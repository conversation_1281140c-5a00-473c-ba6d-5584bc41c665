<template>
  <el-container>
    <el-header>
      <vToolbar :data="toolbarItems" @toolbarClick="handleToolbarClick"></vToolbar>
    </el-header>
    <el-container>
      <el-aside>
        <el-input v-model="search" size="small" style="width: 100%;margin-bottom: 10px;" @keydown.native="handleKeydown" :placeholder="$t('arc.overprint.placeholder')" >
          <i slot="suffix" class="el-input__icon el-icon-search" @click="handleIconClick"></i>
        </el-input>
        <el-tree ref="tree" node-key="bill_type" :default-expand-all="true" :expand-on-click-node="false"
                 :data="treeData" :props="defaultProps" :highlight-current="true" :default-expanded-keys="expandedKeys"
                 @node-click="handleNodeClick" :current-node-key="current_node_key">
            <span :class="{ 'custom-tree-node': true }" slot-scope="{node, data}" :ref="node.data.id">

              <span class="enumText" style="margin-right:5px;">{{ data.label }}</span>
              <span>
                <el-button type="text" class="el-icon-circle-plus-outline" size="mini"
                           v-if="node.childNodes.length == 0" @click.stop="appendTreeNode(data, node)">
                </el-button>
              </span>
            </span>

        </el-tree>
      </el-aside>
      <main style="height: 100%;overflow: auto">
        <el-table v-loading="loading" v-if="printtable" :data="tableData" height="100%"
                  :header-cell-style="{ backgroundColor: '#F4F5F9', fontWeight: '400', verticalAlign: 'top' }"
                  highlight-current-row style="width: 100%;" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="60" align="center" />

          <el-table-column :label="$t('arc.overprint.table.menuName')" prop="bill_type_name" />
          <el-table-column :label="$t('arc.overprint.table.templateNO')" prop="code" />
          <el-table-column :label="$t('arc.overprint.table.templateName')" prop="name" />
          <el-table-column :label="$t('arc.overprint.table.default')" prop="is_default" align="center">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.is_default === true">{{ $t('arc.overprint.table.Y') }}</el-tag>
              <el-tag v-if="scope.row.is_default === false">{{ $t('arc.overprint.table.N') }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column :label="$t('arc.overprint.table.operation')">
            <template v-slot="{ row }">
              <el-button type="text" @click="handledit(row)">{{ $t('public.edit') }}</el-button>
              <el-button type="text" @click="printedit(row)">{{ $t('arc.overprint.table.design') }}</el-button>
              <el-button type="text" @click="handleDel(row)">{{ $t('public.delete') }}</el-button>
              <div style="height: 23px;width: 100%;" />
            </template>
          </el-table-column>
        </el-table>
      </main>
      <div style="text-align: left">
        <CreateTemplate ref="createTemplateRef" @refresh="handleRefresh"></CreateTemplate>
      </div>
    </el-container>
  </el-container>
<!--  <div class="tab-container">-->
<!--    <vToolbar :data="toolbarItems" @toolbarClick="handleToolbarClick"></vToolbar>-->
<!--    <el-card shadow="never">-->
<!--      <el-row :gutter="16">-->


<!--      </el-row>-->

<!--    </el-card>-->
<!--  </div>-->
</template>

<script>
import {
  getTypeList,
  reportconPage,
  reportConDel,
} from '@/api/interfaceList'
import vToolbar from '@/components/amtxts/vTable/vToolbar.vue'
import CreateTemplate from '@/views/custommana/overprint/createTemplate.vue'
import MenuTree from '@/views/sys/menu/menuTree.vue'
export default {
  name: 'CustomerAssessment',
  components: { MenuTree, CreateTemplate, vToolbar },
  data() {
    return {
      search: '',
      isSubmit: false,
      tablePage: {
        currentPage: 1,
        pageSize: 1000,
        total: 0
      },
      treeData: [
        {
          parent_id:'',
          bill_type: 1,
          label: this.$t('arc.overprint.treeLabel'),
          children: []
        }],
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      tableData: [],
      printtable: 'number1',
      expandedKeys: [],
      current_node_key: 'ACCRPT',
      openId: '',
      multipleSelection: [],
      dataadd: {},
      loading: false,
      toolbarItems: [
        { label: 'arc.overprint.table.design', value: 'title' },
        { value: 'remove', visible: false },
        { value: 'query', visible: false },
        { value: 'custom', visible: false },
        // { label: 'public.add', value: 'crateSave' },
      ]
    }
  },
  created() {
    this.loading = true
    getTypeList({
      current: this.tablePage.currentPage,
      size: this.tablePage.pageSize,
    }).then(res => {
      this.treeData[0].children = res.data.records
      reportconPage(
        {
          bill_type: res.data.records[0].parent_id
        }
      ).then(res => {
        this.loading = false
        this.tableData = res.data.records
      }).catch(err => {
        this.loading = false
        this.tableData = []
        this.requestFailed(err)
      })
    }).catch(err => {
      this.requestFailed(err)
    })
  },
  methods: {
    init() {
      getTypeList({
        current: this.tablePage.currentPage,
        size: this.tablePage.pageSize,
        label: this.search,
        label_condition:1
      }).then(res => {
        this.treeData[0].children = res.data.records
        reportconPage(
          {
            bill_type: res.data.records[0].parent_id
          }
        ).then(res => {
          this.loading = false
          this.tableData = res.data.records
        }).catch(err => {
          this.loading = false
          this.tableData = []
          this.requestFailed(err)
        })
      }).catch(err => {
        this.requestFailed(err)
      })
    },
    handleKeydown(event) {
      if (event.key === 'Enter') {
        this.init()
      }
    },
    handleIconClick() {
      this.init()
    },
    printedit(row) {
      const username= this.$store.state.user.info.username
      //路由参数放缓存
      localStorage.setItem(username+"_bill_type",row.bill_type)
      localStorage.setItem(username+"_template_id",row.id)
      localStorage.setItem(username+"_refresh_print",true)//刷新页面标识
      this.$router.push({
        name: 'arcmodel',
        params: { id: row.id, name: row.name, reportId: row.id, bill_type: row.bill_type,subType:row.type },
        replace: true
      })
    },
    getList() {
      reportconPage(
        {
          bill_type: this.openId
        }
      )
        .then(res => {
          this.loading = false
          this.tableData = res.data.records
        })
        .catch(err => {

          this.loading = false
          this.tableData = []
          this.requestFailed(err)
        })
    },
    // 添加一个分类结点
    appendTreeNode(data, node) {
      let entity = {}
      entity.is_default = false
      entity.language_code = 'zh-hans'
      entity.bill_type = data.parent_id
      entity.bill_type_name = data.label
      this.$refs.createTemplateRef.setCreateVisible(true, entity);
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleDelete() {
      if (this.multipleSelection.length === 0) return this.$message.error(this.$t('arc.index.errorMsgSingleItem'))
      let idlist = []
      this.multipleSelection.forEach((i, index) => {
        idlist.push(i.id)
      })
      idlist = idlist.join(',')
      reportConDel({
        id: idlist
      })
        .then(res => {
          this.$message.success(this.$t('public.success'))
          this.getList()
          this.dialogVisible = false
        })
        .catch(err => {
          this.requestFailed(err)
        })
    },
    // 添加
    addHandle() {
      this.$refs.createTemplateRef.setCreateVisible(true);
    },
    handledit(row) {
      this.$refs.createTemplateRef.setCreateVisible(true, JSON.parse(JSON.stringify(row)));
    },
    handleDel(row) {
      const that = this
      this.$confirm(this.$t('public.del.content'), this.$t('public.del.title'), {
        confirmButtonText: this.$t('public.sure'),
        cancelButtonText: this.$t('public.cancel'),
        type: 'warning'
      }).then(() => {
        reportConDel({ id: row.id }).then(res => {
          if (res) {
            this.$message.success({
              message: this.$t('public.success')
            })
            that.getList()
            that.dialogVisible = false
          }
        })
      })
    },
    handleNodeClick(e) {
      if (e) {
        this.openId = e.parent_id
        this.expandedKeys = [e.parent_id]
      }
      reportconPage(
        {
          bill_type: this.openId
        }
      ).then(res => {
        this.loading = false
        this.tableData = res.data.records
      }).catch(err => {
        this.loading = false
        this.tableData = []
        this.requestFailed(err)
      })
    },
    handleToolbarClick(params) {
      switch (params) {
        case 'create':
          this.addHandle();
          break
        default:
      }
    },
    handleRefresh(params){
      reportconPage(
        {
          bill_type: params.bill_type
        }
      ).then(res => {
        this.loading = false
        this.tableData = res.data.records
      }).catch(err => {
        this.loading = false
        this.tableData = []
        this.requestFailed(err)
      })
    }
  },
}
</script>

<style lang="less" scoped>
                   .el-container {
                     height: 100%;
                     overflow: hidden;
                   }

.el-header {
  padding: 0;
  height: auto !important;
}

::v-deep .el-main {
  padding: 17px 15px !important;
  background: #fff;
}

::v-deep .el-aside {
  height: 100%;
  width: 240px !important;
  font-weight: 600;
  padding: 17px 10px !important;
  color: #333;
  box-shadow: 2px 0 6px rgba(0, 21, 41, 0.01);
  border-top: 1px solid rgba(0, 0, 0, .05);
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  overflow-x: hidden !important;
  overflow-y: auto;
  // 隐藏滚动条
  scrollbar-width: none;
  -ms-overflow-style: none;

  &::-webkit-scrollbar {
    display: none;
  }
}
main {
  flex:1;
  margin-top: 18px;
}
</style>
