<template>
  <el-dialog :title="$t('arc.overprint.title')" style="padding: 0px;" width="540px" :before-close="cancelEidt" :close-on-click-modal="false"
    :visible.sync="createVisible"
     top="25vh"
  >
    <el-dialog height="400px" width="800px" :title="$t('arc.overprint.printTemplate.select')" :visible.sync='selectVisible' :close-on-click-modal='false' top="15vh"
      append-to-body>
      <div class="sync-dialog__div">
        <PrintTemplate ref="printTemplateRef" @selectId="handleSelectClick"></PrintTemplate>
      </div>
    </el-dialog>
    <div style="center:'center';margin-top: 15px;">
      <el-form inline-message label-width="85px" :label-position="labelPosition" :model="entity" :rules="rules"
        ref="entity" style="margin: 0px;padding: 0px;">
        <el-form-item :label="$t('arc.overprint.table.menuName')" prop="bill_type">
          <el-input :disabled="pagedisable" v-model="displayedName" @input="updateValue" size="medium" placeholder="">
            <template #suffix>
              <i class="el-icon-search" @click="handleIconClick"></i>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item :label="$t('arc.overprint.language')" prop="language_code">
          <el-select v-model="entity.language_code" size="medium" style="width: 100%" placeholder="">
            <el-option v-for="item in language" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('arc.overprint.templateNO')" prop="code">
          <el-input prefix-icon="el-icon-edit" v-model="entity.code" size="medium" style="width: 100%"
            :placeholder="$t('arc.overprint.templateNOPlaceholder')"></el-input>
        </el-form-item>
        <el-form-item :label="$t('arc.overprint.templateName')" prop="name">
          <el-input prefix-icon="el-icon-edit" v-model="entity.name" size="medium" style="width: 100%"
            :placeholder="$t('arc.overprint.templateNamePlaceholder')"></el-input>
        </el-form-item>
        <el-form-item :label="$t('arc.overprint.labelName')" prop="type">
          <el-radio-group v-model="entity.type" @change="orderTypeChange" id="pdt_radio_content">
            <el-radio label="FPL">{{ $t('arc.overprint.reportPrint') }}</el-radio>
            <el-radio label="CPL">{{ $t('arc.overprint.labelPrint') }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('arc.overprint.defaultTemplate')" prop="is_default">
          <el-switch @change="geoMapChange" v-model="entity.is_default" class="switch" active-color="#08B68B"
            inactive-color="#dddddd" active-text="on" inactive-text="off" :width="45" />
        </el-form-item>
        <el-form-item :label="$t('arc.overprint.is_angles')" prop="is_angles">
          <el-switch @change="templateAngleChange" v-model="entity.is_angles" class="switch" active-color="#08B68B"
                     inactive-color="#dddddd" active-text="on" inactive-text="off" :width="45" />
        </el-form-item>
        <div v-show="entity.is_angles">
          <el-form-item :label="$t('arc.overprint.angles')" prop="angles">
            <el-input prefix-icon="el-icon-edit" v-model="entity.angles" size="medium" type="number" style="width: 100%"
                      :placeholder="$t('arc.overprint.anglesPlaceholder')"></el-input>
          </el-form-item>
          <el-form-item :label="$t('arc.overprint.width')" prop="width">
            <el-input prefix-icon="el-icon-edit" v-model="entity.width" size="medium" type="number" style="width: 100%"
                      :placeholder="$t('arc.overprint.widthPlaceholder')"></el-input>
          </el-form-item>
          <el-form-item :label="$t('arc.overprint.height')" prop="height">
            <el-input prefix-icon="el-icon-edit" v-model="entity.height" size="medium" type="number" style="width: 100%"
                      :placeholder="$t('arc.overprint.heightPlaceholder')"></el-input>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <div slot="footer" class="dialog-footer">
      <div class="footer-buttons">
        <input type="file" ref="fileInput"  accept=".rdlx"  @change="handleFileChange" style="display: none;" />
        <vxe-toolbar size="small">
          <template #buttons>
            <vxe-button :content="$t('public.import')">
              <template #dropdowns>
                <vxe-button mode="text" :content="$t('public.localImport')"  @click="importTemplaeFile('entity')"></vxe-button>
                <vxe-button mode="text" :content="$t('public.cloudImport')"  @click="importCloudTemplaeFile('entity')"></vxe-button>
              </template>
            </vxe-button>
            <vxe-button status="primary" @click="addEntity('entity')">{{ $t('public.sure') }}</vxe-button>
          </template>
        </vxe-toolbar>
<!--        <el-button size="mini"  @click="importTemplaeFile('entity')">{{ $t('public.import') }}</el-button>-->
<!--        <el-button size="mini" type="primary" @click="addEntity('entity')">{{ $t('public.sure') }}</el-button>-->
      </div>
      <el-dialog height="400px" width="600px" :title="$t('arc.topDialogTitle')" :visible.sync='importVisible' :close-on-click-modal='false' top="15vh"
                 append-to-body>
        <div style="center:'center';margin-top: 15px;">
          <vxe-table
            border
            ref="xTable1"
            height="400px"
            @cell-dblclick="cellDblclickkEvent"
            :data="rdlxFiles"
            :radio-config="{highlight: true}">
            <vxe-column type="radio" width="60"></vxe-column>
            <vxe-column field="id" :title="$t('arc.overprint.table.templateName')"></vxe-column>
          </vxe-table>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button size="mini" type="primary" @click="importTemplate()">{{ $t('public.sure') }}</el-button>
        </div>
      </el-dialog>
    </div>
    <!-- <span slot="footer" class="dialog-footer"> -->
    <!-- <el-button size="mini" @click="cancelEidt">{{ $t('public.cancel') }}</el-button> -->
    <!-- <el-button size="mini" type="primary" @click="addEntity('entity')">{{ $t('public.sure') }}</el-button> -->
    <!-- </span> -->
  </el-dialog>
</template>
<script>
import Vue from 'vue';
import { downNginxPrintTemplate, getRdlx, getTypeList, reportConAdd, reportConEdit } from '@/api/interfaceList'
import PrintTemplate from '@/views/custommana/overprint/printTemplate.vue'
import {
  fetchReportNameEncodedString,
  savePrintTemplateDataSource
} from '@/api/printTemplateDesign'
import axios from '@/router/axios'
export default {
  components: { PrintTemplate },
  name: 'CreateTemplate',
  props: {
    fromDataset: {
      type: Object,
      default: () => { }
    },
  },
  data() {
    let validators = (rule, value, callback) => {
      if (this.isSubmit) {
        if (!value) {
          let msg;
          if (rule.field == "code")
            msg = this.$t('arc.overprint.errorMsg0')
          else if (rule.field == "bill_type")
            msg = this.$t('arc.overprint.errorMsg1')
          else if (rule.field == "name")
            msg = this.$t('arc.overprint.errorMsg2')
          this.$alert(msg, this.$t('arc.overprint.error'), { type: 'err' }, {
            confirmButtonText: this.$t('public.sure'),
          });
          this.isSubmit = false
        } else {
          callback();
        }
      }
    };
    return {
      createVisible: false,
      selectVisible: false,
      importVisible: false,
      tableData:[],
      entity: {
        parent_id: '',
        id: '',
        code: '',
        name: '',
        type: 'FPL',
        language_code: '',
        bill_type: '',
        bill_type_name: '',
        is_default: false,
        is_angles:  false,
        have_file:false,
        angles: '',
        width: '',
        height: ''
      },
      displayedName: '',
      displayedType: '',
      language: [
        { label: '简体中文', value: 'zh-hans' },
        { label: '繁體中文', value: 'zh-hant' },
        { label: 'English', value: 'en' }
      ],
      labelPosition: 'right',
      pagedisable: false,
      datechoice: [],
      orderType: '1',
      orderTypeName: '',
      rules: {
        code: [{ validator: validators, required: true, trigger: 'blur' }],
        bill_type: [{ validator: validators, required: true, trigger: 'blur' }],
        name: [{ validator: validators, required: true, trigger: 'blur' }]
      },
      isSubmit: false,
      rdlxFiles: [], // 用于存储提取出的 .rdlx 文件
      selectRow: null
    }
  },
  mounted() {
    Vue.set(this.entity, 'language_code', 'zh-hans');
  },
  created() {
  },
  methods: {
    updateValue() {
      const option = this.datechoice.find(option => option.parent_id == this.displayedType);
      if (option) {
        Vue.set(this.entity, 'bill_type_name', option.label);
        Vue.set(this.entity, 'bill_type', option.parent_id);
      } else {
        Vue.set(this.entity, 'bill_type_name', '');
        Vue.set(this.entity, 'bill_type', '');
      }
    },
    orderTypeChange(val) {
      if (val === '1') {
        this.orderTypeName = this.$t('arc.orderTypeName0')
      } else {
        this.orderTypeName = this.$t('arc.orderTypeName1')
      }
    },
    handleIconClick() {
      if (this.pagedisable)
        return
      this.selectVisible = true
      // this.$refs.printTemplateRef.setForm();
    },
    geoMapChange(val) {
      this.entity.is_default = val
      this.$forceUpdate()
    },
    templateAngleChange(val) {
      if (!val){
        this.entity.angles = ''
        this.entity.width = ''
        this.entity.height = ''
      }
      this.entity.is_angles = val
      this.$forceUpdate()
    },
    addEntity(formName) {
      this.isSubmit = true
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.entity.is_angles){
            if (!this.entity.width || !this.entity.height || !this.entity.angles){
              this.$message.error($t('arc.overprint.errorMsg3'))
              return
            }
          }
          if (this.entity.id) {
            reportConEdit(this.entity).then(res => {
              this.$message.success(this.$t('public.success'))
              this.setCreateVisible(false)
              this.pagedisable = false
              this.isSubmit = false
              // this.printedit(this.entity)
              this.$emit('refresh',this.entity)
            }).catch(err => {
              this.$message(err)
            })
          } else {
            reportConAdd(this.entity).then(res => {
              this.$message.success(this.$t('public.success'))
              this.setCreateVisible(false)
              this.pagedisable = false
              this.isSubmit = false
              this.entity.id = res.data
              // this.printedit(this.entity)
              this.$emit('refresh',this.entity)
            }).catch(err => {
              this.$message(err)
            })
          }
        }
      })
    },
    async importTemplaeFile(formName){
      this.isSubmit = true
      this.$refs[formName].validate((valid) => {
        if (valid) {
          // 触发隐藏的文件输入框点击事件
          this.$refs.fileInput.click();
          this.isSubmit = false
        }
      })
    },
    async importCloudTemplaeFile(formName){
      this.isSubmit = true
      await this.$refs[formName].validate((valid) => {
        if (valid) {
          // 触发隐藏的文件输入框点击事件
          // this.$refs.fileInput.click();
          this.getFileList()
          console.log('rdlxFiles', this.rdlxFiles)
          this.importVisible = true
          this.isSubmit = false
        }
      })
    },
    handleFileChange: async function(event) {
      // 获取选中的文件
      const file = event.target.files[0]
      console.log('selectedFile', file)
      if (file && file.name.endsWith('.rdlx')) {
        await this.readFile(file).then(() => {
          //截取文件数据源内容
          let index = this.fileContent.indexOf('<!--')
          if (index != -1) {
            let dataStr = this.fileContent.substring(index+4, this.fileContent.length-3)
            console.log('dataStr', dataStr)
            console.log('content', this.fileContent.substring(0, index))
            this.fileContent = this.fileContent.substring(0, index)
            //导入数据源
            savePrintTemplateDataSource({'content': dataStr}).then(res => {
              if (res.code === 0) {
                this.$message.success(this.$t('arc.topMsgSuccess'))
              } else {
                this.$message.error(this.$t('arc.topMsgError'))
              }
            })
            this.entity.have_file = true
            this.entity.file = '\uFEFF' + this.fileContent
            this.addEntity('entity')
          }
        })
      } else {
        this.$message.warning({ message: this.$t('arc.fileTypeError') })
      }
    },
    readFile(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = (e) => {
          this.fileContent = e.target.result; // 将读取的内容保存为字符串
          console.log('fileContent', e.target.result);
          resolve(); // 读取成功，调用 resolve
        };
        reader.onerror = (e) => {
          console.error('文件读取失败:', e);
          reject(e); // 读取失败，调用 reject
        };
        reader.readAsText(file); // 以文本格式读取文件
      });
    },
    async getFileList() {
      // 假设 Nginx 提供的路径是 '/app/'
      await getRdlx()
        .then(response => {
          this.parseRdlxFiles(response);
        })
        .catch(error => {
          console.error('Error fetching file list', error);
        });
    },
    parseRdlxFiles(html) {
      // 正则表达式匹配所有以 .rdlx 结尾的链接
      const rdlxRegex = /href="([^"]+\.rdlx)"/g;
      let match;
      let files = [];

      // 用正则提取所有 .rdlx 文件链接
      while ((match = rdlxRegex.exec(html)) !== null) {
        files.push(match[1]);  // match[1] 是链接地址
      }
      //发送请求后端转码
      fetchReportNameEncodedString({ 'params':files }).then(res => {
        let names = []
        if (res.code === 0) {
          res.data.forEach(item => {
            names.push({ id:item });
          })
          // 更新 rdlxFiles 数组
          this.rdlxFiles = names;
        }
      })
    },
    importTemplate(){
      this.downloadFile(this.$refs.xTable1.getRadioRecord());
    },
    cellDblclickkEvent({row}){
      this.selectRow = row;
      this.downloadFile(this.selectRow);
    },
    //根据名字去NGINX下载文件，然后导入到系统中
    async downloadFile(row){
      this.importVisible = false;
      await downNginxPrintTemplate(row.id).then(res =>{
        //截取文件数据源内容
        let index = res.indexOf('<!--')
        if (index != -1) {
          let dataStr = res.substring(index+4, res.length-3)
          this.fileContent = res.substring(0, index)
          //导入数据源
          savePrintTemplateDataSource({'content': dataStr}).then(res => {
            if (res.code === 0) {
              this.$message.success(this.$t('arc.topMsgSuccess'))
            } else {
              this.$message.success(this.$t('arc.topMsgError'))
            }
          })
          this.entity.have_file = true
          this.entity.file = '\uFEFF' + this.fileContent
          this.addEntity('entity')
        }
      })
    },

    printedit(row) {
      const username = this.$store.state.user.info.username
      //路由参数放缓存
      localStorage.setItem(username + "_bill_type", row.parent_id)
      localStorage.setItem(username + "_template_id", row.id)
      localStorage.setItem(username + "_refresh_print", true)//刷新页面标识
      this.$router.push({
        name: 'arcmodel',
        params: { id: row.id, name: row.name, reportId: row.id, bill_type: row.parent_id ,subType:row.type},
        replace: true
      })
    },
    cancelEidt() {
      this.pagedisable = false
      this.createVisible = false
    },
    async setCreateVisible(val, params) {
      let _that = this
      await getTypeList({
        current: 1,
        size: 1000,
      }).then(res => {
        _that.datechoice = res.data.records
      }).catch(err => {
        _that.requestFailed(err)
      })
      this.createVisible = val
      if (params) {
        this.pagedisable = true
        this.entity = params
        this.displayedName = params.bill_type_name
        this.displayedType = params.bill_type
        if (this.entity.angles > 0)
          this.entity.is_angles = true
        this.updateValue()
      }
    },
    handleSelectClick(prams) {
      this.displayedName = prams.label
      this.displayedType = prams.parent_id
      Vue.set(this.entity, 'bill_type', prams.parent_id);
      Vue.set(this.entity, 'bill_type_name', prams.label);
      this.selectVisible = false
      this.updateValue()
    }
  }
}
</script>
<style lang='scss' scoped>
.dialog-footer {
  padding: 10px 10px;
}

::v-deep .el-dialog {
  border-radius: 10px;
}

::v-deep .el-dialog__title {
  border-radius: 10px;
  line-height: 30px;
  font-size: 14px;
  color: #303133;
}

::v-deep .el-dialog__header {
  padding: 2px 10px;
  height: 30px;
  border-radius: 10px 10px 0 0;
  background-color: #dce6e8 !important;
  color: #ffffff !important;
}

::v-deep .el-dialog__body {
  padding: 0px 10px;
}

::v-deep .el-dialog__footer {
  padding: 0px;
}

::v-deep .el-dialog__headerbtn {
  position: absolute;
  top: 5px;
  right: 20px;
  padding: 0;
  background: 0 0;
  border: none;
  outline: 0;
  cursor: pointer;
  font-size: 16px;
}
.el-form-item {
  margin-bottom: 10px;
}

::v-deep .vxe-toolbar .vxe-buttons--wrapper{
  justify-content: flex-end;
}
</style>