<template>
  <div class='tab-container'>
    <div class='card-container'>
      <vToolbarTop :data="toolbarItems" @toolbarClick="handleToolbarClick"></vToolbarTop>
      <input type="file" ref="fileInput"  accept=".rdlx"  @change="handleFileChange" style="display: none;" />
      <div id='arc-designer' v-loading='viewLoading' class='app-container account-container'>
        <div class='scroll-box-container'>
          <div class='report-designer'>
            <div ref='designer' id='designer-id' style='height: calc(100vh - 135px);' />
          </div>
        </div>
      </div>

      <el-dialog :title="$t('public.preview')" :visible.sync='visible' :close-on-click-modal='true' :show-close='true' top='6vh'  v-if="visible"
        width='80%'>
        <!-- <div class='viewer-container'> -->
        <div class="input-container">
          <label for="document-code">{{ $t('arc.index.sqlParameter') + ':' }}</label>
          <el-input v-model="sqlParams" id="document-code" class="document-input" size="small"
            style="border-color: rgb(178, 178, 178);">
          </el-input>
          <el-button type="primary" @click="preview" size="small" class="preview">{{ $t('public.preview') }}</el-button>
        </div>
        <ArViewer ref='arViewer' :report-id='reportId' :is-full-screen='fullScreen' />
        <!-- </div> -->
      </el-dialog>

      <el-dialog :title="$t('arc.index.sqlDataSourceList')" :visible.sync='sqlVisible' :close-on-click-modal='false' :show-close='true'
        :before-close="sqlListClose" width='60%'>
        <el-dialog width="80%" :title="$t('arc.index.sqlDataSource')" :visible.sync='innerVisible' :close-on-click-modal='false'
          :before-close="beforeClose" append-to-body>
          <div class="sync-dialog__div">
            <DataSource ref="interfaceDetails" :fromDataset="this.form" :isEdit="this.isEdit"></DataSource>
          </div>
        </el-dialog>
        <div class="toolbar">
          <div class="toolbar-btn">
            <el-button @click="addHandler()">
              <svg-icon icon-class="icon-add" />
              {{ $t('public.add') }}
            </el-button>
            <el-button @click="removeHandler()">
              <svg-icon icon-class="icon-delete" />
              {{ $t('public.delete') }}
            </el-button>
          </div>
        </div>
        <main>
          <vxe-grid ref="gridRef" border stripe resizable show-overflow show-header-overflow :height="tableHeight"
            :loading="loading" :columns="tableColumn" :data="tableData" :custom-config="{ mode: 'popup' }"
            :radio-config="{ trigger: 'row', highlight: true }" :pager-config="tablePage"
            @page-change="handlePageChange" @cell-dblclick="handleDblclick">
          </vxe-grid>
        </main>
      </el-dialog>
    </div>
    <div style="text-align: left">
      <ImPort ref="imPortTemplateRef"></ImPort>
    </div>
    <el-dialog height="400px" width="600px" title="选择导入套版" :visible.sync='importVisible' :close-on-click-modal='false' top="15vh"
               append-to-body>
      <div style="center:'center';margin-top: 15px;">
        <vxe-table
          border
          ref="xTable1"
          height="400px"
          @cell-dblclick="cellDblclickkEvent"
          :data="rdlxFiles"
          :radio-config="{highlight: true}">
          <vxe-column type="radio" width="60"></vxe-column>
          <vxe-column field="id" title="套版名称"></vxe-column>
        </vxe-table>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="importTemplate()">{{ $t('public.sure') }}</el-button>
      </div>
    </el-dialog>
  </div>

</template>

<script>

import '@grapecity/ar-designer/dist/web-designer.css'
import ArViewer from './ArViewer'
import {
  downPrintTemplate,
  fetchPrintTemplate,
  fetchReportDatasetList, fetchReportNameEncodedString,
  savePrintTemplateDataSource,
  fetchTemplateFileData
} from '@/api/printTemplateDesign'
import vToolbarTop from './vToolbarTop.vue'
import DataSource from '@/views/custommana/interfaceList/newList.vue'

const ActiveReports = window.GrapeCity.ActiveReports
import {
  downNginxPrintTemplate,
  getRdlx,
  getTypeList,
  registeringDataSourceDel,
  registeringDataSourcePage
} from '@/api/interfaceList'
import ImPort from '@/views/custommana/arc/createTemplate.vue'
export default {
  name: 'ReportDesigner',
  components: {
    ImPort,
    ArViewer, DataSource,vToolbarTop
  },
  data() {
    return {
      rdlxFiles: [], // 用于存储提取出的 .rdlx 文件
      importVisible:false, //云端套版界面
      fileContent:'',
      designerId:'',
      sqlParams: '',//设计器预览传参值
      fullScreen: true,
      viewLoading: false,
      templateObject: null, // 当前的套版对象
      queryId: this.$route.params.id, // 查询id
      reportId: null,
      visible: false,
      sqlVisible: false,
      innerVisible: false,
      activeName: '1',
      bill_type: '',
      subType:'',
      templateId: '',
      toolbarItems: [
        { label: 'arc.arcTitleName', value: 'title' },
        { value: 'create', visible: false },
        { value: 'remove', visible: false },
        { value: 'query', visible: false },
        { value: 'custom', visible: false },
        { label: 'public.return', value: 'return', icon: 'icon-return', },
        { label: 'public.save', value: 'save', icon: 'icon-add' },
        // { label: 'public.preview', value: 'preview', },
        // { label: '打印', value: 'printed', icon: 'icon-print' },
        { label: 'arc.index.dataSource', value: 'dataSource', icon: 'icon-setting' },
        // { label: 'public.import', value: 'import', icon: 'icon-import', },
        { label: 'public.export', value: 'export', icon: 'icon-export', },
      ],
      tableColumn: [
        { type: "seq", width: '50', fixed: "left", },
        { field: "bill_type", title: "arc.interfaceList.templateType", },
        { field: "name", title: "arc.interfaceList.interfaceName", },
        { field: "connectString", title: "arc.interfaceList.interfaceAddress", },
        { field: 'dataProvider', title: 'arc.interfaceList.interfaceType', },
        { field: 'is_default', title: 'arc.interfaceList.isPublic', }
        // { field: 'is_default', title: 'arc.interfaceList.isPublic',
        //   slots: {
        //     default: ({ row }) => {
        //       return this.$createElement('el-tag', {
        //         props: {
        //           type: row.is_default === true ? 'primary' : 'danger'
        //         },
        //         domProps: {
        //           innerHTML: row.is_default === true ? this.$t(`public.T`) :  this.$t(`public.F`)
        //         }
        //       })
        //     }
        //   }
        // },
      ],
      loading: false,
      tablePage: {
        layouts: ['Total', 'Sizes', 'PrevPage', 'JumpNumber', 'NextPage', 'FullJump'],
        total: 0,
        currentPage: 1,
        pageSize: 20
      },
      tableHeight: 500,
      tableData: [],
      datechoice: [],
      isEdit: false,
      form: {
        id: null,
        bill_type: '',
        commandText: '',
        is_default: false,
        dataSourceName: '',
        name: '',
        connectString: '/api/sys/report_sql',
        suffix: ';',
        prefix: 'jsondoc=',
        deliveryType: '',
        custom_sql: true,
        sql_script: '',
        switch_data_source: '',
        report_filed: false,
      },
      // options: [
      //   { label: '$', value: '$' },
      //   { label: '$.[*]', value: '$.[*]' },
      //   { label: '$.data', value: '$.data' },
      //   { label: '$.data.[*]', value: '$.data.[*]' },
      //   { label: '$.data.records.[*]', value: '$.data.records.[*]' }
      // ],
      rules: {
        dataSourceName: [{ required: true, message: this.$t('arc.index.rulesDataSourceName'), trigger: 'blur' }],
        name: [{ required: true, message: this.$t('arc.index.rulesName'), trigger: 'blur' }],
        connectString: [{ required: true, message: this.$t('arc.index.rulesConnectString'), trigger: 'blur' }]
      },
      api:null,
    }
  },
  watch: {
    $route(to, from) {
      if (this.$route.name === 'arcmodel' && this.$route.params.id) {
        this.queryId = this.$route.params.id
      }
    },
    // 切换页签时，只有不同套版才进行刷新，否则不刷新
    queryId() {
      fetchPrintTemplate(this.$route.params.id, { is_get_file: false }).then(res => {
        this.templateObject = res
        // 路由切换不需要重新renderApplication
        if (this.templateObject.have_file) {
          this.openReportEvent()
        } else {
          this.createReportEvent()
        }
      }).catch(err => this.requestFailed(err))
    }
  },
  activated() {
    //处理页面缓存问题
    this.initAR()
  },
  created() {
    getTypeList().then(res => {
      this.datechoice = this.datechoice.concat(res.data)
    }).catch(err => {
      this.requestFailed(err)
    })
  },
  mounted() {
    this.bill_type = this.$route.params.bill_type;
    this.templateId = this.$route.params.id
    this.subType = this.$route.params.subType
    // this.initAR()

    // if(this.bill_type)
    //   localStorage.setItem("billType",this.bill_type)
    // else 
    // this.bill_type = llocalStorage.getItem("billType")
    // // document.getElementById('appmainid').style.overflowY = 'scroll'
    // let id;
    // if (this.$route.params.id)
    //   id = this.$route.params.id
    // else
    //   id = localStorage.getItem("templateID")
    // fetchPrintTemplate(id, { is_get_file: false }).then(res => {

    //   this.templateObject = res
    //   this.setDefaultSettings()
    //   this.initWebDesinger()
    // }).catch(err => this.requestFailed(err))
  },
  destroyed() {
    console.log('destroyed')
    if (this.$refs.designer){
    //   // let api = ActiveReports.WebDesignerV2.apiOf(this.$refs.designer.id)
    //   if (this.api != undefined && this.api != null) {
    //     const hasUnsavedChanges = api.documents.hasUnsavedChanges()
    //     console.log(hasUnsavedChanges, 'hasUnsavedChanges')
    //     if (hasUnsavedChanges) {
    //       this.api.documents.saveById(this.templateObject.id.toString(), this.templateObject.id.toString()).then(() => {
    //         console.log('静默保存成功！.')
    //         this.reloadJsSciprt('designerjs', '/static/js/web-designer.js')
    //       })
    //     } else {
    //       // 只能通过一通骚操作避开关闭页签再打开/刷新页签时，报错id已经存在的问题
    //       this.reloadJsSciprt('designerjs', '/static/js/web-designer.js')
    //     }
    //   }
      this.reloadJsSciprt('designerjs', '/static/js/web-designer.js')
    }
  },
  beforeDestroy() {
    const username = this.$store.state.user.info.username
    localStorage.setItem(username + "_template_id", this.templateObject.id.toString())
    console.log('beforeDestroy')
    // let api = ActiveReports.WebDesignerV2.apiOf(this.$refs.designer.id)
    if (this.api != undefined && this.api != null) {
      // const hasUnsavedChanges = this.api.documents.hasUnsavedChanges()
      // console.log(hasUnsavedChanges, 'hasUnsavedChanges')
      // if (hasUnsavedChanges) {
      //   this.api.documents.saveById(this.templateObject.id.toString(), this.templateObject.id.toString()).then(() => {
      //     console.log('静默保存成功！.')
      //     this.reloadJsSciprt('designerjs', '/static/js/web-designer.js')
      //   })
      // } else {
      //   // 只能通过一通骚操作避开关闭页签再打开/刷新页签时，报错id已经存在的问题
      //   this.reloadJsSciprt('designerjs', '/static/js/web-designer.js')
      // }
      this.reloadJsSciprt('designerjs', '/static/js/web-designer.js')
    }
  },
  methods: {
    async initAR() {
      this.subType = this.$route.params.subType
      const username = this.$store.state.user.info.username
      let by = localStorage.getItem(username + "_bill_type")
      let id = localStorage.getItem(username + "_template_id")
      let refreshFlag = localStorage.getItem(username + "_refresh_print")
      let flag = false;   // 路由切换不需要重新renderApplication
      if (this.bill_type) {
        if (this.templateId) {
          flag = true
          id = this.templateId
        } else if (refreshFlag === "true") { //说明重新点击了新的套版
          flag = true
          this.bill_type = by
        }
        localStorage.setItem(username + "_refresh_print", false)//刷新页面标识
      } else {
        flag = true
        this.bill_type = by
      }
      if (flag === true) {
        await fetchPrintTemplate(id, { is_get_file: false }).then(res => {
          this.templateObject = res
          if (this.designerId){
            if (this.templateObject.have_file) {
              this.openReportEvent()
            } else {
              this.createReportEvent()
            }
          }else {
            this.setDefaultSettings()
            this.initWebDesinger0()
          }
        }).catch((err) => {
          this.requestFailed(err)
        })
        this.templateId = undefined;//处理页面缓存刷新问题
      }
    },
    handleToolbarClick(params) {
      switch (params) {
        case 'save':
          this.saveEvent();
          break
        case 'preview':
          this.previewEvent();
          break
        case 'printed':
          this.arPrint();
          break
        case 'return':
          this.backEvent();
          break
        case 'dataSource':
          this.dataSourceEvent();
          break
        case 'import':
          this.importPrintTemplate();
          break
        case 'export':
          this.exportPrintTemplate();
          break
        case 'localImport':
          this.localImport();
          break
        case 'cloudImport':
          this.cloudImport();
        default:
      }
    },
    // 设置动态id
    designerKey() {
      return 'designer-' + new Date().getTime()
    },
    // 重新加载js文件
    reloadJsSciprt(id, newJS) {
      let oldjs = null
      oldjs = document.getElementById(id)
      if (oldjs) oldjs.parentNode.removeChild(oldjs)
      let scriptObj = document.createElement('script')
      scriptObj.src = newJS
      scriptObj.type = 'text/javascript'
      scriptObj.id = id
      console.log(scriptObj, 'scriptObj')
      document.getElementsByTagName('body')[0].appendChild(scriptObj)
    },
    // 初始化报表设计器
    async initWebDesinger0(){
      this.designerId = this.designerKey()
      this.$refs.designer.id = this.designerId
      var viewer = null;
      this.api = await ActiveReports.Designer.create('#' + this.designerId, {
        propertyGrid: {
          propertiesTab: {
            mode: 'Basic',
          },
        },
        units: 'cm',
        language:this.templateObject.language_code === 'en' ? 'en' : 'zh',
        editor: {
          snapToGrid: true,
          snapToGuides: true,
        },
        documents: {
          fileView: {
            visible: false,
          },
        },
        storeUnsavedReport: false,
        rpx: { enabled: true },
        appBar: {
          saveButton: { visible: false },
          saveAsButton: { visible: false }
        },
        data: {
          dataSets: { canModify: true },
          dataSources: { visible: false,canModify: false }
        },
        server: {
          url: '/arc/api'
        },
        preview: {
          openViewer: (options) => {
            // if (viewer) {
            //   viewer.openReport(documentId);
            //   return;
            // }
            viewer = ActiveReports.JSViewer.create({
              element: '#' + options.element,
              reportID: options.reportInfo.id,
              reportService: {
                url: '/arc/api/reporting'
              },
              settings: {
                zoomType: 'FitPage'
              }
            });
          }
        }
      })
      if (this.api) {
        if (this.templateObject.have_file) {
          this.openReportEvent()
        } else {
          this.createReportEvent()
        }
      }
    },


    async initWebDesinger() {
      console.log('ActiveReports', window.GrapeCity.ActiveReports)
      const designerOptions = ActiveReports.WebDesigner.createDesignerOptions()

      designerOptions.server.url = '/arc/api'
      designerOptions.reportItemsFeatures.table.autoFillFooter = false
      designerOptions.fileView.visible = false // 将文件工具栏按钮进行隐藏
      designerOptions.dataTab.dataSets.canModify = true
      designerOptions.dataTab.dataSources.canModify = false
      designerOptions.dataTab.dataSources.visible = false // 隐藏数据源面板
      // designerOptions.dataTab.parameters.visible = true;
      // designerOptions.dataTab.parameters.canModify = false;
      // designerOptions.fonts = ['微软雅黑', 'Arial Unicode MS']
      this.designerId = this.designerKey()
      this.$refs.designer.id = this.designerId
      designerOptions.propertiesTab.defaultMode = JSON.parse(localStorage.getItem('designerSettings')).advancedMode ? 'Advanced' : 'Basic' // 属性设置模式默认为高级模式
      designerOptions.language = this.templateObject.language_code === 'en' ? 'en' : 'zh'
      designerOptions.storeUnsavedReport = false // 去除未保存报表恢复功能
      designerOptions.openViewer = function (options) {
        let parameters
        const URI = encodeURIComponent("@id=1")
        let viewer = new ActiveReports.JSViewer.create({ // eslint-disable-line
          element: '#' + options.element,
          reportService: {
            url: '/arc/api/reporting',
          },
          reportID: options.reportInfo.id,
          settings: {
            zoomType: 'FitPage'
          },
          // reportParameters: [{ name: '@id', values: ['1'] }]
        })
        // viewer.openReport(options.reportInfo.id + '|' + URI)
      }

      // designerOptions.toggleTooltipVisible()
      console.log('designerOptions：', designerOptions)
      ActiveReports.WebDesigner.renderApplication(this.$refs.designer.id, designerOptions).then(res => {
            // 异步方法，调用结束后再执行
            if (this.templateObject.have_file) {
              this.openReportEvent()
            } else {
              this.createReportEvent()
            }
          }).catch((err) => {
            console.log(err, 'err')
            this.viewLoading = false
            // this.requestFailed(err)
            //处理退出系统问题
            if (this.templateObject.have_file) {
              this.openReportEvent()
            } else {
              this.createReportEvent()
            }
          })
    },

    // 通过修改localStorage的designerSettings来关闭网格对齐的功能,避免拖拽卡顿
    closeSnapSwitch() {
      const designerSettings = JSON.parse(localStorage.getItem('designerSettings'))
      let settingsObjet = {}
      if (designerSettings !== null) {
        settingsObjet = designerSettings
        settingsObjet.snapToGrid = false
        settingsObjet.snapToLines = false
        settingsObjet.locale = this.templateObject.language_code === 'zh-hant' ? 'zh-hans' : this.templateObject.language_code
        localStorage.setItem('designerSettings', JSON.stringify(settingsObjet))
      } else {
        // 使用默认值
        this.setDefaultSettings()
      }
    },
    // 设置默认值
    setDefaultSettings() {
      const defaultObject = {
        advancedMode: true,
        gridSize: '0.5cm',
        locale: 'zh',
        showGrid: true,
        sidebarWidth: 0,
        snapToGrid: false,
        snapToLines: false,
        units: 'cm',
        zoomFactor: 1
      }
      localStorage.setItem('designerSettings', JSON.stringify(defaultObject))
    },
    // 返回
    backEvent() {
      this.$router.push({ path: '/custommana/overprint/index' })
    },
    // 工具栏-保存
    saveEvent() {
      console.log('tempObject', this.templateObject)
      // let api = ActiveReports.Designer.apiOf(this.$refs.designer.id)
      this.api.documents.saveById(this.templateObject.id.toString(), this.templateObject.id.toString()).then(() => {
        console.log('保存报表成功！.')
        this.$message.success(this.$t('arc.saveReportTopMsg'))
      }).catch(err => {
        if (err) {
          // 失败重试
          this.api.documents.saveById(this.templateObject.id.toString(), this.templateObject.id.toString())
        }
      })
    },
    // 工具栏-导入
    importPrintTemplate() {
      // 触发隐藏的文件输入框点击事件
      this.$refs.fileInput.click();
    },

    handleFileChange: async function(event) {
      // 获取选中的文件
      const file = event.target.files[0]
      console.log('selectedFile', file)
      if (file && file.name.endsWith('.rdlx')) {
        await this.readFile(file).then(() => {
          //截取文件数据源内容
          let index = this.fileContent.indexOf('<!--')
          if (index != -1) {
            let dataStr = this.fileContent.substring(index+4, this.fileContent.length-3)
            console.log('dataStr', dataStr)
            console.log('content', this.fileContent.substring(0, index))
            this.fileContent = this.fileContent.substring(0, index)
            //导入数据源
            savePrintTemplateDataSource({'content': dataStr}).then(res => {
              if (res.code === 0) {
                this.$message.success(this.$t('arc.topMsgSuccess'))
              } else {
                this.$message.success(this.$t('arc.topMsgError'))
              }
            })
          }
          let entity = {}
          entity.is_default = false
          entity.language_code = 'zh-hans'
          entity.bill_type = this.templateObject.bill_type
          entity.bill_type_name = this.templateObject.bill_type_name
          entity.file = '\uFEFF' + this.fileContent
          this.$refs.imPortTemplateRef.setCreateVisible(true, entity)
        })
      } else {
        this.$message.warning({ message: this.$t('arc.fileTypeError') })
      }
    },
    readFile(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = (e) => {
          this.fileContent = e.target.result; // 将读取的内容保存为字符串
          console.log('fileContent', e.target.result);
          resolve(); // 读取成功，调用 resolve
        };
        reader.onerror = (e) => {
          console.error('文件读取失败:', e);
          reject(e); // 读取失败，调用 reject
        };
        reader.readAsText(file); // 以文本格式读取文件
      });
    },
    /**
     * 导入功能 本地导入和云端导入
     */
    localImport(){
      this.importPrintTemplate()
    },
    cloudImport(){
      this.getFileList()
      this.importVisible = true
    },
    async getFileList() {
      // 假设 Nginx 提供的路径是 '/app/'
      await getRdlx()
        .then(response => {
          this.parseRdlxFiles(response);
        })
        .catch(error => {
          console.error('Error fetching file list', error);
        });
    },
    parseRdlxFiles(html) {
      // 正则表达式匹配所有以 .rdlx 结尾的链接
      const rdlxRegex = /href="([^"]+\.rdlx)"/g;
      let match;
      let files = [];

      // 用正则提取所有 .rdlx 文件链接
      while ((match = rdlxRegex.exec(html)) !== null) {
        files.push(match[1]);  // match[1] 是链接地址
      }
      //发送请求后端转码
      fetchReportNameEncodedString({ 'params':files }).then(res => {
        let names = []
        if (res.code === 0) {
          res.data.forEach(item => {
            names.push({ id:item });
          })
          // 更新 rdlxFiles 数组
          this.rdlxFiles = names;
        }
      })
    },
    importTemplate(){
      this.downloadFile(JSON.stringify(this.$refs.xTable1.getRadioRecord()));
    },
    cellDblclickkEvent({row}){
      this.downloadFile(row);
    },
    //根据名字去NGINX下载文件，然后导入到系统中
    async downloadFile(row){
      this.importVisible = false;
      await downNginxPrintTemplate(row.id).then(res =>{
        //截取文件数据源内容
        let index = res.indexOf('<!--')
        if (index != -1) {
          let dataStr = res.substring(index+4, res.length-3)
          this.fileContent = res.substring(0, index)
          //导入数据源
          savePrintTemplateDataSource({'content': dataStr}).then(res => {
            if (res.code === 0) {
              this.$message.success('导入数据源成功')
            } else {
              this.$message.error('导入数据源失败')
            }
          })
          let entity = {}
          entity.is_default = false
          entity.language_code = 'zh-hans'
          entity.bill_type = this.templateObject.bill_type
          entity.bill_type_name = this.templateObject.bill_type_name
          entity.file = '\uFEFF' + this.fileContent
          this.$refs.imPortTemplateRef.setCreateVisible(true, entity)
        }
      })
    },
    // 工具栏-导出
    async exportPrintTemplate() {
      const datasetId = this.templateObject.id;
      //文件名格式：code_name_billType_billTypeName_type_language
      let name = this.templateObject.code + "_" + this.templateObject.name + "_" + this.templateObject.bill_type + "_" + this.templateObject.bill_type_name + "_" + this.templateObject.type + "_" + this.templateObject.language_code;
      try {
        // 调用下载函数
      await downPrintTemplate(datasetId).then(res =>{
          // 创建一个 URL 对象
          const url = window.URL.createObjectURL(new Blob([res]));
          // 创建一个链接并触发下载
          const link = document.createElement('a');
          link.href = url;
          link.setAttribute('download', `${name}.rdlx`); // 设置下载文件的名称
          document.body.appendChild(link);
          link.click();
          // 清理 URL 对象
          window.URL.revokeObjectURL(url);
          document.body.removeChild(link);
        });
      } catch (error) {
        console.error('下载模板失败:', error);
      }
    },
    // 获取数据源，数据集
    async fetchDataSourceEvent(is_get_detail = true) {
      if (this.templateObject && this.templateObject.bill_type) {
        const params = {
          bill_type: this.templateObject.bill_type,
          is_get_detail: is_get_detail
        }
        if (this.templateObject.accrpt_type) {
          params.accrpt_type = this.templateObject.accrpt_type
        }
        try {
          // 要把套版语言别作为参数传入，设置request.headers.Accept-Language以获得对应的数据集
          return await fetchReportDatasetList(params, this.templateObject.language_code)
        } catch (error) {
          this.viewLoading = false
        }
      } else {
        this.viewLoading = false
      }
    },
    // 打开已存在的报表
    async openReportEvent() {
      const file = await fetchTemplateFileData(this.templateObject.id)
      // // 获取最新数据集并替换
      const data = await this.fetchDataSourceEvent()
      console.log(data.DataSources, 'data')
      if (data.DataSources.length > 0) {
        if (file.DataSources == undefined) {
          file.DataSources = this.pusList(data.DataSets)
        }else {
          let json = file.DataSources.concat(data.DataSources)
          file.DataSources = this.pusList(json)
        }
      }
      if (data.DataSets.length > 0) {
        if (file.DataSets == undefined) {
          file.DataSets = this.pusList(data.DataSets)
        }else {
          let json = file.DataSets.concat(data.DataSets)
          file.DataSets = this.pusList(json)
        }
      }
      file.DataSets = data.DataSets;
      file.DataSources = data.DataSources;
      this.api.documents.openById(this.templateObject.id.toString(),{
        platform: 'rdlx',
        type: 'report',
        subType: this.subType,
      },this.templateObject.id,file).then(() => {
        console.log('====打开了报表====', this.templateObject.name)
      })
    },
    pusList(data) {
      let uniqueArr = data.reduce((prev, cur) => {
        let ids = prev.map(item => item.Name)
        if (ids.indexOf(cur.Name) === -1) {
          prev.push(cur)
        }
        return prev
      }, [])
      console.log(uniqueArr)
      return uniqueArr
    },
    // 创建报表
    async createReportEvent() {
      const dataSetsInfo = await this.fetchDataSourceEvent(false)
      if (dataSetsInfo) {
        dataSetsInfo.forEach(item => {
          item.id = `${item['model_class']}|${this.templateObject.language_code}`
          item.name = item.model_name
          delete item.model_class
          delete item.model_name
        })
      }
      const templateInfo = {
        name: this.templateObject.name
      }
      let CreateReportOptions = {
        templateInfo,
        dataSetsInfo
      }
      // 如果是页面报表，且have_file为false，则reportType需要传参
      if (!this.templateObject.have_file && this.templateObject.type === 'FPL') {
        CreateReportOptions.reportType = 'FPL'
      }
      // 创建报表
      // let api = ActiveReports.WebDesignerV2.apiOf(this.$refs.designer.id)
      console.log('CreateReportOptions', CreateReportOptions)
      this.api.documents.create(CreateReportOptions).then(() => {
        console.log('创建报表成功！')
      })
    },
    // 预览
    previewEvent() {
      this.visible = true
      // let api = ActiveReports.WebDesignerV2.apiOf(this.$refs.designer.id)
      this.api.documents.saveById(this.templateObject.id.toString(), this.templateObject.id.toString()).then(() => {
        console.log('保存报表并预览！.')
      })
    },
    //代参数预览
    preview() {
      let params = 'timeKey=1&bill_type=' + this.bill_type + '&condition=id:' + this.sqlParams
      const URI = encodeURIComponent(params)
      this.reportId = this.templateObject.id + '|' + URI
      this.$refs.arViewer.openReportEvent(this.reportId)
    },
    arPrint(){
      // let params = 'timeKey=1&bill_type=' + this.bill_type + '&condition=id:1'
      // const URI = encodeURIComponent(params)
      // this.reportId = this.templateObject.id + '|' + URI
      this.reportId = this.templateObject.id
      GrapeCity.ActiveReports.JSViewer.print(
        {
          reportID:  this.reportId,
        }
      )
    },
    /**
     * sql数据源列表
     */
    dataSourceEvent() {
      this.sqlVisible = true
      this.getList();
    },
    getList() {
      this.bill_type = this.bill_type ? this.templateObject.bill_type : this.bill_type
      registeringDataSourcePage({
        current: this.tablePage.currentPage,
        size: this.tablePage.pageSize,
        bill_type: this.bill_type
      }).then(res => {
        this.tableData = res.data.records
      })
        .catch(err => {
          this.requestFailed(err)
        })
    },
    handlePageChange({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      try {
        registeringDataSourcePage({
          current: this.tablePage.currentPage,
          size: this.tablePage.pageSize
        }).then(res => {
          this.tableData = res.data.records || res.data;
          this.tablePage.total = res.data.total;
          this.tablePage.currentPage = res.data.current;
        }).catch(err => {
        })
      } catch (err) {
        return Promise.reject(err);
      } finally {
        this.loading = false;
      }
    },
    handleDblclick({ row }) {
      this.innerVisible = true
      this.isEdit = true;
      this.form = row
      // this.$refs.interfaceDetails.init(row)
    },
    removeHandler() {
      const selectRecords = this.$refs.gridRef.getRadioRecord()
      console.log(selectRecords)
      if (selectRecords != null) {
        this.$confirm(this.$t('public.del.content'), this.$t('public.del.title'), {
          confirmButtonText: this.$t('public.sure'),
          cancelButtonText: this.$t('public.cancel'),
          type: 'warning'
        }).then(() => {
          this.loading = true
          try {
            let item = { id: selectRecords.id };
            let that = this;
            registeringDataSourceDel(item).then(res => {
              this.$message.success({
                message: this.$t('public.success')
              });
              this.getList();
            }).catch(err => {
              this.$message.info({
                message: err
              });
            })
            this.loading = false
          } catch (err) {
            throw err;
          } finally {
            this.loading = false
          }
        }).catch(() => {
          this.loading = false
        });
      } else {
        this.$message.warning(this.$t('public.list'))
      }
    },
    addHandler() {
      this.innerVisible = true
      this.form = {
        id: null,
        bill_type: this.bill_type,
        commandText: '',
        is_default: false,
        dataSourceName: '',
        name: '',
        connectString: '/api/sys/report_sql',
        suffix: ';',
        prefix: 'jsondoc=',
        deliveryType: '',
        custom_sql: true,
        switch_data_source: true,
        sql_script: ''
      }
      this.isEdit = true;
    },
    /**
     * 数据源详情窗口关闭回调函数
     * @param done
     */
    beforeClose(done) {
      this.getList();
      done();
    },
    /**
     * 数据源列表窗口关闭回调函数
     * @param done
     */
    sqlListClose(done) {
      this.openReportEvent()
      done();
    },
  }
}
</script>
<style lang='scss' scoped>
body,
html {
  background-color: white;
}

/**
*预览弹窗样式
*====================================================================
*/
.preview {
  margin-left: 20px;
}

.input-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
}

.input-container label {
  margin-right: 5px;
}

.document-input {
  width: 200px;
}

:deep(.document-input .el-input__inner) {
  border: 1px solid rgb(178, 178, 178) !important;
  ;
}

//===================================================================
.viewer-container {
  height: calc(100vh - 45px - 30px - 20px);
}

::v-deep .el-dialog {
  border-radius: 10px;
}

::v-deep .el-dialog__title {
  border-radius: 10px;
  line-height: 30px;
  font-size: 14px;
  color: #303133;
}

::v-deep .el-dialog__header {
  padding: 2px 10px;
  height: 30px;
  border-radius: 10px 10px 0 0;
  background-color: #dce6e8 !important;
  color: #ffffff !important;
}

::v-deep .el-dialog__body {
  padding: 10px 10px;
}

::v-deep .el-dialog__headerbtn {
  position: absolute;
  top: 5px;
  right: 20px;
  padding: 0;
  background: 0 0;
  border: none;
  outline: 0;
  cursor: pointer;
  font-size: 16px;
}

.sync-dialog__div {
  height: 590px;
  //overflow: auto;
  overflow-x: hidden;
}

.toolbar {
  margin-bottom: 10px;
  display: flex;
  height: 45px;
  background: #FFFFFF;
  overflow: hidden;
  white-space: nowrap;
  position: relative;
  z-index: 1;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.1);

  .toolbar-title {
    display: flex;
    justify-content: center;
    align-items: center;

    &::after {
      content: '';
      display: block;
      width: 1px;
      height: 22px;
      background: rgba(0, 0, 0, 0.2);
      margin: 0 16px;
    }

    span {
      height: 20px;
      line-height: 20px;
      font-weight: 600;
      margin-left: 16px;
    }
  }

  .toolbar-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;

    ::v-deep .el-button {
      border: none;
      box-shadow: none;
      border-radius: 0;
      height: 45px;
      margin-left: 0;
      padding: 8px 15px;
      color: rgba(0, 0, 0, 0.85);
      font-size: 14px;

      span {
        display: flex;
        align-items: center;

        svg {
          color: var(--el-color-primary);
          margin-right: 3px;
        }
      }

      &:hover {
        color: white;
        background-color: var(--el-color-primary);

        svg {
          color: white;
        }
      }

      &.is-disabled {
        color: #747474;
        pointer-events: none;
        cursor: default;

        svg {
          color: #747474;
        }
      }
    }
  }
}

.vxe-grid {
  ::v-deep .vxe-body--row {
    &.row--checked {
      background: var(--el-color-primary-light-3);
    }
  }

  ::v-deep .vxe-header--row {
    padding: 0;
    height: 32px;
    background-color: var(--el-color-primary-light-4);
  }

}

main {
  .main-top {
    padding-top: 15px;
  }
}

/**
报表设计器样式
 */
::v-deep .wd-vc-header {
  display: grid;
  width: 100%;
  height: 30px;
  color: #fff;
  background-color: #205f78;
  grid-template-columns: 50px 220px 1fr;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
}

::v-deep .gc-heading--case-default .gc-heading__text {
  text-transform: none;
}

::v-deep .gc-heading--text-size-small .gc-heading__text {
  font-size: 10px;
}

::v-deep .gc-heading__text {
  overflow: hidden;
  min-width: 0;
  height: 12px;;
  margin: 0;
  padding: 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  white-space: nowrap;
  text-transform: uppercase;
  text-overflow: ellipsis;
  color: inherit;
  font-size: 12px;
  font-weight: 400;
  line-height: 12px;
}
</style>
