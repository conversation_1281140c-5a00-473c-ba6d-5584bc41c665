<style lang="scss" scoped>
.viewer-container{
  height: calc(100vh - 45px - 30px - 20px);
}
</style>
<template>
  <div v-loading="viewLoading" class="app-container">
     <el-dialog
    title="预览"
    :visible.sync="visible"
    :close-on-click-modal="false"
    width="500px"
    height="400px"
    top="8vh"
  >
    <div class="viewer-container">
      <ArViewer ref="ArViewer"  />
    </div>
    </el-dialog>
  </div>
</template>
<script>
import ArViewer from './ArViewer'
export default {
  name: 'ReportViewer',
  components: {
    ArViewer
  },
  data() {
    return {
      viewLoading: false,
      visible:false,
      reportList: [], // 报表列表
      selectedReport: '',
      reportId: null // 自定义报表ID
    }
  },
  watch: {
    $route(to, from) {
      // 路由跳转，默认重新渲染预览界面，防止套版设计进行了更改
      if (this.$route.params.reportId) {
        console.log('reportViewer', this.$route.params.reportId)
        this.$refs.ArViewer.openReportEvent(this.$route.params.reportId)
      }
    }
  },
  beforeCreate() {

  },
  mounted() {
    // 赋值reportId
    if (this.$route.params.reportId) {
      this.reportId = this.$route.params.reportId
    }
  },
  methods: {
      create (id) {
          this.reportId = id
          this.visible = true
          if (this.$refs.ArViewer) {
              this.$refs.ArViewer.openReportEvent(id)
          }
      },
    selectReport(reportName) {
      this.selectedReport = reportName
    }
  }
}
</script>

