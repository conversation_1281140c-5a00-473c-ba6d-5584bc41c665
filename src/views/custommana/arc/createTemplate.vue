<template>
  <el-dialog :title="$t('arc.overprint.title')" style="padding: 0px;" width="540px" :before-close="cancelEidt" :close-on-click-modal="false"
    :visible.sync="createVisible"
     top="25vh"
  >
    <el-dialog height="400px" width="800px" :title="$t('arc.overprint.printTemplate.select')" :visible.sync='selectVisible' :close-on-click-modal='false' top="15vh"
      append-to-body>
      <div class="sync-dialog__div">
        <PrintTemplate ref="printTemplateRef" @selectId="handleSelectClick"></PrintTemplate>
      </div>
    </el-dialog>
    <div style="center:'center';margin-top: 15px;">
      <el-form inline-message label-width="85px" :label-position="labelPosition" :model="entity" :rules="rules"
        ref="entity" style="margin: 0px;padding: 0px;">
        <el-form-item :label="$t('arc.overprint.table.menuName')" prop="bill_type">
          <el-input v-model="displayedName" @input="updateValue" size="medium" placeholder="">
            <template #suffix>
              <i class="el-icon-search" @click="handleIconClick"></i>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item :label="$t('arc.overprint.language')" prop="language_code">
          <el-select v-model="entity.language_code" size="medium" style="width: 100%" placeholder="">
            <el-option v-for="item in language" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('arc.overprint.templateNO')" prop="code">
          <el-input prefix-icon="el-icon-edit" v-model="entity.code" size="medium" style="width: 100%"
            :placeholder="$t('arc.overprint.templateNOPlaceholder')"></el-input>
        </el-form-item>
        <el-form-item :label="$t('arc.overprint.templateName')" prop="name">
          <el-input prefix-icon="el-icon-edit" v-model="entity.name" size="medium" style="width: 100%"
            :placeholder="$t('arc.overprint.templateNamePlaceholder')"></el-input>
        </el-form-item>
      </el-form>
    </div>
    <div slot="footer" class="dialog-footer">
      <div class="footer-buttons">
        <el-button size="mini" type="primary" @click="addEntity('entity')">{{ $t('public.sure') }}</el-button>
      </div>
    </div>
    <!-- <span slot="footer" class="dialog-footer"> -->
    <!-- <el-button size="mini" @click="cancelEidt">{{ $t('public.cancel') }}</el-button> -->
    <!-- <el-button size="mini" type="primary" @click="addEntity('entity')">{{ $t('public.sure') }}</el-button> -->
    <!-- </span> -->
  </el-dialog>
</template>
<script>
import Vue from 'vue';
import { getTypeList, reportConAdd, reportConEdit } from '@/api/interfaceList'
import PrintTemplate from '@/views/custommana/overprint/printTemplate.vue'
export default {
  components: { PrintTemplate },
  name: 'ImPortTemplate',
  props: {
    fromDataset: {
      type: Object,
      default: () => { }
    },
  },
  data() {
    let validators = (rule, value, callback) => {
      if (this.isSubmit) {
        if (!value) {
          let msg;
          if (rule.field == "code")
            msg = this.$t('arc.overprint.errorMsg0')
          else if (rule.field == "bill_type")
            msg = this.$t('arc.overprint.errorMsg1')
          else if (rule.field == "name")
            msg = this.$t('arc.overprint.errorMsg2')
          this.$alert(msg, this.$t('arc.overprint.error'), { type: 'err' }, {
            confirmButtonText: this.$t('public.sure'),
          });
          this.isSubmit = false
        } else {
          callback();
        }
      }
    };
    return {
      createVisible: false,
      selectVisible: false,
      entity: {
        parent_id: '',
        id: '',
        code: '',
        name: '',
        type: 'FPL',
        language_code: '',
        bill_type: '',
        bill_type_name: '',
        is_default: false,
        have_file:true,
        file:''
      },
      displayedName: '',
      displayedType: '',
      language: [
        { label: '简体中文', value: 'zh-hans' },
        { label: '繁體中文', value: 'zh-hant' },
        { label: 'English', value: 'en' }
      ],
      labelPosition: 'right',
      pagedisable: false,
      datechoice: [],
      orderType: '1',
      orderTypeName: '',
      rules: {
        code: [{ validator: validators, required: true, trigger: 'blur' }],
        bill_type: [{ validator: validators, required: true, trigger: 'blur' }],
        name: [{ validator: validators, required: true, trigger: 'blur' }]
      },
      isSubmit: false,
    }
  },
  mounted() {
    Vue.set(this.entity, 'language_code', 'zh-hans');
  },
  created() {
  },
  methods: {
    updateValue() {
      const option = this.datechoice.find(option => option.parent_id == this.displayedType);
      if (option) {
        Vue.set(this.entity, 'bill_type_name', option.label);
        Vue.set(this.entity, 'bill_type', option.parent_id);
      } else {
        Vue.set(this.entity, 'bill_type_name', '');
        Vue.set(this.entity, 'bill_type', '');
      }
    },
    orderTypeChange(val) {
      if (val === '1') {
        this.orderTypeName = '页面报表'
      } else {
        this.orderTypeName = 'RDL报表'
      }
    },
    handleIconClick() {
      this.selectVisible = true
      // this.$refs.printTemplateRef.setForm();
    },
    geoMapChange(val) {
      this.entity.is_default = val
      this.$forceUpdate()
    },
    addEntity(formName) {
      this.isSubmit = true
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.entity.id) {
            reportConEdit(this.entity).then(res => {
              this.$message.success(this.$t('public.success'))
              this.setCreateVisible(false)
              this.pagedisable = false
              this.isSubmit = false
              this.printedit(this.entity)
            }).catch(err => {
              this.$message(err)
            })
          } else {
            this.entity.have_file = true
            reportConAdd(this.entity).then(res => {
              this.$message.success(this.$t('public.success'))
              this.setCreateVisible(false)
              this.pagedisable = false
              this.isSubmit = false
              this.entity.id = res.data
              this.printedit(this.entity)

            }).catch(err => {
              this.$message(err)
            })
          }
        }
      })
    },
    printedit(row) {
      const username = this.$store.state.user.info.username
      //路由参数放缓存
      localStorage.setItem(username + "_bill_type", row.parent_id)
      localStorage.setItem(username + "_template_id", row.id)
      localStorage.setItem(username + "_refresh_print", true)//刷新页面标识
      this.$router.push({
        name: 'arcmodel',
        params: { id: row.id, name: row.bill_type_name, reportId: row.id, bill_type: row.bill_type,subType:row.type },
      })
    },
    cancelEidt() {
      this.pagedisable = true
      this.createVisible = false
    },
    async setCreateVisible(val, params) {
      let _that = this
      await getTypeList({
        current: 1,
        size: 1000,
      }).then(res => {
        _that.datechoice = res.data.records
      }).catch(err => {
        _that.requestFailed(err)
      })
      this.createVisible = val
      if (params) {
        this.pagedisable = true
        this.entity = params
        this.displayedName = params.bill_type_name
        this.displayedType = params.bill_type
        this.updateValue()
      }
    },
    handleSelectClick(prams) {
      this.displayedName = prams.label
      this.displayedType = prams.parent_id
      Vue.set(this.entity, 'bill_type', prams.parent_id);
      Vue.set(this.entity, 'bill_type_name', prams.label);
      this.selectVisible = false
      this.updateValue()
    }
  }
}
</script>
<style lang='scss' scoped>
.dialog-footer {
  padding: 10px 10px;
}

::v-deep .el-dialog {
  border-radius: 10px;
}

::v-deep .el-dialog__title {
  border-radius: 10px;
  line-height: 30px;
  font-size: 14px;
  color: #303133;
}

::v-deep .el-dialog__header {
  padding: 2px 10px;
  height: 30px;
  border-radius: 10px 10px 0 0;
  background-color: #dce6e8 !important;
  color: #ffffff !important;
}

::v-deep .el-dialog__body {
  padding: 0px 10px;
}

::v-deep .el-dialog__footer {
  padding: 0px;
}

::v-deep .el-dialog__headerbtn {
  position: absolute;
  top: 5px;
  right: 20px;
  padding: 0;
  background: 0 0;
  border: none;
  outline: 0;
  cursor: pointer;
  font-size: 16px;
}

::v-deep .el-form-item {
  margin-bottom: 10px;
}
</style>