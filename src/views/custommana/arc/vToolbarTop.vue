<template>
	<div class="toolbar">
			<div class="toolbar-btn">
					<el-button v-for="(item, index) in toolbarItems" :key="index" @click="handleClick(item.value)" type="text" :disabled="item.disabled">
							<svg-icon :icon-class="item.icon || 'icon-custom-field'" />
							{{ $t(item.label) }}
					</el-button>
        <el-tooltip class="item" effect="light" placement="top">
          <!-- 使用slot传递自定义内容 -->
          <template #content>
            <ul style="margin: 0; padding: 0; list-style: none; max-width: 200px;">
              <li @click="handleClick('localImport')"
                  style="cursor: pointer; padding: 5px 10px; transition: background-color 0.3s;">
                {{$t('public.localImport')}}
              </li>
              <li @click="handleClick('cloudImport')"
                  style="cursor: pointer; padding: 5px 10px; transition: background-color 0.3s;">
                {{$t('public.cloudImport')}}
              </li>
            </ul>
          </template>
          <!-- 按钮 -->
          <el-button @click="handleClick('public.import')" type="text">
            <svg-icon icon-class="icon-import" />
            {{ $t('public.import') }}
          </el-button>
        </el-tooltip>
			</div>
	</div>
</template>

<script>
export default {
	name: 'vToolbarTop',
	props: {
			data: {
					type: Array,
					default: () => []
			},
      editCtrl:{
        type: Boolean,
      }
	},
	data() {
			return {
					title: '标题栏',
					drawer: false,
					toolbarItems: [],
			}
	},
	mounted() {
			this.filterData();
	},
	methods: {
			filterData() {
					// 如果data中有title项，更新title
					const titleItem = this.data.find(item => item.value === 'title');
					if (titleItem) {
							this.title = titleItem.label;
					}

					// 遍历外部传入的data
					this.data.forEach(item => {
							// 排除title项
							if (item.value !== 'title') {
									const index = this.toolbarItems.findIndex(toolbarItem => toolbarItem.value === item.value);
									if (index !== -1) {
											// 如果找到相同value的项，覆盖默认项，如果没有label则保留默认label
											const newItem = { ...this.toolbarItems[index], ...item };
											if (!item.label) {
													newItem.label = this.toolbarItems[index].label;
											}
											this.toolbarItems.splice(index, 1, newItem);
									} else {
											// 如果没有找到，新增项
											this.toolbarItems.push(item);
									}
							}
					});

					// 过滤掉不可见的项
					this.toolbarItems = this.toolbarItems.filter(item => item.visible !== false);
			},
			handleClick(params) {
				this.$emit('toolbarClick', params)
			},
	},
}
</script>

<style scoped lang="less">
.toolbar {
	display: flex;
	height: 45px;
	background: #FFFFFF;
	overflow: hidden;
	white-space: nowrap;
	position: relative;
	z-index: 1;
	box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.1);

	.toolbar-title {
			display: flex;
			justify-content: center;
			align-items: center;

			&::after {
					content: '';
					display: block;
					width: 1px;
					height: 22px;
					background: rgba(0, 0, 0, 0.2);
					margin: 0 16px;
			}

			span {
					height: 20px;
					line-height: 20px;
					font-weight: 600;
					margin-left: 16px;
			}
	}

	.toolbar-btn {
			display: flex;
			justify-content: center;
			align-items: center;
			flex-shrink: 0;

			::v-deep .el-button {
					border: none;
					box-shadow: none;
					border-radius: 0;
					height: 45px;
					margin-left: 0;
					padding: 8px 15px;
					color: rgba(0, 0, 0, 0.85);
					font-size: 14px;

					span {
							display: flex;
							align-items: center;

							svg {
									color: var(--el-color-primary);
									margin-right: 3px;
							}
					}

					&:hover {
							color: white;
							background-color: var(--el-color-primary);

							svg {
									color: white;
							}
					}

					&.is-disabled {
							color: #747474;
							pointer-events: none;
							cursor: default;

							svg {
									color: #747474;
							}
					}
			}
	}
}

/* 去除 Tooltip 的黑色背景 */
.el-tooltip__popper {
  background-color: #fff !important;  /* 使用白色背景 */
  border: 1px solid var(--el-color-primary);  /* 边框颜色 */
  box-shadow: 0 2px 8px var(--el-color-primary);  /* 轻微阴影 */
}

/* 为列表项添加悬浮效果 */
.el-tooltip__popper ul li {
  padding: 8px;
  cursor: pointer;
  list-style-type: none;
  transition: background-color 0.3s ease;
}

.el-tooltip__popper ul li:hover {
  background-color: var(--el-color-primary);  /* 鼠标悬浮时背景色 */
  color: #fff;
}
</style>