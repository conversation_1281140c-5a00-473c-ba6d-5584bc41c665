<style lang="scss" scoped>
#viewerContainer {
  background-color: #e5e5e5;
  width: 100%;
  height: calc(100vh - 280px);
  //height: calc(100vh - 400px);
}

body,
html {
  background-color: white;
}
</style>

<template>
  <div id="viewerContainer" />
</template>

<script>
import '@grapecity/ar-viewer/dist/jsViewer.min.js'
import '@grapecity/ar-viewer/dist/jsViewer.min.css'

export default {
  name: 'ArViewer',
  data() {
    return {
      viewer: null,
      isFullScreen: true,
      reportId: null,
    }
  },
  mounted: function () {
    this.initViewer();
  },
  methods: {
    initViewer() {
      let ActiveReports = window.GrapeCity.ActiveReports
      console.log(ActiveReports, 'ActiveReports')
      this.viewer = new ActiveReports.JSViewer.create({ // eslint-disable-line
        element: '#viewerContainer',
        reportService: {
          url: '/arc/api/reporting'
        },
        settings: {
          zoomType: 'FitPage'
        },
        documentLoaded: () => {
          if (this.isFullScreen) {
            const fullButton = document.getElementById('main_toolbar_Item_14').children[0]
            fullButton.click() // 从打印按钮进来则全屏幕展示
          }
        },
        reportLoaded() {
          console.log('打印')
        }
      })
    },
    // 设计器编辑后再预览，需要重新调用openReport
    openReportEvent(id) {
      this.reportId = id
      if (this.viewer) {
        if (this.isFullScreen) {
          const fullButton = document.getElementById('main_toolbar_Item_14').children[0]
          fullButton.click() // 从打印按钮进来则全屏幕展示
        }
        this.viewer.openReport(id)
      }
    }
  }
}
</script>
