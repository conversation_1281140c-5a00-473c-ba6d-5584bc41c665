<template>
  <ReportTable ref="reportTable"
      v-bind="vTableProps"
      @dataLoaded="handleDataLoaded"
    />
</template>

<script>
import ReportTable from '@/components/amtxts/BaseTable/vGridReport.vue'

export default {
  name: 'ReportView',
  components: {
    ReportTable
  },
  data() {
    return {
      vTableProps:{
        RPTNAME: 'mes.prdt',
      }
    }
  },
  methods: {
    // 数据加载完成事件
    handleDataLoaded(data) {
      console.log('报表数据加载完成:', data.length)
    },
    
  }
}
</script>
