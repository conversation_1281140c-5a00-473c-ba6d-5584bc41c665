<template>
  <div>
    <a-modal :title="title" destroyOnClose width="65%" :visible.sync="visible" @cancel="onClose" :footer="null">
      <div style="margin-bottom: 10px" v-if="isShow">
        <a-form layout="inline">
          <a-form-item :label="$t('zlsq.fileNo')">
            <a-input v-model="fileNo" :placeholder="$t('zlsq.placeholder.fileNo')" />
          </a-form-item>
          <a-form-item>
            <a-checkbox v-model="check"> {{ $t('zlsq.isNew') }} </a-checkbox>
          </a-form-item>
          <a-form-item>
            <a-button type="primary" @click="handleQuery">{{ $t('public.query') }}</a-button>
          </a-form-item>
        </a-form>
      </div>
      <div style="margin: 5px 0px" v-if="!isShow">
        <a-row>
          <a-col :span="12">
            <a-button type="danger" :disabled="errorFileNoList.length <= 0" @click="exportErrorData">
              导出处理失败数据
            </a-button>
          </a-col>
          <a-col :span="12" style="text-align: right">
            <span style="color: red">存在{{ errorFileNoList.length }}条数据处理失败</span>
          </a-col>
        </a-row>
      </div>
      <vxe-table
        border
        resizable
        stripe
        highlight-current-row
        show-overflow
        highlight-hover-row
        export-config
        size="mini"
        ref="xTable1"
        :max-height="400"
        :loading="loading"
        :data="tableData"
        :keyboard-config="{ isArrow: true }"
        :edit-config="{ trigger: 'click', mode: 'row', showStatus: true }"
      >
        <vxe-table-column type="checkbox" width="50" align="center"></vxe-table-column>
        <vxe-table-column field="fileNo" title="zlsq.fileNo" align="center"></vxe-table-column>
        <vxe-table-column field="verNo" title="zlsq.verNo" align="center"></vxe-table-column>
        <vxe-table-column field="fileName" title="zlsq.fileName" align="center"></vxe-table-column>
        <vxe-table-column field="gvFang" title="zlsq.gvFang" align="center"></vxe-table-column>
        <vxe-table-column field="lvFang" title="zlsq.lvFang" align="center"></vxe-table-column>
        <vxe-table-column field="map" title="zlsq.map" align="center"></vxe-table-column>
        <vxe-table-column field="zhCount" title="zlsq.zhCount" align="center"></vxe-table-column>
        <vxe-table-column field="printId" title="zlsq.printId" align="center">
          <template v-slot="scope">
            <a-tag color="blue" type="primary">{{ scope.row.printId === 'Y' ? $t('public.T') : $t('public.F') }}</a-tag>
          </template>
        </vxe-table-column>
        <vxe-table-column field="rem" title="zlsq.r" align="center"></vxe-table-column>
      </vxe-table>
      <vxe-pager
        :loading="loading"
        :current-page="tablePage.currentPage"
        :page-size="tablePage.pageSize"
        :total="tablePage.total"
        :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
        @page-change="handlePageChange"
      >
      </vxe-pager>

      <a-row :gutter="16">
        <a-col class="gutter-row" :span="12" style="text-align: right">
          <a-button type="primary" :loading="hadeOkLoading" @click="hadeOk($event)">确定</a-button>
        </a-col>
        <a-col class="gutter-row" :span="12" style="text-align: left">
          <a-button @click="visible = false">取消</a-button>
        </a-col>
      </a-row>

      <vxe-table v-show="false" size="small" :export-config="{}" ref="xTable2" :data="errorFileNoList">
        <vxe-table-column field="fileNo" title="文件编号" align="center"></vxe-table-column>
      </vxe-table>
    </a-modal>
  </div>
</template>

<script>
import { pageByFileNos } from '@/api/srm/wjff'
export default {
  props: ['data'],
  data() {
    return {
      tableData: [],
      fileNos: [],
      errorFileNoList: [],
      title: '',
      fileNo: '',
      visible: false,
      check: true,
      isShow: false,
      loading: false,
      hadeOkLoading: false,
      tablePage: {
        currentPage: 1,
        pageSize: 100,
        total: 0,
      },
      tableExport: {
        // 默认选中类型
        type: 'xlsx'
      },
    }
  },
  methods: {
    create(obj) {
      this.title = obj.title
      this.isShow = obj.isShow
      this.fileNo = obj.fileNo
      if (obj.fileNos) this.fileNos = obj.fileNos // excel解析出来的文件编号
      if (obj.isShow) this.getList()
      else this.getExcelList()
      this.visible = true
    },
    getList() {
      this.loading = true
      this.tableData = []
      this.errorFileNoList = []
      pageByFileNos({
        current: this.tablePage.currentPage,
        size: this.tablePage.pageSize,
        lastFlag: this.check == true ? '1' : '0',
        excelFlag: '0',
        fileNos: this.fileNo == '' || this.fileNo == undefined ? [] : [this.fileNo]
      })
        .then((res) => {
          if (res.data.errorFileNoList != null && res.data.errorFileNoList != []) {
            let error = res.data.errorFileNoList
            error.forEach((i) => {
              this.errorFileNoList.push(Object.assign({}, { fileNo: i }))
            })
          }
          if (res.data.ipage != null) {
            this.tableData = res.data.ipage.records
            this.tablePage.total = res.data.ipage.total
          } else {
            this.tableData = []
          }
          this.loading = false
        })
        .catch((err) => {
          this.loading = false
          this.tableData = []
          this.errorFileNoList = []
          this.requestFailed(err)
        })
    },
    getExcelList() {
      this.loading = true
      this.tableData = []
      this.errorFileNoList = []
      pageByFileNos({
        current: this.tablePage.currentPage,
        size: this.tablePage.pageSize,
        lastFlag: '1',
        excelFlag: '1',
        fileNos: this.fileNos
      })
        .then((res) => {
          this.tableData = res.data.ipage.records
          if (res.data.errorFileNoList != [] && res.data.errorFileNoList != null) {
            let error = res.data.errorFileNoList
            error.forEach((i) => {
              this.errorFileNoList.push(Object.assign({}, { fileNo: i }))
            })
          }
          this.tablePage.total = res.data.ipage.total
          this.loading = false
        })
        .catch((err) => {
          this.loading = false
          this.tableData = []
          this.errorFileNoList = []
          this.requestFailed(err)
        })
    },
    handleQuery() {
      this.tablePage.currentPage = 1
      this.getList()
    },
    handlePageChange({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      if (this.isShow) this.getList()
      else this.getExcelList()
    },
    onClose() {
      this.tableData = []
      this.errorFileNoList = []
      this.loading = false
      this.visible = false
    },
    hadeOk(e) {
      let target = e.target
      if (target.nodeName == 'SPAN' || target.nodeName == 'I') {
        target = e.target.parentNode
      }
      target.blur()
      let selectRecords = this.$refs.xTable1.getCheckboxRecords()
      if (selectRecords == [] || selectRecords.length == 0) return this.$message.warning('请至少选择一条数据！')
      let itemJson = []
      if (this.data != [] && this.data.length != 0) {
        this.data.forEach((i) => {
          selectRecords.forEach((j) => {
            if (i.fileNo + i.verNo == j.fileNo + j.verNo) {
              itemJson.push(j.fileNo)
            }
          })
        })
      }
      if (itemJson == [] || itemJson.length == 0) {
        this.$emit('searchList', selectRecords)
        this.visible = false
      } else {
        this.$notification['warn']({
          message: '警告',
          description: '重复单据' + ' ' + itemJson.join(','),
          duration: 5,
        })
      }
    },
    exportErrorData() {
      this.$refs.xTable2.exportData({ type: 'csv' })
    }
  }
}
</script>