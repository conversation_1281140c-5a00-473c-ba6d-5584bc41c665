<template>
  <div class="zl_sq">
    <a-card :bordered="false">
      <a-card :bordered="true">
        <a-form layout="inline">
          <a-row>
            <a-form-item :label="$t('zlsq.sqNo')">
              <a-input
                v-model="queryParam.sqNo"
                :placeholder="$t('zlsq.placeholder.sqNo')"
                :readOnly="true"
                style="width: 180px"
              />
            </a-form-item>

            <a-form-item :label="$t('zlsq.fileNo')">
              <a-input-search
                v-model="queryParam.fileNo"
                @search="fileNoSearch"
                :placeholder="$t('zlsq.placeholder.fileNo')"
                :readOnly="save"
                style="width: 200px"
              >
                <a-tooltip
                  slot="suffix"
                  title="清空"
                  v-show="!save"
                >
                  <a-icon
                    v-show="!save"
                    @click="queryParam.fileNo = ''"
                    type="close-circle"
                    style="color: rgba(0, 0, 0, 0.45)"
                  />
                </a-tooltip>
                <a-button
                  ref="btn"
                  slot="enterButton"
                  :disabled="save"
                >
                  <a-icon type="database" />
                </a-button>
              </a-input-search>
            </a-form-item>
            <a-form-item>
              <a-button
                style="margin-bottom: 15px"
                type="primary"
                @click="handReset()"
                v-permission="tz_application_reset"
              >{{ $t('public.add') }}</a-button>
              <a-button
                style="margin-left: 10px"
                type="danger"
                @click="handSave()"
                :disabled="save"
                v-permission="tz_application_save"
              >{{ $t('public.save') }}</a-button>
              <a-button
                style="margin-left: 10px"
                type="primary"
                @click="handleFind()"
                v-permission="tz_application_search"
              >{{ $t('public.query') }}</a-button>
              <a-button
                style="margin-left: 10px"
                type="primary"
                @click="handDelete()"
                v-permission="tz_application_del"
              >{{ $t('public.delete') }}</a-button>
              <div
                class="upload_excel"
                v-permission="tz_application_excel"
              >
                <el-upload
                  class="upload"
                  accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
                  :action="UploadUrl()"
                  :show-file-list="false"
                  :auto-upload="false"
                  :before-upload="beforeUpload"
                  :on-change="importExcel"
                  :multiple="false"
                >
                  <a-button type="primary">{{ $t('zlsq.excelExport') }}</a-button>
                </el-upload>
              </div>
            </a-form-item>
            <!-- <a-col
              :md="6"
              :sm="24"
            >
              <a-form-item :label="$t('zlsq.sqDd')">
                <a-date-picker
                  style="width:100%"
                  v-model="queryParam.sqDd"
                  :placeholder="$t('zlsq.placeholder.sqDd')"
                />
              </a-form-item>
            </a-col> -->

            <!-- <a-col
              :md="6"
              :sm="24"
            >
              <a-form-model-item :label="$t('zlsq.bilNo')">
                <my-selectList
                  url="/srm/fafang/findall"
                  :tableColumn="$Column.srmfindall"
                  :form="$Form.srmfindall"
                  :data="data.bilNo"
                  allowClear
                  name="bilNo"
                  @choose="choose"
                  ref="selectList"
                  :placeholder="$t('zlsq.placeholder.bilNo')"
                ></my-selectList>
              </a-form-model-item>
            </a-col> -->

            <!-- <a-col
              :md="6"
              :sm="24"
            >
              <a-form-item :label="$t('zlsq.cusName')">
                <a-input
                  v-model="queryParam.cusName"
                  :placeholder="$t('zlsq.placeholder.cusName')"
                  :readOnly="true"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="48">
            <a-col
              :md="6"
              :sm="24"
            >
              <a-form-item :label="$t('zlsq.fileNo')">
                <a-input
                  v-model="queryParam.fileNo"
                  :placeholder="$t('zlsq.placeholder.fileNo')"
                  :readOnly="true"
                />
              </a-form-item>
            </a-col> -->

            <!-- <a-col
              :md="6"
              :sm="24"
            >
              <a-form-item :label="$t('zlsq.prdNo')">
                <my-selectList
                  url="/srm/fafang/findprdnos"
                  :tableColumn="$Column.srmfindprdnos"
                  :form="$Form.srmfindprdnos"
                  :data="data2.prdNo"
                  allowClear
                  name="prdNo"
                  @choose="choose"
                  ref="selectList"
                  :placeholder="$t('zlsq.prdNo')"
                ></my-selectList>
              </a-form-item>
            </a-col> -->
          </a-row>
        </a-form>
      </a-card>

      <div>
        <a-row :gutter="24">
          <a-col>
            <vxe-toolbar custom>
              <template v-slot:buttons>
                <a-dropdown
                  :trigger="['click']"
                  :disabled="save"
                  v-permission="tz_application_dropdowndel"
                >
                  <a-button>{{ $t('public.action') }}
                    <a-icon type="down" />
                  </a-button>
                  <a-menu slot="overlay">
                    <a-menu-item key="0">
                      <a @click="handleDel('del')">{{ $t('public.delete') }}</a>
                    </a-menu-item>
                  </a-menu>
                </a-dropdown>
              </template>
            </vxe-toolbar>
            <vxe-table
              border
              resizable
              stripe
              highlight-current-row
              show-overflow
              highlight-hover-row
              export-config
              ref="xTable"
              size="mini"
              :max-height="tableHeight"
              :loading="loading"
              :data="tableData"
              :keyboard-config="{ isArrow: true }"
              :edit-config="{ trigger: 'click', mode: 'cell', showStatus: true }"
            >
              <vxe-table-column
                fixed="left"
                type="checkbox"
                width="50"
                align="center"
              ></vxe-table-column>
              <vxe-table-column
                field="fileNo"
                title="zlsq.fileNo"
                align="center"
              ></vxe-table-column>
              <vxe-table-column
                field="verNo"
                title="zlsq.verNo"
                align="center"
              ></vxe-table-column>
              <vxe-table-column
                field="fileName"
                title="browsing.fileName"
                align="center"
              ></vxe-table-column>
              <vxe-table-column
                field="gvFang"
                title="zlsq.gvFang"
                align="center"
              ></vxe-table-column>
              <!-- :edit-render="{name: 'input'}" -->
              <vxe-table-column
                field="lvFang"
                title="zlsq.lvFang"
                align="center"
              ></vxe-table-column>
              <!-- :edit-render="{name: 'input'}" -->
              <vxe-table-column
                field="map"
                title="zlsq.map"
                align="center"
              ></vxe-table-column>
              <vxe-table-column
                field="zhCount"
                title="zlsq.zhCount"
                align="center"
              ></vxe-table-column>
              <vxe-table-column
                field="printId"
                title="zlsq.printId"
                align="center"
              >
                <template v-slot="scope">
                  <a-tag
                    color="blue"
                    type="primary"
                    @click="handleTagClick(scope.row)"
                  >{{
                    scope.row.printId === 'Y' ? $t('public.T') : $t('public.F')
                  }}</a-tag>
                </template>
              </vxe-table-column>
              <!-- Y代表是 N代表否 -->
              <!-- T代表是 F代表否 -->
              <vxe-table-column
                field="rem"
                title="zlsq.r"
                align="center"
              ></vxe-table-column>
            </vxe-table>
            <vxe-pager
              :loading="loading"
              :current-page="tablePage.currentPage"
              :page-size="tablePage.pageSize"
              :total="tablePage.total"
              :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
              @page-change="handlePageChange"
            >
            </vxe-pager>
            <Drawer
              ref="Drawer"
              @handleOk="handleSave"
            />
          </a-col>
        </a-row>
      </div>
      <Modal
        ref="modal"
        @getSaveList="getSaveList"
      />
      <SearchModal
        @searchList="searchList"
        :data="tableData"
        ref="SearchModal"
      />
    </a-card>
  </div>
</template>

<script>
import { getSqNo, add, queryFileNo, queryprdNo, findHeadBody, delhb } from '@/api/srm/wjff'
import MySelectList from '@/components/MySelectList'
import moment from 'moment'
import Drawer from './drawer'
import Modal from './modal'
import SearchModal from './flienosearch'
export default {
  components: {
    Drawer,
    MySelectList,
    Modal,
    SearchModal,
  },
  data () {
    return {
      tz_application_reset: 'tz_application_reset',
      tz_application_save: 'tz_application_save',
      tz_application_search: 'tz_application_search',
      tz_application_del: 'tz_application_del',
      tz_application_excel: 'tz_application_excel',
      tz_application_dropdowndel: 'tz_application_dropdowndel',
      loading: false,
      save: false,
      queryParam: {
        sqDd: moment(new Date(), 'YYYY-MM-DD'),
        sqNo: '',
        cusNo: '',
        cusName: '',
        bilNo: '',
        bilId: '',
        fileNo: '',
        prdNo: '',
      },
      tableData: [],
      fileNos: [],
      tableHeight: window.innerHeight - 320,
      tablePage: {
        currentPage: 1,
        pageSize: 100,
        total: 0,
      },
      query: {
        current: 1,
        size: 100,
      },
      data: {
        bilNo: '',
      },
      data2: {
        prdNo: '',
      },
      validRules: {
        gvFang: [{ required: true, message: 'G番必须填写' }, { validator: gvFangValid }],
        lvFang: [{ required: true, message: 'L番必须填写' }, { validator: lvFangValid }],
      },
    }
    const gvFangValid = ({ cellValue }) => {
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          if (!cellValue || cellValue.length == 0) {
            reject(new Error('G番必须填写'))
          } else {
            resolve()
          }
        }, 20)
      })
    }
    const lvFangValid = ({ cellValue }) => {
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          if (!cellValue || cellValue.length == 0) {
            reject(new Error('L番必须填写'))
          } else {
            resolve()
          }
        }, 20)
      })
    }
  },
  created () {
    this.getsqNo()
  },
  mounted () {
    window.onresize = () => {
      return (() => {
        this.tableHeight = window.innerHeight - 320
      })()
    }
  },
  methods: {
    // 获取申请单号
    getsqNo () {
      getSqNo()
        .then((res) => {
          this.queryParam.sqNo = res.data
        })
        .catch((err) => this.requestFailed(err))
        .finally(() => { })
    },
    // 分页
    handlePageChange ({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      // this.getSaveList(this.id)
    },
    fileNoSearch () {
      let obj = {
        title: '查询',
        isShow: true,
        fileNo: this.queryParam.fileNo,
      }
      this.$refs.SearchModal.create(obj)
    },
    UploadUrl () {
      return ''
    },
    beforeUpload (file) {
      const Xls = file.name.split('.')
      const islt20 = file.size / 1024 / 1024 < 20
      if (Xls[1] == 'xls' || Xls[1] == 'xlsx') {
      } else {
        this.$message.warning('只能上传xls或者xlsx格式的文件')
        return
      }
      if (!islt20) {
        this.$message.warning('文件大小不能超过20M')
        return
      }
      return true
    },
    importExcel (param) {
      let _this = this
      _this
        .file2Xce(param)
        .then((item) => {
          if (item && item.length > 0) {
            if (item[0] && item[0].sheet && item[0].sheet.length) {
              let data = item[0].sheet
              let arr = []
              data.forEach((i) => {
                arr.push(Object.values(i))
              })
              _this.fileNos = [].concat.apply([], arr)
              let obj = {
                title: '导入',
                isShow: false,
                fileNo: _this.queryParam.fileNo,
                fileNos: _this.fileNos,
              }
              _this.$refs.SearchModal.create(obj)
            }
          }
        })
        .catch((error) => { })
    },
    file2Xce (file) {
      return new Promise(function (resolve, reject) {
        const reader = new FileReader()
        reader.onload = function (e) {
          const data = e.target.result
          this.wb = XLSX.read(data, {
            type: 'binary',
          })
          const result = []
          this.wb.SheetNames.forEach((sheetName) => {
            result.push({
              sheetName: sheetName,
              sheet: XLSX.utils.sheet_to_json(this.wb.Sheets[sheetName]),
            })
          })
          resolve(result)
        }
        reader.readAsBinaryString(file.raw)
      })
    },
    choose (obj, clear) {
      if (obj.obj.name === 'bilNo') {
        this.data[obj.obj.name] = clear == undefined ? obj.obj.data.bilNo : ''
        this.queryParam[obj.obj.name] = clear == undefined ? obj.obj.data.bilNo : ''
        this.queryParam.bilId = clear == undefined ? obj.obj.data.bilId : ''
        if (clear) return false
        this.loading = true
        queryFileNo({
          current: this.query.current,
          size: this.query.size,
          bilNo: obj.obj.data.bilNo,
        })
          .then((res) => {
            this.loading = false
            this.queryParam.cusNo = res.data.records[0].cusNo
            this.queryParam.cusName = res.data.records[0].cusName
            this.tableData = Object.assign(res.data.records)
            this.tablePage.total = res.data.total
            this.tablePage.currentPage = res.data.current
            this.tableData.forEach((item) => {
              item.printId = 'N'
              item.gvFang = ''
              item.lvFang = ''
              item.map = ''
              item.rem = ''
            })
          })
          .catch((e) => {
            this.requestFailed(e)
            this.loading = false
            this.tableData = []
          })
      } else if (obj.obj.name === 'prdNo') {
        this.queryParam[obj.obj.name] = clear === undefined ? obj.obj.data.prdNo : ''
        this.data2.prdNo = clear === undefined ? obj.obj.data.prdNo : ''
        if (clear) return false
        this.loading = true
        queryprdNo({
          current: this.query.current,
          size: this.query.size,
          prdNo: obj.obj.data.prdNo,
        })
          .then((res) => {
            this.loading = false
            if (res.data != null) {
              this.tableData = [res.data]
              this.tableData.forEach((item) => {
                item.printId = 'N'
                item.gvFang = ''
                item.lvFang = ''
                item.map = ''
                item.rem = ''
              })
            }
          })
          .catch((e) => {
            this.requestFailed(e)
            this.loading = false
            this.tableData = []
          })
      }
    },
    async handSave () {
      const that = this
      let form = {
        sqDd: moment(this.queryParam.sqDd).format('YYYY-MM-DD HH:mm:ss'),
        sqNo: this.queryParam.sqNo,
        cusNo: this.queryParam.cusNo,
        cusName: this.queryParam.cusName,
        bilNo: this.queryParam.bilNo,
        bilId: this.queryParam.bilId,
        fileNo: this.queryParam.fileNo,
        prdNo: this.queryParam.prdNo,
      }
      if (this.tableData.length > 0) {
        let obj = {
          ...form,
          tfApplySlList: this.tableData,
        }
        this.$confirm({
          title: this.$t('public.del.title'),
          content: this.$t('invoice.file'),
          okText: this.$t('public.sure'),
          okType: 'warn',
          cancelText: this.$t('public.cancel'),
          onOk () {
            add(obj)
              .then((res) => {
                if (res) {
                  that.$message.success(that.$t('public.success'))
                  that.del()
                }
              })
              .catch((err) => that.requestFailed(err))
              .finally(() => {
                that.loading = false
              })
          },
          onCancel () { },
        })
      } else {
        this.$notification['warn']({
          message: this.$t('public.message'),
          description: '请先添加表身数据后保存单据！',
        })
      }
    },
    del () {
      this.queryParam = {}
      this.tableData = []
      this.data.bilNo = ''
      this.queryParam.sqDd = moment(new Date(), 'YYYY-MM-DD')
    },
    cellDBLClickEvent ({ row, rowIndex }) {
      this.$refs.Drawer.edit({ title: this.$t('public.Detailed') }, row, rowIndex)
    },
    handleSave ({ rowIndex, form }) {
      this.$set(this.tableData, rowIndex, form)
    },
    handleFind () {
      this.$refs.modal.create({
        title: '单据查询',
      })
    },
    getSaveList (sqNo) {
      findHeadBody({
        current: 1,
        size: 100,
        sqNo: sqNo,
      })
        .then((res) => {
          let date = res.data.records[0].sqDd
          this.queryParam.sqDd = moment(date, 'YYYY-MM-DD')
          this.queryParam.sqNo = res.data.records[0].sqNo
          this.queryParam.cusNo = res.data.records[0].cusNo
          this.queryParam.cusName = res.data.records[0].cusName
          this.queryParam.bilNo = res.data.records[0].bilNo
          this.data.bilNo = res.data.records[0].bilNo
          this.queryParam.bilId = res.data.records[0].bilId
          this.queryParam.fileNo = res.data.records[0].fileNo
          this.queryParam.prdNo = res.data.records[0].prdNo
          this.tableData = res.data.records[0].tfApplySlList
          this.tablePage.total = res.data.records[0].tfApplySlList.length
          this.save = true
        })
        .catch((err) => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    handReset () {
      this.save = false
      this.resetQueryObj()
      this.data.bilNo = ''
      this.tableData = []
      this.getsqNo()
    },
    handDelete () {
      const that = this
      this.$confirm({
        title: this.$t('public.del.title'),
        content: this.$t('identifier.delcont'),
        okText: this.$t('public.sure'),
        okType: 'warn',
        cancelText: this.$t('public.cancel'),
        onOk () {
          that.loading = true
          delhb({
            sqNo: that.queryParam.sqNo,
          })
            .then((res) => {
              that.loading = false
              if (res) {
                if (res.data == 0) {
                  that.handReset()
                  that.$message.success(that.$t('public.success'))
                } else {
                  that.$message.error('文件已审核不能删除!')
                }
              }
            })
            .catch((err) => {
              that.loading = false
              that.requestFailed(err)
            })
        },
        onCancel () { },
      })
    },
    resetQueryObj () {
      this.queryParam = {
        sqDd: moment(new Date(), 'YYYY-MM-DD'),
        sqNo: '',
        cusNo: '',
        cusName: '',
        bilNo: '',
        bilId: '',
        fileNo: '',
        prdNo: '',
      }
    },
    handleDel () {
      const that = this
      let selectRecords = this.$refs.xTable.getCheckboxRecords()
      if (selectRecords == [] || selectRecords.length == 0) return this.$message.warning('请至少选择一条数据！')
      this.$confirm({
        title: this.$t('public.del.title'),
        content: this.$t('invoice.del'),
        okText: this.$t('public.sure'),
        okType: 'danger',
        cancelText: this.$t('public.cancel'),
        onOk () {
          let newArr = that.tableData.filter((item) => {
            let arrlist = selectRecords.map((item2) => item2)
            return !arrlist.includes(item)
          })
          that.tableData = []
          that.tableData = newArr
        },
        onCancel () { },
      })
    },
    searchList (data) {
      this.tableData.push(...data)
      this.tablePage.total = this.tableData.length
    },
    handleTagClick (row) {
      if (!this.save) row.printId == 'Y' ? (row.printId = 'N') : (row.printId = 'Y')
    }
  }
}
</script>

<style lang="less" scoped>
.zl_sq {
  .ant-card-body {
    padding: 10px !important;
    .ant-form {
      height: 40px;
    }
  }
}
.upload_excel {
  margin-left: 10px;
  float: right;
  /deep/ .el-upload__input {
    display: none;
  }
}
</style>