<template>
  <div>
    <a-modal :title="title" destroyOnClose width="53%" :visible.sync="visible" @cancel="onClose" :footer="null">
      <div>
        <div class="table-page-search-wrapper">
          <a-form layout="inline">
            <a-row>
              <a-col :span="8">
                <a-form-item :label="$t('zlsq.sqNo')">
                  <a-input v-model="queryParam.sqNo" :placeholder="$t('zlsq.sqNo')" style="width: 95%;"/>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item :label="$t('zlsq.sqDd')">
                  <a-date-picker
                    :disabled-date="disabledStartDate"
                    v-model="startDate"
                    format="YYYY-MM-DD"
                    :placeholder="$t('completed.staDd')"
                    @openChange="handleStartOpenChange"
                    style="width: 45%;"
                  />
                  <a-date-picker
                    v-model="endDate"
                    :disabled-date="disabledEndDate"
                    format="YYYY-MM-DD"
                    :placeholder="$t('completed.endDd')"
                    :open="endOpen"
                    @openChange="handleEndOpenChange"
                    style="width: 45%;margin-left: 6px;"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="4">
                <span class="table-page-search-submitButtons">
                  <a-button type="primary" @click="getList">{{ $t('public.query') }}</a-button>
                  <a-button style="margin-left: 8px" @click="reset">{{ $t('public.reset') }}</a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
        <vxe-table
          border
          resizable
          stripe
          highlight-current-row
          show-overflow
          highlight-hover-row
          export-config
          ref="xTable"
          size="mini"
          :loading="loading"
          :data="tableData"
          @cell-dblclick="cellDBLClickEvent"
          :keyboard-config="{ isArrow: true }"
          :edit-config="{ trigger: 'click', mode: 'row' }"
        >
          <vxe-table-column field="sqNo" title="zlsq.sqNo" align="center"></vxe-table-column>
          <vxe-table-column field="sqDd" title="zlsq.sqDd" align="center"></vxe-table-column>
        </vxe-table>
        <vxe-pager
          :loading="loading"
          :current-page="tablePage.currentPage"
          :page-size="tablePage.pageSize"
          :total="tablePage.total"
          :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
          @page-change="handlePageChange"
        >
        </vxe-pager>
      </div>
    </a-modal>
  </div>
</template>

<script>
import Vue from 'vue'
import { findSaveList } from '@/api/srm/wjff'
import moment from 'moment'

export default {
  name: 'FileModel',
  data() {
    return {
      title: '',
      visible: false,
      endDate: '',
      startDate: '',
      endOpen: false,
      modeType: '',
      queryParam: {},
      loading: false,
      formStatus: true,
      confirmLoading: false,
      fileList: [],
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      tableData: [],
      row: {}
    }
  },
  watch: {
    startDate(val) {
      if (val) {
        this.queryParam.startDate = moment(val).format('YYYY-MM-DD 00:00:00')
      } else {
        this.queryParam.startDate = undefined
      }
    },
    endDate(val) {
      if (val) {
        this.queryParam.endDate = moment(val).format('YYYY-MM-DD 23:59:59')
      } else {
        this.queryParam.endDate = undefined
      }
    }
  },
  methods: {
    disabledStartDate(startValue) {
      const endValue = this.endDate
      if (!startValue || !endValue) {
        return false
      }
      return startValue.valueOf() > endValue.valueOf()
    },
    disabledEndDate(endValue) {
      const startValue = this.startDate
      if (!endValue || !startValue) {
        return false
      }
      return startValue.valueOf() >= endValue.valueOf()
    },
    handleStartOpenChange(open) {
      if (!open) {
        this.endOpen = true
      }
    },
    handleEndOpenChange(open) {
      this.endOpen = open
    },
    getList() {
      this.loading = true
      findSaveList(
        Object.assign(
          {
            current: this.tablePage.currentPage,
            size: this.tablePage.pageSize
          },
          this.queryParam
        )
      )
        .then(res => {
          this.tableData = res.data.records
          this.tablePage.total = res.data.total
          this.tablePage.currentPage = res.data.current
          this.loading = false
        })
        .catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    reset() {
      this.endDate = null
      this.startDate = null
      this.queryParam = {}
    },
    onClose() {
      this.tableData = []
      this.loading = false
      this.visible = false
      this.modeType = ''
    },
    cellDBLClickEvent({ row }) {
      this.$emit('getSaveList', row.sqNo)
      this.visible = false
    },
    // 添加弹框
    create(model) {
      this.getList()
      this.modeType = '0'
      this.title = model.title
      this.visible = true
      this.fileList = []
    },
    // 点击编辑按钮
    handleMenuClick() {
      this.modeType = '2'
      this.title = this.$t('public.edit')
      this.formStatus = true
    },
    handlePageChange({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getList()
    }
  }
}
</script>
