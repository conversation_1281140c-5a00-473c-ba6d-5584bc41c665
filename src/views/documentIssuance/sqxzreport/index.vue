<template>
  <a-card :bordered="false">
    <a-form layout="inline">
      <a-row>
        <a-form-item :label="$t('zlsq.sqDd')">
          <a-date-picker
            v-model="startDd"
            :disabled-date="disabledStartDate"
            format="YYYY-MM-DD"
            :placeholder="$t('datadown.startD')"
            @openChange="handleStartOpenChange"
            style="width: 150px"
          />
          <a-date-picker
            v-model="endDd"
            :disabled-date="disabledEndDate"
            format="YYYY-MM-DD"
            :placeholder="$t('datadown.endD')"
            :open="endOpen"
            @openChange="handleEndOpenChange"
            style="margin-left: 8px; width: 150px"
          />
        </a-form-item>
        <a-form-item :label="$t('zlsq.sqNo')">
          <a-input v-model="queryParam.sqNo" :placeholder="$t('zlsq.sqNo')" />
        </a-form-item>
        <a-form-item :label="$t('zlsq.fileNo')">
          <a-input v-model="queryParam.fileNo" :placeholder="$t('zlsq.fileNo')" />
        </a-form-item>
        <a-form-item>
          <a-button style="margin-left: 10px" type="primary" @click="haandleQuery()" v-permission="tz_sqxzreport_search">{{ $t('public.query') }}</a-button>
          <a-button style="margin-left: 10px" type="primary" @click="handleReset()" v-permission="tz_sqxzreport_reset">{{ $t('public.reset') }}</a-button>
        </a-form-item>
      </a-row>
    </a-form>
    <div style="margin-top: 10px;">
      <vxe-toolbar custom>
        <template v-slot:buttons>
          <a-dropdown :trigger="['click']" v-permission="tz_sqxzreport_export">
            <a-button
              >{{ $t('public.action') }}
              <a-icon type="down" />
            </a-button>
            <a-menu slot="overlay">
              <a-menu-item key="0">
                <a @click="dropdownMenuEvent('export')">{{ $t('submission.export') }}</a>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </template>
      </vxe-toolbar>
      <vxe-table
        border
        resizable
        stripe
        highlight-current-row
        show-overflow
        highlight-hover-row
        export-config
        ref="xTable"
        size="mini"
        :max-height="tableHeight"
        :loading="loading"
        :data="tableData"
        :keyboard-config="{ isArrow: true }"
        :edit-config="{ trigger: 'click', mode: 'row' }"
      >
        <vxe-table-column fixed="left" field="sqNo" title="zlsq.sqNo" align="center" :width="150"></vxe-table-column>
        <vxe-table-column
          field="sqDd"
          title="zlsq.sqDd"
          align="center"
          :width="150"
          :formatter="['formatDate', 'yyyy-MM-dd']"
        ></vxe-table-column>
        <vxe-table-column
          field="downloadTime"
          title="datadown.downTime"
          align="center"
          :width="150"
          :formatter="['formatDate', 'yyyy-MM-dd']"
        ></vxe-table-column>
        <vxe-table-column field="cusName" title="zlsq.cusName" align="center" :width="150"></vxe-table-column>
        <vxe-table-column field="fileNo" title="zlsq.fileNo" align="center" :width="150"></vxe-table-column>
        <vxe-table-column field="verNo" title="zlsq.verNo" align="center" :width="150"></vxe-table-column>
        <vxe-table-column field="zhCount" title="zlsq.zhCount" align="center" :width="150"></vxe-table-column>
        <vxe-table-column field="map" title="zlsq.map" align="center" :width="150"></vxe-table-column>
        <vxe-table-column field="gvFang" title="zlsq.gvFang" align="center" :width="150"></vxe-table-column>
        <vxe-table-column field="lvFang" title="zlsq.lvFang" align="center" :width="150"></vxe-table-column>
        <vxe-table-column title="zlsq.printId" align="center" :width="150">
          <template v-slot="scope">
            <a-tag color="blue" type="primary">{{ scope.row.printId === 'Y' ? $t('public.T') : $t('public.F') }}</a-tag>
          </template>
        </vxe-table-column>
        <vxe-table-column field="rem" title="zlsq.r" align="center" :width="150"></vxe-table-column>
      </vxe-table>
      <vxe-pager
        :loading="loading"
        :current-page="tablePage.currentPage"
        :page-size="tablePage.pageSize"
        :total="tablePage.total"
        :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
        @page-change="handlePageChange"
      >
      </vxe-pager>
    </div>
    <exportExcel ref="exportExcel" />
  </a-card>
</template>

<script>
import { downDetail } from '@/api/fm/grantreport'
import exportExcel from '@/components/ExportExcel/ExportExcel'
import moment from 'moment'
import axios from 'axios'
export default {
  components: {
    exportExcel,
  },
  data() {
    return {
      tz_sqxzreport_search: 'tz_sqxzreport_search',
      tz_sqxzreport_reset: 'tz_sqxzreport_reset',
      tz_sqxzreport_export: 'tz_sqxzreport_export',
      startDd: null,
      endDd: null,
      queryParam: {
        startDate: moment(new Date()).format('YYYY-MM-DD 00:00:00'),
        endDate: moment(new Date()).format('YYYY-MM-DD 23:59:59'),
        sqNo: '',
        fileNo: '',
      },
      tableExport: {
        // 默认选中类型
        type: 'xlsx',
      },
      tableData: [],
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
      ipqqqq: '',
      endOpen: false,
      loading: false,
      tableHeight: window.innerHeight - 350,
    }
  },
  created() {
    this.startDd = moment(new Date())
    this.endDd = moment(new Date())
    this.getList()
  },
  mounted() {
    window.onresize = () => {
      return (() => {
        this.tableHeight = window.innerHeight - 350
      })()
    }
  },
  watch: {
    'queryParam.invalidId': {
      handler(val, oldval) {
        this.getList()
      },
      deep: true, // 对象内部的属性监听，也叫深度监听
    },
    startDd(val) {
      if (val) {
        this.queryParam.startDate = moment(val).format('YYYY-MM-DD 00:00:00')
      } else {
        this.queryParam.startDate = undefined
      }
    },
    endDd(val) {
      if (val) {
        this.queryParam.endDate = moment(val).format('YYYY-MM-DD 23:59:59')
      } else {
        this.queryParam.endDate = undefined
      }
    },
  },
  methods: {
    getList() {
      this.loading = true
      downDetail(
        Object.assign(
          {
            current: this.tablePage.currentPage,
            size: this.tablePage.pageSize,
          },
          this.queryParam
        )
      )
        .then((res) => {
          this.tableData = res.data.records
          this.tablePage.total = res.data.total
          this.tablePage.currentPage = res.data.current
          this.loading = false
        })
        .catch((err) => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    // 分页
    handlePageChange({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getList()
    },
    haandleQuery() {
      this.tablePage.currentPage = 1
      this.getList()
    },
    disabledStartDate(startValue) {
      const endValue = this.fbEndDd
      if (!startValue || !endValue) {
        return false
      }
      return startValue.valueOf() > endValue.valueOf()
    },
    disabledEndDate(endValue) {
      const startValue = this.fbStartDd
      if (!endValue || !startValue) {
        return false
      }
      return startValue.valueOf() >= endValue.valueOf()
    },
    handleStartOpenChange(open) {
      if (!open) {
        this.endOpen = true
      }
    },
    handleEndOpenChange(open) {
      this.endOpen = open
    },
    handleReset() {
      this.startDd = null
      this.endDd = null
      this.queryParam.sqNo = ''
      this.queryParam.fileNo = ''
    },
    // 导出
    dropdownMenuEvent(name) {
      switch (name) {
        case 'export': {
          let obj = {
            queryParam: this.queryParam,
            startDd: this.startDd,
            endDd: this.endDd,
            url: '/srm/downdetail/page',
            name: '文件下载明细表',
            eng: [
              'sqNo',
              'sqDd',
              'downloadTime',
              'cusName',
              'fileNo',
              'verNo',
              'zhCount',
              'map',
              'gvFang',
              'lvFang',
              'printId',
              'rem',
            ],
            zh: [
              '申请单号',
              '申请日期',
              '下载时间',
              '供应商',
              '文件编号',
              '版本',
              '张数',
              '图幅',
              'G番',
              'L番',
              '需晒印',
              '摘要',
            ],
          }
          this.$refs.exportExcel.create(obj)
        }
      }
    },
  },
}
</script>

<style>
</style>