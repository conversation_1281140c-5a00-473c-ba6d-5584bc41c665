<template>
  <a-card :bordered="false">
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row>
          <a-col :span="6">
            <a-form-item :label="$t('datadown.date')">
              <a-date-picker
                v-model="startDd"
                :disabled-date="disabledStartDate"
                format="YYYY-MM-DD"
                :placeholder="$t('datadown.startD')"
                @openChange="handleStartOpenChange"
                style="width: 48%"
              />
              <a-date-picker
                v-model="endDd"
                :disabled-date="disabledEndDate"
                format="YYYY-MM-DD"
                :placeholder="$t('datadown.endD')"
                :open="endOpen"
                @openChange="handleEndOpenChange"
                style="width: 48%; margin-left: 8px"
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item :label="$t('datadown.status')">
              <a-select v-model="queryParam.downSta" style="width: 95%">
                <a-select-option value="0"> {{ $t('datadown.NotDownloaded') }} </a-select-option>
                <a-select-option value="1"> {{ $t('datadown.Downloaded') }} </a-select-option>
                <a-select-option value="2"> {{ $t('datadown.all') }} </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item :label="$t('datadown.sqNo')">
              <a-input
                v-model="queryParam.sqNo"
                :placeholder="$t('datadown.sqNo')"
                style="width: 95%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item :label="$t('datadown.prdNo')">
              <a-input
                v-model="queryParam.prdNo"
                :placeholder="$t('datadown.prdNo')"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="48">
          <a-col :xl="8" :lg="8" :md="12" :sm="24"></a-col>
          <a-col :xl="8" :lg="8" :md="12" :sm="24"> </a-col>
          <a-col :xl="8" :lg="8" :md="12" :sm="24" style="text-align: right">
            <a-button style="margin-left: 10px" type="primary" @click="handleQuery()" v-permission="tz_datadown_search">{{ $t('public.query') }}</a-button>
            <a-button style="margin-left: 10px" type="primary" @click="handleReset()" v-permission="tz_datadown_reset">{{
              $t('public.reset')
            }}</a-button>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- <vxe-toolbar custom> </vxe-toolbar> -->
    <div style="margin-top: 10px;">
      <vxe-table
      border
      stripe
      resizable
      size="mini"
      show-overflow
      export-config
      highlight-current-row
      highlight-hover-row
      :max-height="tableHeight"
      :loading="loading"
      :data="tableData"
      :keyboard-config="{ isArrow: true }"
      :edit-config="{ trigger: 'click', mode: 'row' }"
      ref="xTable"
    >
      <vxe-table-column fixed="left" field="sqNo" title="datadown.sqNo" align="center" :width="150"></vxe-table-column>
      <vxe-table-column field="fileNo" title="datadown.fileNo" align="center" :width="150"></vxe-table-column>
      <vxe-table-column field="verNo" title="zlsq.verNo" align="center" :width="80"></vxe-table-column>
      <vxe-table-column field="isDown" title="datadown.isDown" align="center" :width="100">
        <template v-slot="scope">
          <span>{{
            scope.row.isDown === 'T' && scope.row.prCheckSta === '0' ? $t('datadown.isDown') : $t('datadown.noisDown')
          }}</span>
        </template>
      </vxe-table-column>
      <vxe-table-column title="datadown.status" align="center" :width="100">
        <template v-slot="scope">
          <span>{{ scope.row.prCheckSta === '0' ? $t('datadown.prCheckSta') : $t('datadown.noprCheckSta') }}</span>
        </template>
      </vxe-table-column>
      <vxe-table-column field="prdNo" title="datadown.prdNo" align="center" :width="140"></vxe-table-column>
      <vxe-table-column field="prdName" title="datadown.prdName" align="center" :width="150"></vxe-table-column>
      <vxe-table-column field="gvFang" title="zlsq.gvFang" align="center" :width="80"></vxe-table-column>
      <vxe-table-column field="lvFang" title="zlsq.lvFang" align="center" :width="80"></vxe-table-column>
      <vxe-table-column field="map" title="zlsq.map" align="center" :width="80"></vxe-table-column>
      <vxe-table-column field="zhCount" title="zlsq.zhCount" align="center" :width="80"></vxe-table-column>
      <vxe-table-column field="printId" title="zlsq.printId" align="center" :width="80">
        <template v-slot="scope">
          <a-tag color="blue" type="primary">
            {{ scope.row.printId === 'Y' ? $t('public.T') : $t('public.F') }}
          </a-tag>
        </template>
      </vxe-table-column>
      <vxe-table-column field="downloadTime" title="datadown.downTime" align="center" :width="150"></vxe-table-column>
      <vxe-table-column field="rem" title="zlsq.r" align="center" :width="130"></vxe-table-column>
      <vxe-table-column fixed="right" field="prdName" title="datadown.down" align="center" :width="100">
        <template v-slot="scope">
          <a-button size="small" @click="downPdf(scope.row)" :disabled="scope.row.prCheckSta === '1'">{{
            $t('datadown.downPdf')
          }}</a-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <vxe-pager
      :loading="loading"
      :current-page="tablePage.currentPage"
      :page-size="tablePage.pageSize"
      :total="tablePage.total"
      :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
      @page-change="handlePageChange"
    >
    </vxe-pager>
    </div>
  </a-card>
</template>

<script src="http://pv.sohu.com/cityjson?ie=utf-8"></script> 
<script>
import { fetchList, down, isHaveMac } from '@/api/srm/zlxz'
import moment from 'moment'
import axios from 'axios'
export default {
  data() {
    return {
      tz_datadown_search: 'tz_datadown_search',
      tz_datadown_reset: 'tz_datadown_reset',
      startDd: null,
      endDd: null,
      queryParam: {
        downSta: '0',
        startDate: moment().subtract(7, 'd').format('YYYY-MM-DD 00:00:00'),
        endDate: moment(new Date()).format('YYYY-MM-DD 23:59:59'),
        sqNo: '',
        prdNo: '',
      },
      tableData: [],
      tablePage: {
        currentPage: 1,
        pageSize: 100,
        total: 0,
      },
      ipqqqq: '',
      endOpen: false,
      loading: false,
      spanSize: '8',
      spanSizeNo: '6',
      tableHeight: window.innerHeight - 300,
    }
  },
  created() {
    if (window.innerWidth < 1620) {
      this.spanSize = '7'
      this.spanSizeNo = '7'
    }
    this.startDd = moment().subtract(7, 'd')
    this.endDd = moment(new Date())
    this.getList()
  },
  mounted() {
    alert(window.innerWidth)
    window.onresize = () => {
      return (() => {
        this.tableHeight = window.innerHeight - 300
      })()
    }
  },
  computed: {},
  watch: {
    'queryParam.downSta': {
      handler(val, oldval) {
        this.getList()
      },
      deep: true, // 对象内部的属性监听，也叫深度监听
    },
    startDd(val) {
      if (val) {
        this.queryParam.startDate = moment(val).format('YYYY-MM-DD 00:00:00')
      } else {
        this.queryParam.startDate = undefined
      }
    },
    endDd(val) {
      if (val) {
        this.queryParam.endDate = moment(val).format('YYYY-MM-DD 23:59:59')
      } else {
        this.queryParam.endDate = undefined
      }
    },
  },
  mounted() {},
  methods: {
    getList() {
      this.loading = true
      this.tableData = []
      fetchList(
        Object.assign(
          {
            current: this.tablePage.currentPage,
            size: this.tablePage.pageSize,
          },
          this.queryParam
        )
      )
        .then((res) => {
          this.tableData = res.data.records
          this.tablePage.total = res.data.total
          this.tablePage.currentPage = res.data.current
          this.loading = false
        })
        .catch((err) => {
          this.loading = false
          this.tableData = []
          this.requestFailed(err)
        })
    },
    // 分页
    handlePageChange({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getList()
    },
    handleQuery() {
      this.tablePage.currentPage = 1
      this.getList()
    },
    // 下载pdf
    downPdf(row) {
      isHaveMac({
        ip: returnCitySN['cip'],
      })
        .then((res) => {
          // 先判断是否有绑定ip地址
          if (res.data === '0') {
            // 未绑定ip,提示
            const that = this
            this.$confirm({
              title: this.$t('public.del.title'),
              content: this.$t('zlsq.isHaveMac'),
              okText: this.$t('public.sure'),
              okType: 'danger',
              cancelText: this.$t('public.cancel'),
              onOk() {
                that.requestDwnFile(row)
              },
              onCancel() {},
            })
          } else if (res.data === '1') {
            // 直接下载
            this.requestDwnFile(row)
          }
        })
        .catch((err) => {
          this.requestFailed(err)
        })
    },
    requestDwnFile(row) {
      const hide = this.$message.loading(this.$t('public.waitDown'), 0)
      let obj = {
        ip: '',
        ...row,
      }
      obj.ip = returnCitySN['cip']
      down(obj)
        .then((res) => {
          if (res.data) {
            if (res.data.flag) {
              let url = res.data.returnUrl
              this.downFile(url, res.data.fileName, hide)
            } else {
              setTimeout(hide, 5)
              this.$message.error(res.data.message)
            }
          }
        })
        .catch((err) => {
          setTimeout(hide, 5)
          this.requestFailed(err)
        })
    },
    downFile(url, fileName, hide) {
      axios({
        method: 'get',
        url: url,
        responseType: 'blob',
      })
        .then((response) => {
          setTimeout(hide, 5)
          this.tablePage.currentPage = 1
          this.getList()
          this.$message.success(this.$t('public.downSuccess'))
          const blob = new Blob([response])
          const blobUrl = window.URL.createObjectURL(blob)
          this.download(blobUrl, fileName)
        })
        .catch((err) => {
          setTimeout(hide, 5)
          this.loading = false
          this.requestFailed(err)
        })
    },
    download(blobUrl, fileName) {
      let a = document.createElement('a')
      a.style.display = 'none'
      a.download = fileName
      a.href = blobUrl
      a.click()
    },
    disabledStartDate(startValue) {
      const endValue = this.fbEndDd
      if (!startValue || !endValue) {
        return false
      }
      return startValue.valueOf() > endValue.valueOf()
    },
    disabledEndDate(endValue) {
      const startValue = this.fbStartDd
      if (!endValue || !startValue) {
        return false
      }
      return startValue.valueOf() >= endValue.valueOf()
    },
    handleStartOpenChange(open) {
      if (!open) {
        this.endOpen = true
      }
    },
    handleEndOpenChange(open) {
      this.endOpen = open
    },
    handleReset() {
      this.startDd = null
      this.endDd = null
      this.queryParam.downSta = '0'
      this.queryParam.sqNo = ''
      this.queryParam.prdNo = ''
    },
  },
}
</script>

<style>
</style>