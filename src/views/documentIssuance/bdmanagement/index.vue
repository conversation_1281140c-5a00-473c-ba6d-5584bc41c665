<template>
  <a-card :bordered="false">
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row>
          <a-col :span="6">
            <a-form-item :label="$t('datadown.date')">
              <a-date-picker
                v-model="startDd"
                :disabled-date="disabledStartDate"
                format="YYYY-MM-DD"
                :placeholder="$t('datadown.startD')"
                @openChange="handleStartOpenChange"
                style="width: 48%"
              />
              <a-date-picker
                v-model="endDd"
                :disabled-date="disabledEndDate"
                format="YYYY-MM-DD"
                :placeholder="$t('datadown.endD')"
                :open="endOpen"
                @openChange="handleEndOpenChange"
                style="width: 48%; margin-left: 8px"
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item :label="$t('datadown.status')">
              <a-select v-model="queryParam.prCheckSta" style="width: 95%;">
                <a-select-option value="0"> {{ $t('datadown.recovery') }} </a-select-option>
                <a-select-option value="1"> {{ $t('datadown.yrecovery') }} </a-select-option>
                <a-select-option value="2"> {{ $t('datadown.all') }} </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item :label="$t('datadown.sqNo')">
              <a-input v-model="queryParam.sqNo" :placeholder="$t('datadown.sqNo')" style="width: 95%;"/>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item :label="$t('datadown.prdNo')">
              <a-input v-model="queryParam.prdNo" :placeholder="$t('datadown.prdNo')" style="width: 100%;"/>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="6">
            <a-form-item :label="$t('zlsq.fileNo')">
              <a-input v-model="queryParam.fileNo" :placeholder="$t('zlsq.placeholder.fileNo')" style="width: 98%;"/>
            </a-form-item>
          </a-col>
          <a-col :span="12"></a-col>
          <a-col :span="6" style="text-align: right">
            <a-button style="margin-left: 10px" type="primary" @click="haddleQuery" v-permission="tz_bdmanagement_search">{{ $t('public.query') }}</a-button>
            <a-button style="margin-left: 10px" type="primary" @click="handleReset" v-permission="tz_bdmanagement_reset">{{ $t('public.reset') }}</a-button>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <vxe-table
      border
      resizable
      stripe
      highlight-current-row
      show-overflow
      highlight-hover-row
      export-config
      ref="xTable"
      size="mini"
      :max-height="tableHeight"
      :loading="loading"
      :data="tableData"
      :keyboard-config="{ isArrow: true }"
      :edit-config="{ trigger: 'click', mode: 'row' }"
    >
      <vxe-table-column fixed="left" type="checkbox" align="center" width="50"> </vxe-table-column>
      <vxe-table-column field="fileNo" title="void.fileNo" align="center" :width="150"></vxe-table-column>
      <vxe-table-column field="sqNo" title="datadown.sqNo" align="center" :width="150"></vxe-table-column>
      <vxe-table-column field="verNo" title="zlsq.verNo" align="center" :width="150"></vxe-table-column>
      <vxe-table-column field="downNo" title="zlsq.downCount" align="center" :width="150"></vxe-table-column>
      <vxe-table-column field="downloadTime" title="zlsq.downloadTime" align="center" :width="150"></vxe-table-column>
      <vxe-table-column field="prdNo" title="datadown.prdNo" align="center" :width="150"></vxe-table-column>
      <vxe-table-column field="prdName" title="datadown.prdName" align="center" :width="150"></vxe-table-column>
      <vxe-table-column field="gvFang" title="zlsq.gvFang" align="center" :width="150"></vxe-table-column>
      <vxe-table-column field="lvFang" title="zlsq.lvFang" align="center" :width="150"></vxe-table-column>
      <vxe-table-column field="map" title="zlsq.map" align="center" :width="150"></vxe-table-column>
      <vxe-table-column field="zhCount" title="zlsq.zhCount" align="center" :width="150"></vxe-table-column>
      <vxe-table-column field="printId" title="zlsq.printId" align="center" :width="150">
        <template v-slot="scope">
          <a-tag color="blue" type="primary">{{ scope.row.printId === 'Y' ? $t('public.T') : $t('public.F') }}</a-tag>
        </template>
      </vxe-table-column>
      <vxe-table-column field="zhCount" title="zlsq.zhCount" align="center" :width="150"></vxe-table-column>
      <vxe-table-column field="upNotify" title="zlsq.upNotify" align="center" :width="150"></vxe-table-column>
      <vxe-table-column field="fileUpTime" title="zlsq.fileUpTime" align="center" :width="150"></vxe-table-column>
      <vxe-table-column field="status" title="zlsq.status" align="center" :width="150"></vxe-table-column>
      <vxe-table-column field="rem" title="zlsq.r" align="center" :width="150"></vxe-table-column>
    </vxe-table>
    <vxe-pager
      :loading="loading"
      :current-page="tablePage.currentPage"
      :page-size="tablePage.pageSize"
      :total="tablePage.total"
      :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
      @page-change="handlePageChange"
    >
    </vxe-pager>
  </a-card>
</template>

<script>
import { fetchList } from '@/api/srm/zlgl'
import moment from 'moment'
export default {
  data() {
    return {
      tz_bdmanagement_search: 'tz_bdmanagement_search',
      tz_bdmanagement_reset: 'tz_bdmanagement_reset',
      startDd: null,
      endDd: null,
      queryParam: {
        prCheckSta: '2',
        startDate: null,
        endDate: null,
        sqNo: '',
        prdNo: '',
        fileNo: '',
      },
      tableData: [],
      tablePage: {
        currentPage: 1,
        pageSize: 100,
        total: 0,
      },
      endOpen: false,
      loading: false,
      spanSize: '8',
      spanSizeNo: '6',
      tableHeight: window.innerHeight - 350,
    }
  },
  created() {
    if (window.innerWidth < 1620) {
      this.spanSize = '7'
      this.spanSizeNo = '7'
    }
  },
  mounted() {
    let beginTime = moment().month(moment().month()).startOf('month').valueOf()
    let lastTime = moment().month(moment().month()).endOf('month').valueOf()
    this.startDd = moment(beginTime)
    this.endDd = moment(lastTime)
    this.$nextTick(() => {
      this.getList()
    })
    window.onresize = () => {
      return (() => {
        this.tableHeight = window.innerHeight - 350
      })()
    }
  },
  computed: {},
  watch: {
    'queryParam.prCheckSta': {
      handler(val, oldval) {
        this.getList()
      },
      deep: true,
    },
    startDd(val) {
      if (val) {
        this.queryParam.startDate = moment(val).format('YYYY-MM-DD 00:00:00')
      } else {
        this.queryParam.startDate = null
      }
    },
    endDd(val) {
      if (val) {
        this.queryParam.endDate = moment(val).format('YYYY-MM-DD 23:59:59')
      } else {
        this.queryParam.endDate = null
      }
    },
  },
  methods: {
    getList() {
      this.loading = true
      this.tableData = []
      fetchList(
        Object.assign(
          {
            current: this.tablePage.currentPage,
            size: this.tablePage.pageSize,
          },
          this.queryParam
        )
      )
        .then((res) => {
          let data = res.data.records
          data.forEach((i) => {
            if (i.prCheckSta === '0') {
              i.status = '已审核'
            } else if (i.prCheckSta === '1') {
              i.status = '未审核'
            }
          })
          this.tableData = data
          this.tablePage.total = res.data.total
          this.tablePage.currentPage = res.data.current
          this.loading = false
        })
        .catch((err) => {
          this.loading = false
          this.tableData = []
          this.requestFailed(err)
        })
    },
    haddleQuery() {
      this.tablePage.currentPage = 1
      this.getList()
    },
    handlePageChange({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getList()
    },
    disabledStartDate(startValue) {
      const endValue = this.fbEndDd
      if (!startValue || !endValue) {
        return false
      }
      return startValue.valueOf() > endValue.valueOf()
    },
    disabledEndDate(endValue) {
      const startValue = this.fbStartDd
      if (!endValue || !startValue) {
        return false
      }
      return startValue.valueOf() >= endValue.valueOf()
    },
    handleStartOpenChange(open) {
      if (!open) {
        this.endOpen = true
      }
    },
    handleEndOpenChange(open) {
      this.endOpen = open
    },
    handleRecycle(name) {
      switch (name) {
        case 'recycle': {
          const selectRecords = this.$refs.xTable.getCheckboxRecords()
          if (selectRecords.length) {
          } else {
            this.loading = false
            this.$message.warning(this.$t('public.list'))
          }
        }
      }
    },
    handleReset() {
      this.startDd = null
      this.endDd = null
      this.queryParam.prCheckSta = '2'
      this.queryParam.sqNo = ''
      this.queryParam.prdNo = ''
      this.queryParam.fileNo = ''
    },
  },
}
</script>

<style>
</style>