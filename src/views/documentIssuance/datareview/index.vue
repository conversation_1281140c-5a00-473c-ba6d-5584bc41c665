<template>
  <a-card :bordered="false">
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row>
          <a-col :span="6">
            <a-form-item :label="$t('datadown.date')">
              <a-date-picker
                v-model="startDd"
                :disabled-date="disabledStartDate"
                format="YYYY-MM-DD "
                :placeholder="$t('datadown.startD')"
                @openChange="handleStartOpenChange"
                style="width: 48%"
              />
              <a-date-picker
                v-model="endDd"
                :disabled-date="disabledEndDate"
                format="YYYY-MM-DD"
                :placeholder="$t('datadown.endD')"
                :open="endOpen"
                @openChange="handleEndOpenChange"
                style="width: 48%; margin-left: 8px"
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item :label="$t('datadown.status')">
              <a-select v-model="queryParam.prCheckSta" style="width: 95%">
                <a-select-option value="1">
                  {{ $t('datadown.noprCheckSta') }}
                </a-select-option>
                <a-select-option value="0">
                  {{ $t('datadown.prCheckSta') }}
                </a-select-option>
                <a-select-option value="2">
                  {{ $t('datadown.all') }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item :label="$t('datadown.sqNo')">
              <a-input v-model="queryParam.sqNo" :placeholder="$t('datadown.sqNo')" style="width: 95%"/>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item :label="$t('entrustment.supplier')">
              <my-selectList
                url="/srm/cust/page"
                :tableColumn="$Column.srmcus"
                :form="$Form.srmcus"
                :data="data"
                name="cusNo"
                @choose="choose"
                allowClear
                ref="selectList"
                style="width: 100%"
                :placeholder="$t('entrustment.placeholder.supplier')"
              >
              </my-selectList>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :xl="6" :lg="8" :md="12" :sm="24"></a-col>
          <a-col :xl="8" :lg="8" :md="12" :sm="24"> </a-col>
          <a-col :xl="10" :lg="8" :md="12" :sm="24" style="text-align: right">
            <a-button style="margin-left: 10px" type="primary" @click="handleQuery()" v-permission="tz_datareview_search">{{ $t('public.query') }}</a-button>
            <a-button style="margin-left: 10px" type="primary" @click="handReview()" :disabled="isHandReview" v-permission="tz_datareview_toexamine">{{
              $t('datadown.examine')
            }}</a-button>
            <a-button style="margin-left: 10px" type="primary" @click="handReview()" :disabled="isHandNotReview" v-permission="tz_datareview_deaudit">{{
              $t('datadown.NotExamine')
            }}</a-button>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <div style="margin-top: 10px;">
      <!-- <vxe-toolbar custom> </vxe-toolbar> -->
      <vxe-table
        border
        resizable
        stripe
        highlight-current-row
        show-overflow
        highlight-hover-row
        export-config
        ref="xTable"
        size="mini"
        :max-height="tableHeight"
        :loading="loading"
        :data="tableData"
        :keyboard-config="{ isArrow: true }"
        :edit-config="{ trigger: 'click', mode: 'row' }"
      >
        <vxe-table-column fixed="left" type="checkbox" width="50" align="center"> </vxe-table-column>
        <vxe-table-column field="fileNo" title="zlsq.fileNo" align="center"></vxe-table-column>
        <vxe-table-column field="prdNo" title="zlsq.prdNo" align="center"></vxe-table-column>
        <vxe-table-column field="prdName" title="zlsq.prdName" align="center"></vxe-table-column>
        <vxe-table-column field="cusName" title="zlsq.cusName" align="center"></vxe-table-column>
        <vxe-table-column field="gvFang" title="zlsq.gvFang" align="center"></vxe-table-column>
        <vxe-table-column field="lvFang" title="zlsq.lvFang" align="center"></vxe-table-column>
        <vxe-table-column field="verNo" title="zlsq.verNo" align="center"></vxe-table-column>
        <vxe-table-column field="map" title="zlsq.map" align="center"></vxe-table-column>
        <vxe-table-column field="zhCount" title="zlsq.zhCount" align="center"></vxe-table-column>
        <vxe-table-column field="printId" title="zlsq.printId" align="center">
          <template v-slot="scope">
            <a-tag color="blue" type="primary">{{ scope.row.printId === 'Y' ? $t('public.T') : $t('public.F') }}</a-tag>
          </template>
        </vxe-table-column>
        <vxe-table-column field="rem" title="zlsq.r" align="center"></vxe-table-column>
      </vxe-table>
      <vxe-pager
        :loading="loading"
        :current-page="tablePage.currentPage"
        :page-size="tablePage.pageSize"
        :total="tablePage.total"
        :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
        @page-change="handlePageChange"
      >
      </vxe-pager>
    </div>
  </a-card>
</template>

<script>
import { fetchList, reView } from '@/api/srm/zlsh'
import MySelectList from '@/components/MySelectList'
import moment from 'moment'
export default {
  components: {
    MySelectList,
  },
  data() {
    return {
      tz_datareview_search: 'tz_datareview_search',
      tz_datareview_toexamine: 'tz_datareview_toexamine',
      tz_datareview_deaudit: 'tz_datareview_deaudit',
      startDd: null,
      endDd: null,
      data: '',
      queryParam: {
        startDate: moment().subtract(7, 'd').format('YYYY-MM-DD 00:00:00'),
        endDate: moment(new Date()).format('YYYY-MM-DD 23:59:59'),
        prCheckSta: '1',
        sqNo: '',
        cusNo: '',
      },
      tablePage: {
        currentPage: 1,
        pageSize: 100,
        total: 0,
      },
      tableData: [],
      endOpen: false,
      loading: false,
      tableHeight: window.innerHeight - 300,
    }
  },
  created() {
    this.startDd = moment().subtract(7, 'd')
    this.endDd = moment(new Date())
    this.getList()
  },
  mounted() {
    window.onresize = () => {
      return (() => {
        this.tableHeight = window.innerHeight - 300
      })()
    }
  },
  computed: {
    isHandReview() {
      return this.queryParam.prCheckSta === '0'
    },
    isHandNotReview() {
      return this.queryParam.prCheckSta === '1'
    },
  },
  watch: {
    'queryParam.prCheckSta': {
      handler(val, oldval) {
        this.getList()
      },
      deep: true, // 对象内部的属性监听，也叫深度监听
    },
    startDd(val) {
      if (val) {
        this.queryParam.startDate = moment(val).format('YYYY-MM-DD 00:00:00')
      } else {
        this.queryParam.startDate = undefined
      }
    },
    endDd(val) {
      if (val) {
        this.queryParam.endDate = moment(val).format('YYYY-MM-DD 23:59:59')
      } else {
        this.queryParam.endDate = undefined
      }
    },
  },
  methods: {
    getList() {
      this.loading = true
      this.tableData = []
      fetchList(
        Object.assign(
          {
            current: this.tablePage.currentPage,
            size: this.tablePage.pageSize,
          },
          this.queryParam
        )
      )
        .then((res) => {
          this.tableData = res.data.records
          this.tablePage.total = res.data.total
          this.tablePage.currentPage = res.data.current
          this.loading = false
        })
        .catch((err) => {
          this.loading = false
          this.tableData = []
          this.requestFailed(err)
        })
    },
    // 分页
    handlePageChange({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getList()
    },
    handleQuery() {
      this.tablePage.currentPage = 1
      this.getList()
    },
    // 审核
    handReview() {
      const selectRecords = this.$refs.xTable.getCheckboxRecords()
      if (selectRecords.length) {
        const ids = []
        selectRecords.forEach((i) => {
          ids.push(Object.assign({}, { fileNo: i.fileNo, prdNo: i.prdNo, sqNo: i.sqNo, verNo: i.verNo }))
        })
        const that = this
        this.$confirm({
          title: this.$t('public.del.title'),
          content: this.$t('zlsq.pub'),
          okText: this.$t('public.sure'),
          okType: 'danger',
          cancelText: this.$t('public.cancel'),
          onOk() {
            that.loading = true
            reView({
              fileNoList: ids,
              prCheckSta: that.queryParam.prCheckSta,
            })
              .then((res) => {
                that.loading = false
                if (res) {
                  if (res.data == 0) {
                    that.getList()
                    that.$message.success('审核成功！')
                  } else if (res.data == 1) {
                    that.getList()
                    that.$message.success('反审核成功！')
                  } else {
                    that.$message.error('文件已下载不能反审核！')
                  }
                }
              })
              .catch((err) => {
               
                that.loading = false
                that.requestFailed(err)
              })
          },
          onCancel() {
            that.loading = false
          },
        })
      } else {
        this.loading = false
        this.$message.warning(this.$t('public.list'))
      }
    },
    choose(obj, clear) {
      this.data = obj.obj.value
      this.queryParam.cusNo = clear === undefined ? obj.obj.data.cusNo : ''
    },
    disabledStartDate(startValue) {
      const endValue = this.fbEndDd
      if (!startValue || !endValue) {
        return false
      }
      return startValue.valueOf() > endValue.valueOf()
    },
    disabledEndDate(endValue) {
      const startValue = this.fbStartDd
      if (!endValue || !startValue) {
        return false
      }
      return startValue.valueOf() >= endValue.valueOf()
    },
    handleStartOpenChange(open) {
      if (!open) {
        this.endOpen = true
      }
    },
    handleEndOpenChange(open) {
      this.endOpen = open
    },
  },
}
</script>

<style>
</style>