<template>
  <div>
    <a-drawer
      placement="right"
      :closable="false"
      @close="onClose"
      :visible="visible"
      :destroyOnClose="true"
      width="70%"
    >
      <template slot="title">
        <span class="title-name">{{ title }}</span>
      </template>
      <a-form-model layout="horizontal" ref="ruleForm" :model="form">
        <a-row>
          <a-col>
            <a-form-model-item v-bind="formItemLayout" :label="$t('public.numb')" prop="data">
              <my-selectList
                url="/srm/downsetting/page"
                :tableColumn="$Column.srmdownsetting"
                :form="$Form.srmdownsetting"
                :data="data"
                name="cusName"
                @choose="choose($event)"
                ref="selectList"
                :placeholder="$t('public.numb')"
                :disabled="isDisabled"
              ></my-selectList>
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-model-item v-bind="formItemLayout" :label="$t('zlsq.downVoidTime')" prop="name">
              <a-select v-model="form.effTimes" :placeholder="$t('zlsq.downVoidTime')" :disabled="isDisabled">
                <a-select-option value="30">
                  {{ $t('timesz.one') }}
                </a-select-option>
                <a-select-option value="60">
                  {{ $t('timesz.two') }}
                </a-select-option>
                <a-select-option value="90">
                  {{ $t('timesz.three') }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-model-item v-bind="formItemLayout" :label="$t('timesz.filePw')">
              <a-input v-model="form.passDown" :disabled="isDisabled" :placeholder="$t('timesz.filePw')" />
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-model-item v-bind="formItemLayout" :label="$t('zlsq.downCount')">
              <a-input v-model="form.maxC" :disabled="isDisabled" :placeholder="$t('zlsq.downCount')" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
      <a-row :gutter="16">
        <a-col class="gutter-row" :span="12" style="text-align: right">
          <a-button type="primary" :loading="loading" v-if="modeType === '0'" @click="handleOK()">{{
            $t('public.save')
          }}</a-button>
          <a-button type="primary" v-if="modeType === '1'" @click="handleMenuClick()">{{ $t('public.edit') }}</a-button>
          <a-button type="primary" :loading="loading" v-if="modeType === '2'" @click="handleEdit()">{{
            $t('public.save')
          }}</a-button>
        </a-col>
        <a-col class="gutter-row" :span="12" style="text-align: left">
          <a-button @click="onClose">{{ $t('public.cancel') }}</a-button>
        </a-col>
      </a-row>
    </a-drawer>
  </div>
</template>

<script>
import { add, edit } from '@/api/srm/timesz'
import MySelectList from '@/components/MySelectList'
export default {
  components: {
    MySelectList,
  },
  data() {
    return {
      title: '',
      disabled: false,
      visible: false,
      loading: false,
      isDisabled: false,
      modeType: '',
      dep: '',
      data: '',
      row: {},
      form: {
        cusNo: '',
        cusName: '',
        effTimes: '',
        id: undefined,
        passDown: '',
        maxC: '',
      },
      timeArr: [
        {
          name: '一个月',
          value: '30',
        },
        {
          name: '二个月',
          value: '60',
        },
        {
          name: '三个月',
          value: '90',
        },
      ],
      formItemLayout: {
        labelCol: {
          xs: {
            span: 24,
          },
          sm: {
            span: 5,
          },
        },
        wrapperCol: {
          xs: {
            span: 24,
          },
          sm: {
            span: 15,
          },
        },
      },
    }
  },
  created() {},
  computed: {},
  methods: {
    // 取消
    onClose() {
      this.loading = false
      this.visible = false
    },
    create(model, row) {
      this.title = model.title
      this.modeType = '0'
      this.visible = true
      this.isDisabled = false
      this.disabled = true
      this.form = {
        cusNo: '',
        cusName: '',
        effTimes: '30',
        id: undefined,
        passDown: '',
        maxC: '3',
      }
      this.data = ''
    },
    // 点击编辑按钮
    handleMenuClick() {
      this.title = this.$t('public.edit')
      this.isDisabled = false
      this.modeType = '2'
    },
    edit(model, row) {
      this.disabled = true
      this.title = model.title
      this.modeType = '1'
      this.row = row
      this.visible = true
      this.isDisabled = true
      this.form = {
        cusNo: row.cusNo,
        cusName: row.cusName,
        effTimes: row.effTimes,
        id: row.id,
        passDown: row.passDown,
        maxC: row.maxC,
      }
      this.data = row.cusName
    },
    // 添加确认
    handleOK() {
      if (this.data != '') {
        if (this.form.passDown == '' || this.form.passDown == null || this.form.passDown == undefined)
          return this.$notification['warn']({
            message: this.$t('public.message'),
            description: this.$t('timesz.placeholder.filePw'),
          })
        if (this.form.maxC == '' || this.form.maxC == null || this.form.maxC == undefined)
          return this.$notification['warn']({
            message: this.$t('public.message'),
            description: this.$t('timesz.placeholder.maxC'),
          })
        this.loading = true
        add(this.form)
          .then((res) => {
            this.loading = false
            this.onClose()
            this.$emit('getList')
            this.$message.success(this.$t('public.success'))
          })
          .catch((err) => this.requestFailed(err))
          .finally(() => {
            this.loading = false
          })
      } else {
        this.$notification['warn']({
          message: this.$t('public.message'),
          description: this.$t('timesz.placeholder.cusName'),
        })
        return false
      }
    },
    // 确认编辑
    handleEdit() {
      if (this.form.passDown == '' || this.form.passDown == null || this.form.passDown == undefined)
        return this.$notification['warn']({
          message: this.$t('public.message'),
          description: this.$t('timesz.placeholder.filePw'),
        })
      if (this.form.maxC == '' || this.form.maxC == null || this.form.maxC == undefined)
          return this.$notification['warn']({
            message: this.$t('public.message'),
            description: this.$t('timesz.placeholder.maxC'),
        })
      this.loading = true
      edit(this.form)
        .then((res) => {
          this.loading = false
          this.onClose()
          this.$emit('getList')
          this.$message.success(this.$t('public.success'))
        })
        .catch((err) => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    delet() {
      const that = this
      this.$confirm({
        title: this.$t('public.del.title'),
        content: this.$t('creation.delcontent'),
        okText: this.$t('public.sure'),
        okType: 'danger',
        cancelText: this.$t('public.cancel'),
        onOk() {
          that.loading = true
          del({
            id: that.row.id,
            idxNo: that.row.idxNo,
          })
            .then(() => {
              that.loading = false
              that.onClose()
              that.$emit('getList')
              that.$message.success(that.$t('public.success'))
            })
            .catch((err) => that.requestFailed(err))
            .finally(() => {
              that.loading = false
            })
        },
        onCancel() {
          that.loading = false
        },
      })
    },
    choose(obj) {
      this.data = obj.obj.data.cusName
      this.form.cusName = obj.obj.data.cusName
      this.form.cusNo = obj.obj.data.cusNo
    },
  },
}
</script>
