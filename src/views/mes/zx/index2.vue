<template>
  <vTable ref="vTable" v-bind="vTableProps"
  />
</template>

<script>
import vTable from '@/components/amtxts/BaseTable/VGrid/index.vue'

export default {
  name: 'zxindex2',
  components: {
    vTable
  },
  data() {
    return {
      vTableProps:{
        enableDevModel: true,
        // 详情页导航配置
        detailConfig: {
          enabled: true,
          routerName: 'zxset',
        },
        FUNID: "mes.zx2",
        searchConfig:[
          { field: 'prd_no',component: 'quickSelect', aidName: 'mes.prdno'},
          { field: 'create_time',component: 'datePicker',},
          { field: 'update_time',component: 'datePicker',},
        ],
        tableColumn: [
          // { field: "box_no", title: "mesZx.zxCode", },
        ],
      }
    }
  },
  methods: {
    
  },
}

</script>
