/*
 * @Descripttion: 
 * @Author: voanit
 * @Date: 2020-03-13 12:39:56
 * @LastEditors: voanit
 * @LastEditTime: 2020-08-14 10:30:29
 */
// with polyfills
import 'core-js/stable'
import 'regenerator-runtime/runtime'
import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store/'
import axios from '@/router/axios'
import i18n from './locales'
// import VCharts from 'v-charts'
import VueAxios from 'vue-axios'
import "@/assets/icons"; //自定义图标
// mock
//import './mock'
import './utils/def/vxe-renderer'
import Viewer from 'v-viewer'
import 'viewerjs/dist/viewer.css'
import Cookies from 'js-cookie'
Vue.use(Cookies)
    // Vue.use(Element, {
    //   size: Cookies.get('size') || 'small', // set element-ui default size
    //   i18n: (key, value) => i18n.t(key, value)
    // })
Vue.use(Viewer, {
    defaultOptions: {
        zIndex: 9999
    }
})
import bootstrap from './core/bootstrap'
import bus from './utils/bus'
import './core/lazy_use'
import './permission' // permission control
import './utils/filter' // global filter
import './components/global.less'
import '@/theme/index.css'
import 'ant-design-vue/dist/antd.less'

import { Column } from '@/core/List/column'
import { Form } from '@/core/List/form'

// import { i18nRender } from '@/locales'
import Print from 'vue-print-nb'

// dataV
import dataV from '@jiaminghi/data-view'
Vue.use(dataV)

//全局颜色控制
import ColorPlugin from './plugins/colorPlugin';
const colors = {
    primary: '#08B68B',
    secondary: '#08B68B',
};
Vue.use(ColorPlugin, { colors });

//全局引入阿里巴巴矢量图标
import Icon from 'ant-design-vue/es/icon/index'
const MyIcon = Icon.createFromIconfontCN({
    scriptUrl: '//at.alicdn.com/t/c/font_4614888_zwmntcgj07.js' // 在 iconfont.cn 上生成
});
Vue.component('MyIcon', MyIcon);
// 全局element UI
import ElementUI from 'element-ui';
import { Message } from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css';
Vue.use(ElementUI);



// dataV
// 把全局js挂接到vue原型上
Vue.prototype.$Column = Column
Vue.prototype.$Form = Form
Vue.prototype.$bus = bus
Vue.prototype.$setDays = 366

// Vue.prototype.$i18nRender = i18nRender
Vue.use(VueAxios, axios)
    // Vue.use(VCharts)
Vue.use(Print) //注册打印

Vue.prototype.requestFailed = function requestFailed(err) {
    if (err == undefined) {
        this.$notification['error']({
            message: this.$t('public.message'),
            description: '登录失效,请重新手动登录',
            duration: 4
        })
        store.dispatch('Logout').then(() => {
                setTimeout(() => {
                    window.location.reload()
                }, 1500)
            })
            // window.location.reload()
    } else
    if (err) {
        this.$notification['error']({
            message: this.$t('public.message'),
            description: err,
            duration: 4
        })
    }
}
// 封装自定义的 $message 方法
Vue.prototype.$message.success = function (msg, config = {}) {
  return Message({
    offset: 0,
    customClass: 'center-message',
    ...config,
    type: 'success',
    message: msg
  })
}
Vue.prototype.$message.warning = function (msg, config = {}) {
  return Message({
    offset: 0,
    customClass: 'center-message',
    ...config,
    type: 'warning',
    message: msg
  })
}
Vue.prototype.$message.error = function (msg, config = {}) {
  return Message({
    offset: 0,
    customClass: 'center-message',
    ...config,
    type: 'error',
    message: msg
  })
}

Vue.config.productionTip = false
new Vue({
    router,
    store,
    i18n,
    created: bootstrap,
    render: h => h(App)
}).$mount('#app')