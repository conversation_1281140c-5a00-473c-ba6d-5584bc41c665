// 数据转换工具函数
import { isDynamicDateOperator, isStaticDateOperator, getOperatorValue } from '../mixins/operators'

export const SchemeDataUtils = {
  // 将平铺的条件数组（带groupid）转换为按组分组的数据结构
  convertFlatToGrouped(flatConditions) {
    if (!Array.isArray(flatConditions) || flatConditions.length === 0) {
      return []
    }

    // 按 groupid 分组
    const groupMap = new Map()

    flatConditions.forEach(condition => {
      const groupId = condition.groupid || 1 // 默认组ID为1

      if (!groupMap.has(groupId)) {
        groupMap.set(groupId, {
          groupId: groupId,
          ITEMS: []
        })
      }

      // 创建条件副本，移除groupid字段（因为已经通过分组体现）
      const { groupid, ...conditionWithoutGroupId } = condition
      groupMap.get(groupId).ITEMS.push(conditionWithoutGroupId)
    })

    // 按 groupId 排序并返回数组
    return Array.from(groupMap.values()).sort((a, b) => a.groupId - b.groupId)
  },

  // 将分组的数据结构转换回平铺的条件数组（带groupid）
  convertGroupedToFlat(groupedConditions) {
    if (!Array.isArray(groupedConditions) || groupedConditions.length === 0) {
      return []
    }

    const flatConditions = []

    groupedConditions.forEach((group, groupIndex) => {
      const groupId = group.groupId || (groupIndex + 1) // 使用存储的groupId或基于索引生成

      if (group.ITEMS && Array.isArray(group.ITEMS)) {
        group.ITEMS.forEach(condition => {
          // 创建条件副本，添加 groupid 字段
          const flatCondition = {
            ...condition,
            groupid: groupId
          }

          // 动态日期操作符处理：保存时将 value 设为 null
          if (isDynamicDateOperator(flatCondition.operator)) {
            flatCondition.value = null
            console.log(`保存方案时处理动态日期操作符 ${flatCondition.operator}，将 value 设为 null`)
          }

          // 如果没有 typeid，根据字段类型设置默认值
          if (!flatCondition.typeid) {
            // 根据操作符或字段类型推断 typeid
            // 这里可以根据实际业务需求调整逻辑
            if (flatCondition.operator === 'between') {
              flatCondition.typeid = 1 // 日期范围类型
            } else {
              flatCondition.typeid = 2 // 普通条件类型
            }
          }

          // 移除内部使用的字段
          delete flatCondition.component
          delete flatCondition.label
          delete flatCondition._INSERT
          delete flatCondition._DELETE
          delete flatCondition.groupId

          flatConditions.push(flatCondition)
        })
      }
    })

    return flatConditions
  },

  // 处理加载的方案条件，重新计算动态日期操作符的值
  processLoadedConditions(conditions) {
    if (!Array.isArray(conditions) || conditions.length === 0) {
      return conditions
    }

    return conditions.map(condition => {
      // 对动态日期操作符，重新计算当前日期范围
      if (isDynamicDateOperator(condition.operator)) {
        const currentDateRange = getOperatorValue(condition.operator)

        console.log(`加载方案时处理动态日期操作符 ${condition.operator}，重新计算日期范围:`, currentDateRange)

        return {
          ...condition,
          value: currentDateRange
        }
      }

      // 静态日期操作符和其他操作符保持原值
      return condition
    })
  }
}