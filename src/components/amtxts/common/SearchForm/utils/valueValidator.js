/**
 * 查询条件值验证工具函数
 */

/**
 * 判断条件值是否有效
 * @param {*} value - 条件值
 * @param {string} operator - 操作符
 * @returns {boolean} 是否有效
 */
export function isValidConditionValue(value, operator) {
  // 对于某些操作符，不需要输入值
  const noValueOperators = ['null', 'not_null', 'today', 'yesterday', 'thisWeek', 'lastWeek', 'last7Days',
    'thisMonth', 'lastMonth', 'last30Days', 'thisYear', 'lastYear']
  if (noValueOperators.includes(operator)) {
    return true
  }
  
  // null 或 undefined 无效
  if (value === null || value === undefined) {
    return false
  }
  
  // 字符串类型：空字符串无效，但包含空格的字符串有效
  if (typeof value === 'string') {
    return value.trim() !== ''
  }
  
  // 数字类型：0 是有效值，NaN 无效
  if (typeof value === 'number') {
    return !isNaN(value)
  }
  
  // 布尔类型：true 和 false 都有效
  if (typeof value === 'boolean') {
    return true
  }
  
  // 数组类型：非空数组有效（用于日期范围、多选等）
  if (Array.isArray(value)) {
    return value.length > 0 && value.some(item => 
      item !== null && item !== undefined && item !== ''
    )
  }
  
  // 对象类型：非空对象有效
  if (typeof value === 'object') {
    return Object.keys(value).length > 0
  }
  
  // 其他情况按 truthy 判断
  return !!value
}

/**
 * 获取条件值的显示文本
 * @param {*} value - 条件值
 * @param {string} fieldType - 字段类型
 * @returns {string} 显示文本
 */
export function getConditionValueDisplay(value, fieldType) {
  if (value === null || value === undefined) {
    return ''
  }
  
  switch (fieldType) {
    case 'datePicker':
      if (Array.isArray(value) && value.length === 2) {
        return `${value[0]} ~ ${value[1]}`
      }
      return String(value)
      
    case 'number':
      return typeof value === 'number' ? value.toString() : String(value)
      
    case 'select':
    case 'quickSearch':
      if (Array.isArray(value)) {
        return value.join(', ')
      }
      return String(value)
      
    default:
      if (Array.isArray(value)) {
        return value.join(', ')
      }
      return String(value)
  }
}

/**
 * 验证条件完整性
 * @param {Object} condition - 查询条件对象
 * @returns {Object} 验证结果 { valid: boolean, message: string }
 */
export function validateCondition(condition) {
  if (!condition.field) {
    return { valid: false, message: '请选择字段' }
  }
  
  if (!condition.operator) {
    return { valid: false, message: '请选择操作符' }
  }
  
  if (!isValidConditionValue(condition.value, condition.operator)) {
    return { valid: false, message: '请输入有效的条件值' }
  }
  
  return { valid: true, message: '' }
}
