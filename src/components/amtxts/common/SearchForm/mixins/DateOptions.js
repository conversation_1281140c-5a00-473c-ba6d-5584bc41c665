
export const DateOptions = {
  // 构建时间范围
  today(){
    const end = new Date()
    end.setHours(0, 0, 0, 0)
    const start = new Date(end)
    return [start, end]
  },
  yesterday(){
    const end = new Date()
    end.setHours(0, 0, 0, 0)
    const start = new Date()
    start.setTime(end.getTime() - 3600 * 1000 * 24)
    end.setTime(end.getTime() - 3600 * 1000 * 24)
    return [start, end]
  },
  thisWeek(){
    const now = new Date()
    now.setHours(0, 0, 0, 0)
    const day = now.getDay() || 7 // 周日=0，改成7
    const start = new Date()
    start.setTime(now.getTime() - 3600 * 1000 * 24 * (day - 1))
    const end = new Date(now)
    return [start, end]
  },
  lastWeek(){
    const now = new Date()
    now.setHours(0, 0, 0, 0)
    const day = now.getDay() || 7
    const end = new Date()
    end.setTime(now.getTime() - 3600 * 1000 * 24 * day)
    const start = new Date()
    start.setTime(end.getTime() - 3600 * 1000 * 24 * 6)
    return [start, end]
  },
  last7Days(){
    const end = new Date()
    end.setHours(0, 0, 0, 0)
    const start = new Date()
    start.setTime(end.getTime() - 3600 * 1000 * 24 * 6)
    return [start, end]
  },
  thisMonth(){
    const now = new Date()
    now.setHours(0, 0, 0, 0)
    const start = new Date(now.getFullYear(), now.getMonth(), 1)
    const end = new Date(now)
    return [start, end]
  },
  lastMonth(){
    const now = new Date()
    now.setHours(0, 0, 0, 0)
    const start = new Date(now.getFullYear(), now.getMonth() - 1, 1)
    const end = new Date(now.getFullYear(), now.getMonth(), 0) // 上月最后一天
    end.setHours(0, 0, 0, 0)
    return [start, end]
  },
  last30Days(){
    const end = new Date()
    end.setHours(0, 0, 0, 0)
    const start = new Date()
    start.setTime(end.getTime() - 3600 * 1000 * 24 * 29)
    return [start, end]
  },
  thisYear(){
    const now = new Date()
    now.setHours(0, 0, 0, 0)
    const start = new Date(now.getFullYear(), 0, 1) // 今年1月1日
    const end = new Date(now)
    return [start, end]
  },
  lastYear(){
    const now = new Date()
    now.setHours(0, 0, 0, 0)
    const start = new Date(now.getFullYear() - 1, 0, 1) // 去年1月1日
    const end = new Date(now.getFullYear(), 0, 0) // 去年12月31日
    end.setHours(0, 0, 0, 0)
    return [start, end]
  },
  shortcuts: [
    {
      text: '今天',
      onClick(picker) {
        picker.$emit('pick', DateOptions.today())
      }
    },
    {
      text: '昨天',
      onClick(picker) {
        picker.$emit('pick', DateOptions.yesterday())
      }
    },
    {
      text: '本周',
      onClick(picker) {
        picker.$emit('pick', DateOptions.thisWeek())
      }
    },
    {
      text: '上周',
      onClick(picker) {
        picker.$emit('pick', DateOptions.lastWeek())
      }
    },
    {
      text: '近7天',
      onClick(picker) {
        picker.$emit('pick', DateOptions.last7Days())
      }
    },
    {
      text: '本月',
      onClick(picker) {
        picker.$emit('pick', DateOptions.thisMonth())
      }
    },
    {
      text: '上月',
      onClick(picker) {
        picker.$emit('pick', DateOptions.lastMonth())
      }
    },
    {
      text: '近30天',
      onClick(picker) {
        picker.$emit('pick', DateOptions.last30Days())
      }
    },
    {
      text: '今年',
      onClick(picker) {
        picker.$emit('pick', DateOptions.thisYear())
      }
    },
    {
      text: '去年',
      onClick(picker) {
        picker.$emit('pick', DateOptions.lastYear())
      }
    }


  ]

}