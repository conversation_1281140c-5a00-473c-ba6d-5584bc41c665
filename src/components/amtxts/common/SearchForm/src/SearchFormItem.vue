<template>
  <el-col :span="8" class="search-form-item-col">
    <div class="search-form-item">
      <!-- 字段选择器 -->
      <div class="field-selector-wrapper">
        <FieldSelector
          v-model="condition.field"
          :fields="fieldConfig"
          @field-change="handleFieldChange"
        />
      </div>

      <!-- 操作符选择器 -->
      <div class="operator-selector-wrapper">
        <OperatorSelector
          v-model="condition.operator"
          :field-type="currentFieldConfig.component"
          @operator-change="handleOperatorChange"
        />
      </div>

      <!-- 值输入组件 -->
      <div class="value-input-wrapper">
        <ValueInput
          :value="condition.value"
          :field-config="currentFieldConfig"
          :operator="condition.operator"
          :operator-config="currentOperatorConfig"
          @input="handleValueChange"
          @value-change="handleValueChange"
          @selectListEvent="handleSelectListEvent"
          @iconClick="handleIconClick"
        />
      </div>

      <!-- 删除按钮 -->
      <div class="delete-button-wrapper">
        <el-button
          v-if="showDeleteButton"
          circle
          type="danger"
          size="mini"
          icon="el-icon-delete"
          @click="handleDelete"
        />
      </div>
    </div>
  </el-col>
</template>

<script>
import FieldSelector from './FieldSelector.vue'
import OperatorSelector from './OperatorSelector.vue'
import ValueInput from './ValueInput.vue'
import { getOperatorsByType } from '../mixins/operators'

export default {
  name: 'SearchFormItem',
  components: {
    FieldSelector,
    OperatorSelector,
    ValueInput
  },
  props: {
    condition: {
      type: Object,
      required: true
    },
    fieldConfig: {
      type: Array,
      required: true
    },
    index: {
      type: Number,
      required: true
    },
    showDeleteButton: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    currentFieldConfig() {
      if (!this.condition.field) {
        return { component: 'input' }
      }
      return this.fieldConfig.find(f => f.field === this.condition.field) || { component: 'input' }
    },
    
    currentOperatorConfig() {
      if (!this.condition.operator || !this.currentFieldConfig.component) {
        return {}
      }
      const operators = getOperatorsByType(this.currentFieldConfig.component)
      return operators.find(op => op.value === this.condition.operator) || {}
    }
  },
  methods: {
    handleFieldChange(fieldConfig) {
      if (!fieldConfig) {
        this.updateCondition({
          field: '',
          operator: '',
          value: null,
          component: 'input',
        })
        return
      }

      // 字段变更时重置操作符和值
      const operators = getOperatorsByType(fieldConfig.component)
      const defaultOperator = operators.length > 0 ? operators[0].value : null
      
      this.updateCondition({
        field: fieldConfig.field,
        operator: defaultOperator,
        value: null,
        component: fieldConfig.component,
      })
    },
    
    handleOperatorChange(operatorData) {
      if(!operatorData.value) return
        this.updateCondition({
          operator: operatorData.value,
          value: operatorData.config.disableInput ? null : this.condition.value
        })
    },
    
    handleValueChange(value) {
      this.updateCondition({
        value: value
      })
    },
    
    
    handleSelectListEvent(eventData) {
      // 传递快捷查询事件到父组件
      this.$emit('selectListEvent', {
        ...eventData,
        index: this.index,
        condition: this.condition
      })
    },
    
    handleIconClick(field) {
      // 传递图标点击事件到父组件
      this.$emit('iconClick', {
        field,
        index: this.index,
        condition: this.condition
      })
    },
    
    handleDelete() {
      this.$emit('delete', this.index)
    },
    
    updateCondition(updates) {
      const newCondition = {
        ...this.condition,
        ...updates
      }
      this.$emit('condition-change', this.index, newCondition)
    },
  },
  
  watch: {
    
  }
}
</script>

<style scoped>
.el-col-8 {
  min-width: 500px;
}
.search-form-item {
  display: flex;
  align-items: flex-start;
  gap: 6px;
  border-radius: 4px;
  transition: all 0.3s;
  margin-bottom: 3px;
}

.field-selector-wrapper {
  flex: 0 0 130px;
}

.operator-selector-wrapper {
  flex: 0 0 100px;
}

.value-input-wrapper {
  flex: 1;
  min-width: 200px;
}

.delete-button-wrapper {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  width: 28px;
}

.delete-button {
  color: white;
  padding: 4px;
}

.delete-button:hover {
  background-color: #fef0f0;
  color: #f56c6c;
}

</style>

