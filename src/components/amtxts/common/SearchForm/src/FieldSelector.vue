<template>
  <el-select
    v-model="selectedField"
    placeholder="选择字段"
    size="small"
    clearable
    filterable
    :disabled="disabled"
    class="field-selector"
    @change="handleFieldChange"
  >
    <el-option
      v-for="field in fields"
      :key="field.field"
      :label="$t(field.rem_gb || field.title)"
      :value="field.field"
    >
      <span class="field-option">
        <span class="field-label">{{ $t(field.rem_gb || field.title) }}</span>
      </span>
    </el-option>
  </el-select>
</template>

<script>
export default {
  name: 'FieldSelector',
  props: {
    value: {
      type: String,
      default: ''
    },
    fields: {
      type: Array,
      required: true
    },
    disabled: {
      type: <PERSON>olean,
      default: false
    }
  },
  computed: {
    selectedField: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  methods: {
    handleFieldChange(fieldValue) {
      const fieldConfig = this.fields.find(f => f.field === fieldValue)
      this.$emit('field-change', fieldConfig)
    },
    
    getFieldTypeText(component) {
      const typeMap = {
        text: '文本',
        quickSelect: '快捷',
        select: '选择',
        number: '数值',
        datePicker: '日期'
      }
      return typeMap[component] || '文本'
    },
  }
}
</script>

<style scoped>
.field-selector {
  width: 130px;
}

.field-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.field-label {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

::v-deep .el-select-dropdown__item {
  height: auto;
  line-height: 1.5;
  padding: 8px 20px;
}
</style> 