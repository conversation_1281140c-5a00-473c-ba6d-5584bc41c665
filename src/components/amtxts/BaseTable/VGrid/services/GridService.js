import { BaseService } from '../../BaseService/index'

export class GridService extends BaseService {
  constructor(context) {
    super(context)
  }

  // 构建表格列配置
  buildTableColumns() {
    const columns = []

    // 获取选择列配置
    const firstColumn = this.context.tableColumn?.find(column => column.type === 'radio' || column.type === 'checkbox')
    const selectType = firstColumn?.type || (this.context.multiple ? 'checkbox' : 'radio')

    // 添加选择列
    if (this.context.showCheckbox) {
      columns.push({
        type: selectType,
        width: 60,
        align: 'center',
        fixed: 'left'
      })
    }

    // 添加序号列
    if (this.context.showSeq) {
      columns.push({
        type: 'seq',
        width: 60,
        align: 'center',
        fixed: 'left'
      })
    }

    // 构建业务列：优先使用接口数据，tableColumn作为扩展
    const businessColumns = this.buildBusinessColumns()

    return [...columns, ...businessColumns]
  }

  // 构建业务列配置
  buildBusinessColumns() {
    const columns = []
    if (this.context.columnContent && this.context.columnContent.length > 0) {
      // 基于接口数据构建列
      const apiColumns = this.context.columnContent
        .filter(column => column.is_show === 'T') // 只显示is_show为T的列
        .map(column => {
          // 基础列配置
          const columnConfig = {
            field: column.field,
            title: column.rem_gb || column.title, // 别名优先title
            minWidth: this.context.columnMinWidth || '120px',
            sortable: this.context.sortable,
            filters: [],
            filterMultiple: true,
            filterMethod: ({ option, row, column }) => {
              const cellValue = row[column.field]
              if (Array.isArray(option.data)) {
                return option.data.includes(cellValue)
              }
              return cellValue === option.value
            }
          }

          // 如果size有值，设置列宽度
          if (column.size && column.size !== '0') {
            columnConfig.width = parseInt(column.size)
          }

          // 查找tableColumn中的扩展配置
          const extensionConfig = this.findTableColumnExtension(column.field)
          if (extensionConfig) {
            // 合并扩展配置
            Object.assign(columnConfig, extensionConfig)
          }

          return columnConfig
        })

      columns.push(...apiColumns)
    }
    return columns
  }

  // 查找tableColumn中的扩展配置
  findTableColumnExtension(field) {
    if (!this.context.tableColumn) return null

    return this.context.tableColumn.find(column =>
      column.field === field
    )
  }

  // 初始化表格配置
  initGridOptions() {
    console.log('initGridOptions')
    this.context.gridOptions = {
      border: true,
      stripe: true,
      resizable: true,
      showOverflow: true,
      showHeaderOverflow: true,
      height: '100%',
      loading: false,
      keepSource: this.context.edit,
      columns: this.buildTableColumns(),
      data: [],
      scrollY: { enabled: true },
      checkboxConfig: {
        trigger: 'row',
        highlight: true,
        range: true,
        isShiftKey: true
      },
      sortConfig: {
        multiple: true, // 支持多列排序
        remote: false,  // 本地排序
      },
      filterConfig: {
        remote: false,
        showIcon: true,
        iconNone: 'vxe-icon-funnel',
        iconMatch: 'vxe-icon-funnel'
      },
      radioConfig: {
        strict: false,
        trigger: 'row',
        highlight: true,
        range: true,
        isShiftKey: true
      },
      pagerConfig: {
        layouts: ['Total', 'Sizes', 'PrevPage', 'JumpNumber', 'NextPage', 'FullJump'],
        total: 0,
        currentPage: 1,
        pageSize: 500,
        pageSizes: [500, 1000, 2000, 5000, 10000]
      },
      editConfig: {
        trigger: 'dblclick',
        mode: 'cell',
        showIcon: false,
        enabled: this.context.edit,
        showStatus: true
      },
    }
  }

  // 解析排序内容（支持字符串和数组格式）
  parseSortContent(sortContent) {
    if (!sortContent) {
      return []
    }

    // 如果是数组格式（来自 SchemeDialog）
    if (Array.isArray(sortContent)) {
      return sortContent
        .filter(item => item.field && item.sort)
        .map(item => ({
          field: item.field,
          order: item.sort.toLowerCase() === 'desc' ? 'desc' : 'asc'
        }))
    }

    // 如果是字符串格式（来自 API 或 viewDataMap）
    if (typeof sortContent === 'string') {
      return sortContent
        .split(',')
        .map(item => item.trim())
        .filter(item => item)
        .map(item => {
          const parts = item.split(/\s+/)
          const field = parts[0]
          const order = parts[1] ? parts[1].toLowerCase() : 'asc'
          return { field, order: order === 'desc' ? 'desc' : 'asc' }
        })
        .filter(item => item.field)
    }

    return []
  }

  // 应用排序
  applySorting() {
    if (!this.context.sortContent || !this.context.$refs.gridRef) {
      return
    }

    const sortRules = this.parseSortContent(this.context.sortContent)

    if (sortRules.length > 0) {
      // 清除现有排序
      this.context.$refs.gridRef.clearSort()

      // 应用新的排序规则
      sortRules.forEach(rule => {
        this.context.$refs.gridRef.sort(rule.field, rule.order)
      })

      console.log('应用排序规则:', sortRules)
    }
  }

  // 更新表格列配置
  updateGridColumns() {
    if (this.context.gridOptions) {
      this.context.gridOptions.columns = this.buildTableColumns()
    }
  }
} 