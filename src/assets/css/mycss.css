
::v-deep .el-loading-spinner {
width: 38px;
height: 38px;
/* background-position:center; */
/*覆盖 element-ui 默认的 50% 因为此处设置了height:100%,所以不设置的话，会只显示一半，因为被top顶下去了*/
/* top:40%; */
left: 50%;
transform: translate(-50%, -50%);
}
::v-deep .el-loading-spinner:before,
::v-deep .el-loading-spinner:after {
  content: "";
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: #409eff;
  opacity: 0.6;
  position: absolute;
  top: 0;
  left: 0;
  animation: bounce 2.0s infinite ease-in-out;
}
::v-deep .el-loading-spinner:after {
animation-delay: -1.0s;
}
@keyframes bounce {
0%, 100% {
  transform: scale(0);
} 50% {
  transform: scale(1);
}
}
::v-deep .el-loading-spinner .circular {
  /*隐藏 之前 element-ui 默认的 loading 动画*/
  display: none;
  }