// eslint-disable-next-line
import { BasicLayout, UserLayout } from '@/layouts'
import { bxAnaalyse } from '@/core/icons'

/**
 * 静态路由 不受角色权限控制的路由
 * 前端路由在这里添加
 */
export const staticRoutes = [
  {
    path: '/dashboard/workplace',
    name: 'dashboard',
    component: () => import('@/views/dashboard/index'),
    meta: { title: 'menu.workplace', keepAlive: true, icon: bxAnaalyse, isAffix: true }
  },
  {
    path: '/menu/menu',
    name: 'menu',
    isAffix: true,
    component: () => import('@/views/menu/index'),
    meta: { title: 'menu.menu', keepAlive: true, icon: bxAnaalyse, isAffix: true }
  },
  {
    path: '/center/personalCenter',
    name: 'personalCenter',
    hidden: true,
    component: () => import('@/views/dashboard/center/personalCenter'),
    meta: { title: '个人中心', keepAlive: true, icon: 'user' }
  },
];


/**
 * 基础路由
 * @type { *[] }
 */

export const baseRoutes = [
  {
    path: '/login',
    component: UserLayout,
    redirect: '/login/login',
    hidden: true,
    children: [
      {
        path: 'login',
        name: 'login',
        component: () => import('@/views/login/Login'),
        meta: { title: '登录' }
      },
      {
        path: 'recover',
        name: 'recover',
        component: undefined
      }
    ]
  },
  {
    path: '/404',
    component: () => import(/* webpackChunkName: "fail" */ '@/views/exception/404')
  },
  {
    path: '/exception/403',
    name: 'Exception403',
    component: () => import(/* webpackChunkName: "fail" */ '@/views/exception/403'),
    meta: { title: '403' }
  },
  {
    path: '/exception/404',
    name: 'Exception404',
    component: () => import(/* webpackChunkName: "fail" */ '@/views/exception/404'),
    meta: { title: '404' }
  },
  {
    path: '/exception/500',
    name: 'Exception500',
    component: () => import(/* webpackChunkName: "fail" */ '@/views/exception/500'),
    meta: { title: '500' }
  },
  {
    path: '/iframePage',
    name: 'iframePage',
    component: () => import('@/views/iframe/fromErp.vue'),
    meta: { title: '签入页' }
  },
]


/**
 * 动态路由 @backEnd.js维护
 * 禁止在根路由下增加子路由
 */
export const dynamicRoutes = [
  {
    path: '/',
    name: 'index',
    component: BasicLayout,
    meta: { title: 'menu.index' },
    redirect: '/menu/menu',
    children:[]
  },

  {
    path: '*',
    redirect: '/404',
    hidden: true
  }
]